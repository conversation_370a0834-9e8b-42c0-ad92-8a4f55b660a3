#!/usr/bin/env bash
# flush_queue.sh – delete detection_layer_stream_queue only when it grows > 5

set -euo pipefail

PASSWORD="squirrel_sentinel_2025"   # Redis AUTH password
SERVICE="redis"                     # docker-compose service name
KEY="detection_layer_stream_queue"
INTERVAL=2                          # seconds between checks
THRESHOLD=5                         # flush when LLEN > THRESHOLD

while true; do
  # Get queue length (trim trailing CR/LF)
  LEN=$(sudo docker compose exec "$SERVICE" \
          redis-cli -a "$PASSWORD" LLEN "$KEY" | tr -d '\r')

  # Redis prints "(integer) N" if run on its own; inside bash capture we get just N.
  if [[ "$LEN" =~ ^[0-9]+$ && "$LEN" -gt "$THRESHOLD" ]]; then
    sudo docker compose exec "$SERVICE" \
         redis-cli -a "$PASSWORD" DEL "$KEY" >/dev/null
    echo "$(date +'%F %T')  Flushed $KEY (length was $LEN)"
  fi

  sleep "$INTERVAL"
done