FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies required for psutil and NVIDIA monitoring
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    # libnvidia-ml1 \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables for NVIDIA
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=all

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the monitoring script
COPY main.py .

# Create logs directory with proper permissions
RUN mkdir -p /app/logs && chmod 777 -R /app/logs

# Run the monitoring script
CMD ["python", "main.py"]