import psutil, time, datetime, json, atexit, threading
import GPUtil
from collections import deque
import logging
from logging.handlers import RotatingFileHandler
import os
from pathlib import Path

# Create logs directory if it doesn't exist
Path("./logs").mkdir(exist_ok=True)

# Configure logging to output to both file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        RotatingFileHandler(
            './logs/monitoring.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        ),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

LOG_FILE = "./logs/metrics.jsonl"
BUFFER = deque(maxlen=120)  # holds 10 minutes of data at 5s intervals

def sample_metrics():
    logger.info("\n=== System Metrics Sampling ===")
    net1 = psutil.net_io_counters()
    disk1 = psutil.disk_io_counters()
    time.sleep(1)
    net2 = psutil.net_io_counters()
    disk2 = psutil.disk_io_counters()

    # Initialize GPU info with None values
    gpu_info = {
        "util": None,
        "mem_used_percent": None,
        "error": None
    }

    # Try to get GPU metrics
    try:
        gpus = GPUtil.getGPUs()
        if gpus:
            gpu = gpus[0]  # Get first GPU
            gpu_info.update({
                "util": gpu.load * 100,
                "mem_used_percent": (gpu.memoryUsed / gpu.memoryTotal * 100),
                "error": None
            })
            logger.info(f"GPU Utilization: {gpu_info['util']:.1f}%")
            logger.info(f"GPU Memory Used: {gpu.memoryUsed}/{gpu.memoryTotal}MB ({gpu_info['mem_used_percent']:.1f}%)")
        else:
            logger.warning("No GPUs detected")
            gpu_info["error"] = "No GPUs detected"
    except Exception as e:
        logger.warning(f"Failed to get GPU metrics: {str(e)}")
        gpu_info["error"] = str(e)

    metrics = {
        "timestamp": datetime.datetime.utcnow().isoformat(),
        "cpu": psutil.cpu_percent(),
        "ram": psutil.virtual_memory().percent,
        "swap": psutil.swap_memory().percent,
        "disk_read_MBps": (disk2.read_bytes - disk1.read_bytes) / 1024 / 1024,
        "disk_write_MBps": (disk2.write_bytes - disk1.write_bytes) / 1024 / 1024,
        "net_sent_MBps": (net2.bytes_sent - net1.bytes_sent) / 1024 / 1024,
        "net_recv_MBps": (net2.bytes_recv - net1.bytes_recv) / 1024 / 1024,
        "gpu": gpu_info,
    }

    logger.info(f"CPU Usage: {metrics['cpu']:.1f}%")
    logger.info(f"RAM Usage: {metrics['ram']:.1f}%")
    logger.info(f"Disk Read: {metrics['disk_read_MBps']:.2f} MB/s")
    logger.info(f"Disk Write: {metrics['disk_write_MBps']:.2f} MB/s")
    logger.info(f"Network Send: {metrics['net_sent_MBps']:.2f} MB/s")
    logger.info(f"Network Receive: {metrics['net_recv_MBps']:.2f} MB/s")
    logger.info("================================\n")

    return metrics

def monitor(interval=10):
    logger.info(f"Starting system monitoring (sampling every {interval} seconds)")
    while True:
        try:
            data = sample_metrics()
            BUFFER.append(data)
            time.sleep(interval - 1)  # minus the 1s used for net/disk deltas
        except Exception as e:
            logger.error(f"Error in monitoring: {str(e)}", exc_info=True)

def flush_to_disk():
    logger.info("Flushing metrics to disk...")
    try:
        with open(LOG_FILE, "a") as f:
            records = 0
            while BUFFER:
                f.write(json.dumps(BUFFER.popleft()) + "\n")
                records += 1
        logger.info(f"Flushed {records} records to {LOG_FILE}")
    except Exception as e:
        logger.error(f"Error flushing to disk: {str(e)}", exc_info=True)

atexit.register(flush_to_disk)

def start_monitor():
    thread = threading.Thread(target=monitor, kwargs={"interval": 10}, daemon=True)
    thread.start()
    logger.info("Monitoring thread started")

if __name__ == "__main__":
    logger.info("Starting monitoring service")
    start_monitor()
    try:
        while True:
            time.sleep(60)
            flush_to_disk()
    except KeyboardInterrupt:
        logger.info("Shutting down monitoring service")
        flush_to_disk()