# --- reusable block ----------------------------------------------------
x-model-limits: &model-limits
  deploy:
    resources:
      limits:
        cpus: "2.0"      # hard ceiling
        memory: 2g
      reservations:
        cpus: "1.0"      # guaranteed share
        memory: 1g

services:
  postgres:
    image: postgres:15
    expose:
      - "5432" # Map external 5433 to internal 5432 for external access
    volumes:
      - ./database:/var/lib/postgresql/data
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U squirrelsentry_user -d squirrelsentry_db"]
      interval: 2s
      timeout: 2s
      retries: 3
    
  redis:
    image: squirrelsentry-redis
    ports:
      - "6379:6379"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "yes" ]
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 24g

  streaming_redis:
    image: squirrelsentry-streaming_redis
    ports:
      - "6380:6379"
    networks:
      sentry-network:
        ipv4_address: ***********
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "no" ]
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 24g

  alerts_redis:
    image: squirrelsentry-alerts_redis
    ports:
      - "6381:6379"
    networks:
      sentry-network:
        ipv4_address: ***********
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "no" ]
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 24g

  # rtsp:
  #   image: squirrelsentry-rtsp
  #   restart: unless-stopped
  #   ports:
  #     - "8554:8554"
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********

  temporal_control:
    image: squirrelsentry-temporal_control
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./configuration:/app/configuration
      - ./debug_temporal_control:/app/debug
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true

  analytics_camera_one:
    image: squirrelsentry-analytics_camera_one
    environment:
      - ENV_FILE=./env/cam_one.env
    networks:
      sentry-network:
        ipv4_address: **********0
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_two:
    image: squirrelsentry-analytics_camera_two
    environment:
      - ENV_FILE=./env/cam_two.env
    networks:
      sentry-network:
        ipv4_address: **********1
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_three:
    image: squirrelsentry-analytics_camera_three
    environment:
      - ENV_FILE=./env/cam_three.env
    networks:
      sentry-network:
        ipv4_address: **********2
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_four:
    image: squirrelsentry-analytics_camera_four
    environment:
      - ENV_FILE=./env/cam_four.env
    networks:
      sentry-network:
        ipv4_address: **********3
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_five:
    image: squirrelsentry-analytics_camera_five
    environment:
      - ENV_FILE=./env/cam_five.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on: 
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_six:
    image: squirrelsentry-analytics_camera_six
    environment:
      - ENV_FILE=./env/cam_six.env
    networks:
      sentry-network:
        ipv4_address: **********5
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_seven:
    image: squirrelsentry-analytics_camera_seven
    environment:
      - ENV_FILE=./env/cam_seven.env
    networks:
      sentry-network:
        ipv4_address: **********6
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_eight:
    image: squirrelsentry-analytics_camera_eight
    environment:
      - ENV_FILE=./env/cam_eight.env
    networks:
      sentry-network:
        ipv4_address: **********7
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_nine:
    image: squirrelsentry-analytics_camera_nine
    environment:
      - ENV_FILE=./env/cam_nine.env
    networks:
      sentry-network:
        ipv4_address: **********8
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_ten:
    image: squirrelsentry-analytics_camera_ten
    environment:
      - ENV_FILE=./env/cam_ten.env
    networks:
      sentry-network:
        ipv4_address: **********9
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_eleven:
    image: squirrelsentry-analytics_camera_eleven
    environment:
      - ENV_FILE=./env/cam_eleven.env
    networks:
      sentry-network:
        ipv4_address: **********0
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_twelve:
    image: squirrelsentry-analytics_camera_twelve
    environment:
      - ENV_FILE=./env/cam_twelve.env
    networks:
      sentry-network:
        ipv4_address: **********1
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  analytics_camera_thirteen:
    image: squirrelsentry-analytics_camera_thirteen
    environment:
      - ENV_FILE=./env/cam_thirteen.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
      - /dev/shm/frame:/frame
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g

  # analytics_camera_fourteen:
  #   image: squirrelsentry-analytics_camera_fourteen
  #   environment:
  #     - ENV_FILE=./env/cam_fourteen.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped



  model_services_one:
    <<: *model-limits
    image: squirrelsentry-model_services_one
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4020:4020"
    environment:
      - PORT_NUMBER=4020
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_two:
    <<: *model-limits
    image: squirrelsentry-model_services_two
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4021:4021"
    environment:
      - PORT_NUMBER=4021
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
  
  model_services_three:
    <<: *model-limits
    image: squirrelsentry-model_services_three
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4022:4022"
    environment:
      - PORT_NUMBER=4022
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
  
  model_services_four:
    <<: *model-limits
    image: squirrelsentry-model_services_four
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4023:4023"
    environment:
      - PORT_NUMBER=4023
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_five:
    <<: *model-limits
    image: squirrelsentry-model_services_five
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4024:4024"
    environment:
      - PORT_NUMBER=4024
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_six:
    <<: *model-limits
    image: squirrelsentry-model_services_six
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4025:4025"
    environment:
      - PORT_NUMBER=4025
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
  
  model_services_seven:
    <<: *model-limits
    image: squirrelsentry-model_services_seven
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4026:4026"
    environment:
      - PORT_NUMBER=4026
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_eight:
    <<: *model-limits
    image: squirrelsentry-model_services_eight
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
      - /dev/shm/frame:/frame
    ports:
      - "4027:4027"
    environment:
      - PORT_NUMBER=4027
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_one:
    image: squirrelsentry-detection_layer_one
    container_name: squirrelsentry-detection_layer_one
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two:
    image: squirrelsentry-detection_layer_two
    container_name: squirrelsentry-detection_layer_two
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_three:
    image: squirrelsentry-detection_layer_three
    container_name: squirrelsentry-detection_layer_three
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_four:
    image: squirrelsentry-detection_layer_four
    container_name: squirrelsentry-detection_layer_four
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two_one:
    image: squirrelsentry-detection_layer_two_one
    container_name: squirrelsentry-detection_layer_two_one
    volumes:
      - ./keys:/keys
      - ./output_logs_detection_layer_two:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    
  # batch_processor:
  #   image: squirrelsentry-batch-processor
  #   environment:
  #     - BATCH_SIZE=20
  #     - SLEEP_INTERVAL=1
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   depends_on:
  #     - postgres
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped

  backend:
    image: squirrelsentry-backend
    working_dir: /usr/src/backend
    expose:
      - "4000"
    command: >
      sh -c "sh ./start_gunicorn_async.sh"
    healthcheck:
      test: ["CMD-SHELL", "curl -f $BACKEND_DOMAIN:4000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./keys:/usr/src/backend/keys
      - ./output_logs_backend:/usr/src/backend/output_logs
      - ./videos:/usr/src/backend/videos
      - ./fixtures:/usr/src/backend/cameras/fixtures
      - /dev/shm/frame:/frame
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      - streaming_redis
      - alerts_redis

  nginx:
    image: squirrelsentry-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8000:8000"
      - "8001:8001"
      - "4010:4010"
      - "5432:5432"
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./certs:/etc/letsencrypt
      - ./certbot-www:/var/www/certbot
    depends_on:
      - backend
      - frontend
      - model_services_one
      - model_services_two
      - model_services_three
      - model_services_four
      # - model_services_five
      # - model_services_six
      # - model_services_seven
      # - model_services_eight

  certbot:
    image: certbot/certbot
    container_name: squirrelsentry-certbot
    volumes:
      - ./certs:/etc/letsencrypt
      - ./certbot-www:/var/www/certbot
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
    depends_on:
      - nginx
    entrypoint: ""
    restart: unless-stopped
    command: >
      sh -c "sleep infinity"
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - path: .env

  frontend:
    image: squirrelsentry-frontend
    working_dir: /app
    ports:
      - "3000:3000"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    env_file:
      - .env
    command: >
      sh -c "npm run start -- -p 3000"
  
  usage_monitor:
    image: squirrelsentry-usage_monitor
    networks:
      sentry-network:
        ipv4_address: ***********
    restart: unless-stopped
    volumes:
      - ./monitoring/logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

networks:
  sentry-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16