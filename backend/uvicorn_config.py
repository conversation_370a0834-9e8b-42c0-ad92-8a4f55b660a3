"""
Uvicorn configuration for optimized RTSP streaming in SquirrelSentry
"""

# Worker configurations
workers = 4  # Set to number of CPU cores for optimal performance
worker_class = "uvicorn.workers.UvicornWorker"

# Logging configurations
loglevel = "info"
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log errors to stdout

# Timeout configurations optimized for streaming
timeout = 300  # 5 minutes
keepalive = 65  # Keep connections alive

# Buffer sizes optimized for video streaming
buffer_size = 65536  # 64KB buffer size

# Uvicorn specific settings
http = "httptools"
loop = "uvloop"
proxy_headers = True
forwarded_allow_ips = "*"

# Production settings
reload = False  # Set to False in production
