# Django Backend Documentation

## Project Overview

This is a Django-based backend service that provides authentication and user management functionality. It uses session-based authentication with cookie support and integrates with Google OAuth for user authentication.

## Prerequisites

- Python 3.11
- PostgreSQL (for production)
- SQLite (for local development)

## Setup Instructions

### 1. Create Virtual Environment

```bash
# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
# On Linux/Mac:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
# Django Settings
SECRET_KEY=your_django_secret_key
DEBUG=True
ENVIRONMENT=Development  # Development/Production/Staging/Test
LOCAL=True  # True for SQLite, False for PostgreSQL
SECURE=False  # Set to True in production for SSL

# Database Settings (Required if LOCAL=False)
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_HOST=your_postgres_host

# URLs (Required in Production/Staging)
HTTPS_URL_1=your_domain.com
HTTPS_URL_2=www.your_domain.com
HTTPS_APP_URL_1=https://your_domain.com
HTTPS_APP_URL_2=https://www.your_domain.com

# Google OAuth Settings
GOOGLE_OAUTH_SCOPES=openid,https://www.googleapis.com/auth/userinfo.email,https://www.googleapis.com/auth/userinfo.profile
GOOGLE_OAUTH_CLIENT_ID=your_google_oauth_client_id
GOOGLE_OAUTH_CLIENT_SECRET=your_google_oauth_client_secret
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:8000/api/oauth/google/oauth2callback
JAVASCRIPT_ORIGIN=http://localhost:3000

# Cookie Settings
FRONTEND_COOKIE_SUBDOMAIN=localhost  # Use .example.com in production
SESSION_EXPIRY_DAYS=7
SESSION_COOKIE_NAME=session_id

# Encryption
FERNET_KEY=your_fernet_key
```

### 3. Generate Security Keys

#### Django Secret Key

Generate a new Django secret key using Python:

```python
from django.core.management.utils import get_random_secret_key
print(get_random_secret_key())
```

#### Fernet Key

Generate a Fernet key using the following script:

```python
from cryptography.fernet import Fernet
import base64, hashlib

def gen_fernet_key(passcode:bytes) -> bytes:
    assert isinstance(passcode, bytes)
    hlib = hashlib.md5()
    hlib.update(passcode)
    return base64.urlsafe_b64encode(hlib.hexdigest().encode('latin-1'))

# Generate a key using a secure passcode
passcode = '249524.405925.606329'  # Change this to your secure passcode
key = gen_fernet_key(passcode.encode('utf-8'))
print(key)
```

### 4. Database Setup

```bash
# Create database migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate
```

### 5. Running the Server

#### Local Development

```bash
# Run Django development server
python manage.py runserver
```

#### Production

```bash
# Run with Gunicorn
gunicorn backend.wsgi:application --bind 0.0.0.0:8000 --workers 3
```

## Development Guide

### Creating a New App

```bash
# Create a new Django app
python manage.py startapp app_name

# Add the app to INSTALLED_APPS in settings.py
```

### Database Management

```bash
# Create migrations for model changes
python manage.py makemigrations app_name

# Apply migrations
python manage.py migrate

# Show migration status
python manage.py showmigrations
```

### Project Structure

```
backend/
├── backend/          # Main project settings
├── logs/             # Application logs
├── oauth_provider/   # OAuth authentication
├── user/             # User management
├── utils/            # Utility functions
└── manage.py         # Django management script
```

## Security Considerations

1. **Environment Variables**
   - Never commit `.env` file
   - Use strong, unique keys
   - Rotate secrets regularly

2. **Production Settings**
   - Enable `SECURE=True`
   - Use HTTPS
   - Set proper `ALLOWED_HOSTS`
   - Configure cookie domains

3. **Database**
   - Use strong PostgreSQL passwords
   - Regular backups
   - Limited access

## Deployment Checklist

1. Update environment variables
2. Set `DEBUG=False`
3. Configure PostgreSQL
4. Set up Gunicorn
5. Configure HTTPS
6. Set proper cookie domain
7. Update OAuth redirect URIs

## Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Google OAuth Setup](https://console.developers.google.com/)

## Troubleshooting

1. **Database Issues**
   - Check PostgreSQL connection
   - Verify migrations
   - Check logs in `logs/` directory

2. **OAuth Issues**
   - Verify client ID and secret
   - Check redirect URI configuration
   - Ensure scopes are correct

3. **Session Issues**
   - Check cookie domain settings
   - Verify session configuration
   - Check browser cookie settings