from django.db import models
from django.conf import settings
import uuid
import logging

logger = logging.getLogger(__name__)

class Camera(models.Model):
    """Model for managing security cameras with RTSP streams"""
    # Camera identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    location = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    # Connection details
    rtsp_url = models.Char<PERSON>ield(max_length=500, help_text="RTSP URL for the camera stream")
    
    # Stream properties
    status = models.CharField(max_length=20, choices=[
        ('online', 'Online'),
        ('offline', 'Offline'),
        ('error', 'Error'),
    ], default='offline')
    last_status_check = models.DateTimeField(auto_now=True)
    
    # Streaming settings
    stream_fps = models.FloatField(default=1.0, 
                            help_text="Frames per second to capture from RTSP stream")
    stream_id = models.Char<PERSON>ield(max_length=100, blank=True, null=True,
                                help_text="ID for this stream in the RTSP processor")
    # stream_url field has been removed - we use get_stream_url() method instead
    # to dynamically generate the URL based on camera ID
    encoding_format = models.CharField(max_length=10, default='H.264', 
                                    choices=[
                                        ('H.264', 'H.264'),
                                        ('H.265', 'H.265'),
                                        ('jpg', 'JPEG'),  # Keep for backward compatibility
                                        ('png', 'PNG'),   # Keep for backward compatibility
                                        ('webp', 'WebP'), # Keep for backward compatibility
                                    ],
                                    help_text="Video encoding format")
    dynamic_frame_rate = models.BooleanField(default=True,
                                  help_text="Dynamically adjust frame rate based on activity")
    
    required_analytics = models.BooleanField(default=False,
                                  help_text="If True, this camera requires analytics to be run")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.location})"
    
    def get_full_rtsp_url(self):
        """Return the full RTSP URL with authentication from environment variables"""
        import os
        
        # If URL already has credentials, return it as is
        if '@' in self.rtsp_url:
            return self.rtsp_url
            
        # Get credentials from environment variables
        rtsp_username = os.environ.get('RTSP_USERNAME')
        rtsp_password = os.environ.get('RTSP_PASSWORD')
        
        if rtsp_username and rtsp_password:
            # Insert authentication into the URL
            # Typical format: rtsp://username:password@camera-ip:port/stream
            url_parts = self.rtsp_url.split('://')
            if len(url_parts) == 2:
                protocol = url_parts[0]
                address = url_parts[1]
                return f"{protocol}://{rtsp_username}:{rtsp_password}@{address}"
                
        return self.rtsp_url
    
    def get_stream_id(self):
        """Return the stream ID for this camera"""
        if not self.stream_id:
            # If not explicitly set, use the camera ID
            return f"camera_{str(self.id)}"
        return self.stream_id
    
    def get_stream_url(self):
        """Return the MJPEG stream URL for web viewing"""
        # Return the Django-proxied URL instead of direct RTSP server URL
        # Include the /api/ prefix to match how URLs are included in the main project
        return f"/api/cameras/{self.id}/stream/"
        
    def get_direct_stream_url(self):
        """Return the direct RTSP server URL (for internal use)"""
        return f"http://{settings.RTSP_SERVER_HOST}:{settings.RTSP_SERVER_PORT}/streams/{self.get_stream_id()}/mjpeg"
    
    def get_snapshot_url(self):
        """Return the URL for the latest snapshot from this camera"""
        # Return Django-proxied URL for snapshots
        return f"/api/cameras/snapshot/{self.id}/"
    
class CamerasLayersConfiguration(models.Model):
    """Model for storing the configuration of cameras and layers"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='layers_configuration')
    layers = models.ForeignKey("Layer", on_delete=models.CASCADE, related_name='layers_configuration')
    enabled = models.BooleanField(default=True)
    color = models.CharField(max_length=7, default='#777777')
    # Replace the single ForeignKey with a ManyToManyField
    regions = models.ManyToManyField("RegionOfInterest", related_name='layers_configurations', blank=True)
    dependencies = models.ManyToManyField(
        'self',
        through='CamerasLayersConfigurationDependency',
        through_fields=('current_layer', 'dependency_layer'),
        symmetrical=False,
        related_name='dependents'
    )
    has_processing_logic = models.BooleanField(default=False, help_text="If True, this layer has processing logic")
    # Metadata
    configuration = models.JSONField(help_text="Configuration Dictionary")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} - {self.camera.name}"

class CamerasLayersConfigurationDependency(models.Model):
    """
    Through-table tying one CamerasLayersConfiguration (the "current" layer)
    to another (the "dependency" layer), *with* per-dependency conditions.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # the layer that depends on something else:
    current_layer = models.ForeignKey(
        CamerasLayersConfiguration,
        on_delete=models.CASCADE,
        related_name='dependency_links'
    )
    # the layer that must run first:
    dependency_layer = models.ForeignKey(
        CamerasLayersConfiguration,
        on_delete=models.CASCADE,
        related_name='dependent_links'
    )

    # any per-dependency conditions
    conditions = models.JSONField(help_text="Conditions necessary for the current layer to be run")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.current_layer.camera.name}: “{self.current_layer.name}” ⟶ “{self.dependency_layer.name}”"

class RegionOfInterest(models.Model):
    """Model for storing detection zones on cameras"""
    # Available keypoints for pose detection
    KEYPOINT_CHOICES = [
        ('Nose', 'Nose'),
        ('Left Eye', 'Left Eye'),
        ('Right Eye', 'Right Eye'),
        ('Left Ear', 'Left Ear'),
        ('Right Ear', 'Right Ear'),
        ('Left Shoulder', 'Left Shoulder'),
        ('Right Shoulder', 'Right Shoulder'),
        ('Left Elbow', 'Left Elbow'),
        ('Right Elbow', 'Right Elbow'),
        ('Left Wrist', 'Left Wrist'),
        ('Right Wrist', 'Right Wrist'),
        ('Left Hip', 'Left Hip'),
        ('Right Hip', 'Right Hip'),
        ('Left Knee', 'Left Knee'),
        ('Right Knee', 'Right Knee'),
        ('Left Ankle', 'Left Ankle'),
        ('Right Ankle', 'Right Ankle'),
    ]
    
    # Requirements for triggering alerts
    REQUIREMENT_CHOICES = [
        ('above the line', 'Above the line'),
        ('below the line', 'Below the line'),
        ('inside the region', 'Inside the region'),
        ('outside the region', 'Outside the region'),
    ]
    
    # Alert categories
    ALERT_CATEGORY_CHOICES = [
        ('Warning', 'Warning'),
        ('Critical', 'Critical'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    
    # Store coordinates as JSON array of points
    coordinates = models.JSONField(help_text="Array of points defining the region, has both x and y which define the absolute coordinates and normalizedX/Y which defines the relative coordiantes per image frame")
    
    # Type of detection (line or polygon)
    roi_type = models.CharField(max_length=20, choices=[
        ('line', 'Line Detection'),
        ('region', 'Region Detection')
    ])
    
    # Detection settings
    is_active = models.BooleanField(default=True)

    # Alert category (Warning or Critical)
    alerts_category = models.CharField(
        max_length=255,
        choices=ALERT_CATEGORY_CHOICES,
        db_index=True,
        null=True,
        blank=True,
        help_text="Category of alerts to be triggered for this region"
    )
    
    # Structured criteria fields
    pose_keypoints = models.JSONField(
        help_text="List of keypoints used for pose detection criteria",
        null=True,
        blank=True,
        validators=[]
    )
    
    requirement = models.CharField(
        max_length=50,
        choices=REQUIREMENT_CHOICES,
        null=True,
        blank=True,
        help_text="Requirement for triggering alerts (e.g., above the line, inside the region)"
    )
    
    # Keep the original criteria field for backward compatibility
    # but use the structured fields above for new code
    criteria = models.JSONField(
        help_text="Legacy field for criteria. Still used, save method in this class automatically populates this field with correctly formatted requirements and keypoints.",
        null=True,
        blank=True
    )
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                  null=True, related_name='created_regions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def save(self, *args, **kwargs):
        # Ensure criteria is synchronized with structured fields
        if self.pose_keypoints is not None or self.requirement is not None:
            self.criteria = {
                'pose_keypoints': self.pose_keypoints,
                'requirements': self.requirement
            }
        super().save(*args, **kwargs)
        
    def clean(self):
        # Validate that keypoints are from the allowed list
        if self.pose_keypoints:
            valid_keypoints = [choice[0] for choice in self.KEYPOINT_CHOICES]
            for keypoint in self.pose_keypoints:
                if keypoint not in valid_keypoints:
                    raise ValidationError(f"Invalid keypoint: {keypoint}")
                    
        # Validate that requirements match region type
        if self.requirement and self.roi_type:
            if self.roi_type == 'line' and self.requirement not in ['above the line', 'below the line']:
                raise ValidationError(f"For line regions, requirement must be 'above the line' or 'below the line'")
            if self.roi_type == 'region' and self.requirement not in ['inside the region', 'outside the region']:
                raise ValidationError(f"For polygon regions, requirement must be 'inside the region' or 'outside the region'")
                
    def __str__(self):
        return f"{self.name} ({self.roi_type}) on {self.camera.name}"


class ComputerVisionModel(models.Model):
    """Model for representing different computer vision models used in the system"""
    MODEL_TYPES = [
        ('yolov8-seg', 'YOLOv8 Segmentation'),
        ('yolov8-pose', 'YOLOv8 Pose'),
        ('yolov10m', 'YOLOv10 Medium')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    model_type = models.CharField(max_length=50, choices=MODEL_TYPES)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"CV Model: {self.get_model_type_display()}"


class Layer(models.Model):
    """Model for detection layers with specific detection rules"""
    # New layer numeric types
    LAYER_NUMERIC_TYPES = [
        (1, 'Layer 1'),
        (2, 'Layer 2'),
        (3, 'Layer 3')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    
    # New numeric layer type
    layer_type = models.IntegerField(choices=LAYER_NUMERIC_TYPES, default=1, 
                                  help_text="Numeric layer type (1, 2, 3)")
    
    # Computer vision model reference
    cv_model = models.ForeignKey(ComputerVisionModel, on_delete=models.SET_NULL, 
                                null=True, blank=True, related_name='layers')
    
    function_name = models.CharField(max_length=100, null=True, blank=True, 
                                  help_text="Name of the function to execute for this layer")

    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, 
                                  null=True, related_name='created_layers')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} (Layer {self.layer_type})"
        
    def get_dependencies_list(self):
        """Return a list of all dependencies"""
        return list(self.dependencies.all())
    
    def add_dependency(self, layer):
        """Add a dependency to this layer"""
        if layer.id != self.id:  # Prevent self-dependency
            self.dependencies.add(layer)
            return True
        return False
        
    def execute(self, frame_data=None, dependency_results=None):
        """
        Execute the layer's function on the provided frame data
        
        Args:
            frame_data: Raw frame data to process (optional)
            dependency_results: Results from dependency layers (optional)
            
        Returns:
            dict: Results of the layer processing
        """
        # Get the appropriate function for this layer
        if self.function_name:
            # Import the function map to check if the function exists
            from .layer_functions.core_functions import FUNCTION_MAP
            if self.function_name in FUNCTION_MAP:
                # Use the specified function name
                logger.info(f"Using specified function {self.function_name} for layer {self.id}")
                # Register this custom function if needed
                register_custom_layer_function(self.id, self.function_name)
        
        # Get the function based on layer type from registry
        func = get_layer_function(self)
        if not func:
            logger.error(f"No function found for layer {self.id} (type {self.layer_type})")
            return {"error": "No processing function defined for this layer"}
                
        # Before executing, check dependencies if this is a higher-level layer (type 2 or 3)
        if self.layer_type > 1 and not dependency_results:
            dependency_results = {}
            for dep in self.dependencies.all():
                # We don't actually execute dependencies here, just log that they should be executed first
                logger.warning(f"Layer {self.id} depends on {dep.id} which has not been executed")
                
        # Get the actual function to call
        func = get_layer_function(self)
        if not func:
            return {"error": "Function not found"}
            
        try:
            # Execute the function with appropriate arguments
            if self.layer_type == 1:
                # Layer 1 just needs frame data
                return func(self, frame_data)
            elif self.layer_type == 2:
                # Layer 2 may also need frame data 
                return func(self, frame_data)
            elif self.layer_type == 3:
                # Layer 3 uses results from dependencies
                return func(self, dependency_results)
            else:
                return {"error": f"Unknown layer type: {self.layer_type}"}
        except Exception as e:
            logger.error(f"Error executing function for layer {self.id}: {e}")
            return {"error": str(e)}

class Frame(models.Model):
    """Model for storing camera frames with timestamps"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='frames')
    timestamp = models.DateTimeField(db_index=True, help_text="Timestamp when the frame was captured, timestamp matches event timestamp")
    frame_bytes = models.BinaryField(null=True, blank=True)
    
    frame_number = models.BigIntegerField(null=True, blank=True, help_text="Sequential frame number from the stream")
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)
    format = models.CharField(max_length=20, null=True, blank=True, help_text="Image format (JPEG, PNG, etc.)")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['camera', 'timestamp']),
        ]
        get_latest_by = 'timestamp'
    
    def __str__(self):
        return f"Frame from {self.camera.name} at {self.timestamp}"


class CameraEvent(models.Model):
    """Model for storing events detected on cameras"""
    CARDINAL_EVENT_TYPES = [
        ('suspicious_person', 'Suspicious Person'),
        ('offensive_weapon', 'Offensive Weapon'),
        ('oversized_object', 'Person Carrying Oversize Object'),
        ('unattended_object', 'Unattended Object Detection'),
        ('scaling_gantry', 'Scaling Gantry'),
        ('custom', 'Custom Detection Rule'),
    ]

    EVENT_SEVERITY = [
        ('Critical', 'Critical - High Security Threat'),
        ('Warning', 'Warning - Potential Security Concern')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    camera_layer_config = models.ForeignKey(CamerasLayersConfiguration, on_delete=models.SET_NULL,
                            null=True, blank=True, related_name='events')
    
    event_type = models.CharField(max_length=50, choices=CARDINAL_EVENT_TYPES, null=True, blank=True)

    timestamp = models.DateTimeField(auto_now_add=True, db_index=True, help_text="Timestamp when the event was detected, matches frame timestamp")
    
    frame = models.ForeignKey(Frame, on_delete=models.SET_NULL, null=True, blank=True, related_name='events')

    confidence = models.FloatField(default=0.0, help_text="Confidence score of the detection (0-1)")
    
    bounding_boxes = models.JSONField(blank=True, null=True, 
                              help_text="Bounding boxes of the detected objects")

    event_severity = models.CharField(max_length=20, choices=EVENT_SEVERITY, default='Warning',
                              help_text="Severity level of the security event")
    
    is_reviewed = models.BooleanField(default=False)
    is_suspicious = models.BooleanField(default=False)
    reviewed_by = models.ForeignKey(settings.AUTH_USER_MODEL, null=True, blank=True, 
                                   on_delete=models.SET_NULL, related_name='reviewed_events')
    review_timestamp = models.DateTimeField(null=True, blank=True)
    review_notes = models.TextField(blank=True, null=True)

    # For logging
    camera_id = models.CharField(max_length=255, null=True, blank=True)
    frame_timestamp = models.DateTimeField(null=True, blank=True)
    preprocessing_timestamp = models.DateTimeField(null=True, blank=True)
    detection_timestamp = models.DateTimeField(null=True, blank=True)
    alert_received_timestamp = models.DateTimeField(null=True, blank=True)
    socc_acknowledged_timestamp = models.DateTimeField(null=True, blank=True)
    socc_notification_to_tso_timestamp = models.DateTimeField(null=True, blank=True)
    tso_acknowledged_timestamp = models.DateTimeField(null=True, blank=True)

    # Frontend 2D Map Overlay
    homography_x_coord = models.FloatField(null=True, blank=True)
    homography_y_coord = models.FloatField(null=True, blank=True)
    

    class Meta:
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.get_event_type_display()} on {self.camera_layer_config.camera.name} at {self.timestamp}"


class PermanentCameraOverlay(models.Model):
    """Model for storing permanent overlays on camera streams (directions, arrows, text)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    
    # Link to camera
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='overlays')
    
    # Store coordinates as JSON array of points (similar to RegionOfInterest)
    coordinates = models.JSONField(
        help_text="Array of points defining the overlay, has both x and y which define the absolute coordinates "
                "and normalizedX/Y which defines the relative coordinates per image frame"
    )
    
    # Text to display (if applicable)
    text = models.CharField(max_length=255, blank=True, null=True, 
                           help_text="Text to display in the overlay (if applicable)")
    
    # Styling options
    color = models.CharField(max_length=20, default='#FF0000',
                           help_text="Color of the overlay (hex code or color name)")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} overlay on {self.camera.name}"

