import json
import logging
import redis
import threading
import time
from backend.settings import REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD
from cameras.models import Camera<PERSON>vent
from cameras.serializers import Camera<PERSON>ventSerializer
from cameras.services.voiceping_notifier import VoicePingNotifier
import uuid

# UUID JSON encoder class to handle UUID serialization
class UUIDEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            # Return a string representation of the UUID
            return str(obj)
        return json.JSONEncoder.default(self, obj)

logger = logging.getLogger(__name__)

class EventStreamManager:
    """
    Simplified event streaming system with two primary functions:
    1. Listen for new events from external systems via Redis
    2. Broadcast events to frontend clients via SSE
    
    The database is the single source of truth for event state.
    Redis is used only for real-time notification delivery.
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(EventStreamManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
            
    def __init__(self):
        if self._initialized:
            return
            
        # Get Redis connection parameters from settings
        self.host = REDIS_HOST
        self.port = REDIS_PORT
        self.db = REDIS_DB
        self.password = REDIS_PASSWORD
        
        # Redis key for the existing Redis list
        self.queue_key = 'processed_events_queue'  # The existing Redis list that external systems push to
        
        # Set of SSE clients with their queues
        self._clients_lock = threading.Lock()
        self._registered_clients = set()
        
        # Connect to Redis
        logger.info(f"Connecting to Redis at {self.host}:{self.port}")
        try:
            self._redis = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password
            )
            self._redis.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._redis = None
            
        # Setup thread control
        self._running = False
        self._thread = None
        self._initialized = True
        
        # Initialize VoicePing notifier
        self._voiceping_notifier = VoicePingNotifier()
        
    def start(self):
        """Start the Redis list listener in a background thread"""
        if self._running:
            logger.warning("Event listener is already running")
            return
            
        if not self._redis:
            logger.error("Cannot start listener without Redis connection")
            return
            
        logger.info("Starting event listener")
        self._running = True
        self._thread = threading.Thread(target=self._listen_loop)
        self._thread.daemon = True
        self._thread.start()
        
    def stop(self):
        """Stop the event listener"""
        logger.info("Stopping event listener")
        self._running = False
        if self._thread:
            self._thread.join(timeout=5.0)
            self._thread = None
    
    def _listen_loop(self):
        """Background thread to listen for events on Redis list"""
        logger.info("Event listener started")
        
        while self._running:
            try:
                # Block for a short time to avoid CPU spinning
                event_data = self._redis.blpop(self.queue_key, timeout=1)
                if event_data:
                    # blpop returns (key, value) tuple
                    _, event_json = event_data
                    
                    try:
                        # Parse JSON data
                        event_data = json.loads(event_json)
                        self._process_event(event_data)
                    except json.JSONDecodeError:
                        logger.error(f"Failed to parse event JSON: {event_json}")
                        continue
            except Exception as e:
                logger.error(f"Error in event listener loop: {e}")
                if self._running:
                    # Sleep briefly to avoid tight loop on error
                    time.sleep(1)
        
        logger.info("Event listener stopped")
    
    def _process_event(self, event_data):
        """
        Process an event from the Redis list queue and broadcast to clients.
        
        Args:
            event_data: JSON-parsed event data from the Redis list
        """
        if not event_data or not isinstance(event_data, dict):
            logger.warning(f"Received invalid event data: {event_data}")
            return
            
        logger.debug(f"Processing event: {event_data.get('id', 'unknown')}")
        
        # Ensure the event has necessary fields
        if 'id' not in event_data:
            logger.warning("Event missing required 'id' field")
            return
        
        # Check if the event is already reviewed - if so, don't broadcast it
        if event_data.get('is_reviewed', False):
            logger.debug(f"Skipping already reviewed event: {event_data.get('id')}")
            return
            
        # Broadcast to SSE clients directly
        new_event_msg = {
            'type': 'new_event',
            'data': event_data
        }
        
        json_msg = json.dumps(new_event_msg, cls=UUIDEncoder)
        self._broadcast_to_clients(json_msg)
        
        # Send notification via VoicePing if applicable
        try:
            notification_sent = self._voiceping_notifier.notify_event(event_data)
            if notification_sent:
                logger.info(f"VoicePing notification sent for event {event_data.get('id')}")
        except Exception as e:
            # Ensure VoicePing errors don't disrupt the event streaming
            logger.exception(f"Error sending VoicePing notification: {e}")
        
        logger.debug(f"Event {event_data.get('id')} broadcast to clients")

    def get_recent_events(self, limit=50):
        """
        Get recent unreviewed events directly from the database.
        Used for new client connections to get the current state.
        
        Args:
            limit: Maximum number of events to return
            
        Returns:
            list: Recent unreviewed events from the database
        """
        try:
            # Query for unreviewed events directly from the database
            unreviewed_events = CameraEvent.objects.filter(
                is_reviewed=False
            ).order_by('-timestamp')[:limit]
            
            # Serialize events
            serializer = CameraEventSerializer(unreviewed_events, many=True)
            return serializer.data
            
        except Exception as e:
            logger.error(f"Error fetching recent events from database: {e}")
            return []
    
    def broadcast_event_acknowledged(self, event_id):
        """
        Broadcast an event acknowledgment to all clients.
        This should be called when an event is marked as reviewed or deleted.
        
        Args:
            event_id: The ID of the acknowledged event
            
        Returns:
            bool: True if the acknowledgment was broadcast, False otherwise
        """
        if not event_id:
            logger.warning("Cannot broadcast acknowledgment for empty event ID")
            return False
        
        # Convert to string if needed
        event_id = str(event_id)
        
        logger.info(f"Broadcasting acknowledgment for event {event_id}")
        
        # Broadcast to all clients
        ack_message = json.dumps({
            'type': 'event_acknowledged',
            'data': {'id': event_id}
        }, cls=UUIDEncoder)
        
        self._broadcast_to_clients(ack_message)
        logger.info(f"Event acknowledgment broadcast for {event_id}")
        
        return True
    
    def register_client(self, client_queue):
        """Register a client queue to receive events"""
        with self._clients_lock:
            self._registered_clients.add(client_queue)
            logger.info(f"Client registered, total clients: {len(self._registered_clients)}")
    
    def unregister_client(self, client_queue):
        """Unregister a client queue"""
        with self._clients_lock:
            if client_queue in self._registered_clients:
                self._registered_clients.remove(client_queue)
                logger.info(f"Client unregistered, remaining clients: {len(self._registered_clients)}")
    
    def _broadcast_to_clients(self, message):
        """Send a message to all connected SSE clients"""
        # Format message according to SSE protocol: data: <json>
        # Each message must end with two newlines
        sse_message = f"data: {message}\n\n"
        
        with self._clients_lock:
            disconnected_clients = set()
            for client_queue in self._registered_clients:
                try:
                    client_queue.put(sse_message)
                except Exception as e:
                    logger.error(f"Error sending to client: {e}")
                    disconnected_clients.add(client_queue)
                    
            # Clean up disconnected clients
            for client in disconnected_clients:
                self._registered_clients.remove(client)
                
            logger.debug(f"Broadcast message to {len(self._registered_clients)} clients")
    
    def publish_event(self, event_data):
        """
        Publish an event to the Redis list
        This is used for testing or manual event generation
        """
        if not self._redis:
            logger.error("No Redis connection available")
            return None
            
        try:
            # Convert to JSON string
            json_data = json.dumps(event_data, cls=UUIDEncoder)
            
            # Add to Redis list
            self._redis.rpush(self.queue_key, json_data)
            logger.info(f"Published event to Redis list: {event_data.get('id')}")
            return event_data.get('id')
        except Exception as e:
            logger.error(f"Error publishing to Redis list: {e}")
            return None


# Singleton instance
_stream_manager = None

def get_stream_manager():
    """Get or initialize the event stream manager singleton"""
    global _stream_manager
    if _stream_manager is None:
        _stream_manager = EventStreamManager()
    return _stream_manager

def start_event_stream():
    """Start the event stream manager if it's not already running"""
    manager = get_stream_manager()
    manager.start()
    return manager
    
def stop_event_stream():
    """Stop the event stream manager if it's running"""
    global _stream_manager
    if _stream_manager:
        _stream_manager.stop()
        _stream_manager = None