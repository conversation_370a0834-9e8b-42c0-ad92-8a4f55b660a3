from rest_framework import serializers
from django.contrib.auth import get_user_model
from cameras.models import CameraEvent, Camera, Layer, CamerasLayersConfiguration, Frame

# Get the user model
User = get_user_model()


class CameraSerializer(serializers.ModelSerializer):
    """Serializer for Camera objects in events"""
    class Meta:
        model = Camera
        fields = ['id', 'name', 'location']
        read_only_fields = ['id']


class LayerSerializer(serializers.ModelSerializer):
    """Serializer for Layer objects in events"""
    class Meta:
        model = Layer
        fields = ['id', 'name', 'layer_type']
        read_only_fields = ['id']


class FrameSerializer(serializers.ModelSerializer):
    """Serializer for Frame objects"""
    # Convert binary frame_bytes to base64 string for JSON serialization
    frame_bytes = serializers.SerializerMethodField()
    
    class Meta:
        model = Frame
        fields = ['id', 'timestamp', 'frame_number', 'frame_bytes', 'width', 'height', 'format']
        read_only_fields = ['id']
        
    def get_frame_bytes(self, obj):
        """Convert binary frame_bytes to base64 string if available"""
        import base64
        if obj.frame_bytes:
            return base64.b64encode(obj.frame_bytes).decode('utf-8')
        return None


class ConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for CamerasLayersConfiguration objects"""
    camera = CameraSerializer(read_only=True)
    layers = LayerSerializer(read_only=True)
    
    class Meta:
        model = CamerasLayersConfiguration
        fields = ['id', 'name', 'camera', 'layers', 'color']
        read_only_fields = ['id']


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for User objects in events"""
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name']
        read_only_fields = ['id']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Add a computed 'name' field that combines first and last name
        representation['name'] = f"{instance.first_name} {instance.last_name}".strip() or instance.username
        return representation


class CameraEventSerializer(serializers.ModelSerializer):
    """List serializer for CameraEvent objects"""
    camera_layer_config = serializers.PrimaryKeyRelatedField(queryset=CamerasLayersConfiguration.objects.all())
    camera_name = serializers.SerializerMethodField()
    camera_location = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    
    class Meta:
        model = CameraEvent
        fields = [
            'id', 
            'event_type', 
            'timestamp', 
            'confidence', 
            'event_severity',
            'is_reviewed',
            'is_suspicious',
            'camera_layer_config',
            'camera_name',
            'camera_location',
            'camera_id',
            'frame_timestamp', 
            'preprocessing_timestamp',
            'detection_timestamp',
            'alert_received_timestamp',
            'socc_acknowledged_timestamp',
            'socc_notification_to_tso_timestamp',
            'tso_acknowledged_timestamp',
            'thumbnail',
            'homography_x_coord',
            'homography_y_coord'
        ]
        read_only_fields = ['id', 'camera_name', 'camera_location', 'thumbnail']
    
    def get_camera_name(self, obj):
        """Get the camera name from the related camera_layer_config"""
        if obj.camera_layer_config and obj.camera_layer_config.camera:
            return obj.camera_layer_config.camera.name
        return 'Unknown Camera'
    
    def get_camera_location(self, obj):
        """Get the camera location from the related camera_layer_config"""
        if obj.camera_layer_config and obj.camera_layer_config.camera:
            return obj.camera_layer_config.camera.location
        return 'Unknown Location'
    
    def get_thumbnail(self, obj):
        """Get base64 encoded thumbnail from the associated frame"""
        import base64
        if obj.frame and obj.frame.frame_bytes:
            return base64.b64encode(obj.frame.frame_bytes).decode('utf-8')
        return None


class CameraEventDetailSerializer(serializers.ModelSerializer):
    """Detail serializer for CameraEvent objects"""
    camera_layer_config = ConfigurationSerializer(read_only=True)
    reviewed_by = UserBasicSerializer(read_only=True)
    frame = FrameSerializer(read_only=True)
    
    class Meta:
        model = CameraEvent
        fields = [
            'id', 
            'event_type', 
            'timestamp', 
            'confidence', 
            'event_severity',
            'bounding_boxes',
            'is_reviewed',
            'is_suspicious',
            'review_timestamp',
            'review_notes',
            'reviewed_by',
            'camera_layer_config',
            'frame',
            'camera_id',
            'frame_timestamp',
            'preprocessing_timestamp',
            'detection_timestamp',
            'alert_received_timestamp',
            'socc_acknowledged_timestamp',
            'socc_notification_to_tso_timestamp',
            'tso_acknowledged_timestamp',
            'homography_x_coord',
            'homography_y_coord'
        ]
        read_only_fields = ['id', 'timestamp', 'reviewed_by', 'review_timestamp']
    
    def validate(self, data):
        """Validate that event data is consistent"""
        # If event is marked as reviewed, ensure we have a reviewer
        if data.get('is_reviewed', False) and not self.instance.reviewed_by:
            raise serializers.ValidationError(
                "Reviewed events must have a reviewer. This will be set automatically."
            )
        return data
