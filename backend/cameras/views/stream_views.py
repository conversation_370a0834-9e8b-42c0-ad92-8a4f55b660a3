"""Camera streaming views for the SquirrelSentry system

This module handles proxy streaming from RTSP sources to web-compatible MJPEG.
Optimized for both WSGI (Django) and ASGI (Uvicorn) servers with async compatibility.
"""
import os
import json
import time
import socket
import logging
import threading
import subprocess
import requests
import datetime
import io
import asyncio
import functools
from urllib.parse import urlencode
from PIL import Image, ImageDraw
from django.http import StreamingHttpResponse, HttpResponse, HttpResponseServerError, Http404, FileResponse, HttpResponseBadRequest
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from cameras.utils.camera_stream import CameraStream
from utils.redis_connection import CameraCache
from cameras.models import Camera

# Core imports for streaming
import requests
import time

# Async compatibility for Uvicorn
class AsyncIteratorWrapper:
    """
    Wraps a synchronous iterator to make it compatible with asynchronous iteration.
    This allows synchronous generators to be consumed by Uvicorn's ASGI server.
    """
    def __init__(self, sync_iterator):
        self.sync_iterator = sync_iterator
        
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        # Use a safer approach that doesn't expose StopIteration to asyncio.to_thread
        # This approach uses a sentinel value to indicate the end of iteration
        sentinel = object()
        
        def safe_next(it):
            try:
                return next(it)
            except StopIteration:
                return sentinel
        
        item = await asyncio.to_thread(safe_next, self.sync_iterator)
        if item is sentinel:
            raise StopAsyncIteration
        return item

def async_compatible(func):
    """
    Decorator to make a function's return value async-compatible.
    
    If the function returns a StreamingHttpResponse with a synchronous
    iterator as content, this wraps it in an AsyncIteratorWrapper.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        # Check if it's a StreamingHttpResponse
        if isinstance(result, StreamingHttpResponse):
            # Make sure we have an async-compatible iterator
            result.streaming_content = AsyncIteratorWrapper(result.streaming_content)
        
        return result
    
    return wrapper

from ..models import Camera

# Create a custom renderer for MJPEG streams
class MjpegRenderer(BaseRenderer):
    media_type = 'multipart/x-mixed-replace'
    format = 'mjpeg'
    charset = None
    render_style = 'binary'
    
    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data

logger = logging.getLogger(__name__)

@api_view(['GET', 'HEAD'])
@renderer_classes([MjpegRenderer])  # Use our custom renderer to handle MJPEG content
# TEMPORARY: Authentication disabled for testing
# @permission_classes([IsAuthenticated])  # Commented out for temporary public access
@async_compatible  # Make this function async-compatible for Uvicorn
def stream_video(request, camera_id):
    """Stream video from an RTSP camera through Django as MJPEG
    
    Args:
        request: The HTTP request
        camera_id: UUID of the camera to stream
        
    Returns:
        StreamingHttpResponse with MJPEG content
    """
    # Log access for debugging
    logger.info(f"Stream endpoint accessed for camera {camera_id}")
    
    # Handle HEAD requests explicitly - just return headers, no content
    if request.method == 'HEAD':
        # Get expected content type for a streaming response
        response = Response(status=200)
        response["Content-Type"] = 'multipart/x-mixed-replace; boundary=frame'
        
        # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
        response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
        response["Access-Control-Allow-Credentials"] = "true"
        response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
        response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
        
        # Additional headers that help with CORB issues
        response["Timing-Allow-Origin"] = "*"
        response["X-Content-Type-Options"] = "nosniff"
        response["Cross-Origin-Opener-Policy"] = "same-origin"
        response["Cross-Origin-Resource-Policy"] = "cross-origin"
        
        return response
    
    # For testing with HTML (simple test page)
    if request.GET.get('test') == 'true':
        return HttpResponse(
            f"<html><body><h1>Stream Test Successful</h1><p>Camera ID: {camera_id}</p></body></html>",
            content_type='text/html'
        )
    
    # Static image test (useful for debugging image display)
    if request.GET.get('static') == 'true':
        try:
            # Look up the camera
            camera = Camera.objects.get(id=camera_id)
            
            # Create a test image with basic info
            width, height = 640, 480
            image = Image.new('RGB', (width, height), color=(40, 40, 40))
            draw = ImageDraw.Draw(image)
            draw.text((20, 20), f"Camera: {camera.name}", fill=(255, 255, 255))
            draw.text((20, 50), f"ID: {camera_id}", fill=(255, 255, 255))
            draw.text((20, 80), f"Static Test Image", fill=(255, 255, 0))
            draw.text((20, 110), f"Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", fill=(255, 255, 255))
            
            # Add a border
            draw.rectangle([(0, 0), (width-1, height-1)], outline=(255, 255, 255))
            
            # Convert to JPEG
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=90)
            return HttpResponse(buffer.getvalue(), content_type='image/jpeg')
            
        except Exception as e:
            logger.error(f"Error creating static test image: {str(e)}")
            return HttpResponseServerError(f"Error creating test image: {str(e)}")
    
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        
        # Determine if we should use simulation mode
        simulation_mode = request.GET.get('simulate') == 'true'

        # Initialize the camera stream
        camera_stream = CameraStream(camera.rtsp_url, camera_id=camera.id, fps=camera.stream_fps)
        
        # Initialize the redis client
        # redis_client = CameraCache(camera_id=camera_id)

        # Initialize the streaming redis client
        streaming_redis_client = CameraCache(camera_id=camera_id, host="streaming_redis")
        
        # Define the frame generator
        def generate_mjpeg_stream(redis_client: CameraCache):
            # Log detailed camera information for diagnostics
            logger.info(f"Camera details - ID: {camera.id}, Name: {camera.name}, RTSP URL: {camera.rtsp_url}")
            logger.info(f"Camera current status: {camera.status}, Last updated: {camera.updated_at}")
            
            # # Update camera last_status_check (this field exists in the model)
            # try:
            #     # The camera model has last_status_check which is auto_now, so just saving will update it
            #     camera.save(update_fields=['last_status_check'])
            # except Exception as e:
            #     logger.warning(f"Could not update camera access time: {e}")

            # Calculate target frame interval based on FPS
            # target_frame_interval = 1.0 / camera.stream_fps if camera.stream_fps > 0 else 0.1
            # last_frame_time = time.time()

            if camera.required_analytics:
                for frame_data in camera_stream.stream_from_ram(redis_client):
                    # Frame timing control to prevent overwhelming the client
                    yield frame_data
                    # current_time = time.time()
                    # time_since_last_frame = current_time - last_frame_time
                    
                    # if time_since_last_frame < target_frame_interval:
                    #     time.sleep(target_frame_interval - time_since_last_frame)
                    
                    # yield frame_data
                    # last_frame_time = time.time()
            else:
                # for frame_data in camera_stream.direct_stream():
                #     # Frame timing control to prevent overwhelming the client
                #     yield frame_data

                for frame_data in camera_stream.stream_from_ram(redis_client):
                    yield frame_data
        
        # Return the streaming response with proper CORS headers
        logger.info(f"Starting MJPEG stream for camera {camera.name}")
        
        # IMPORTANT: Return a direct Django response rather than through DRF
        # This bypasses content negotiation issues with the streaming content
        # Set a very high timeout to prevent Django from closing the connection prematurely
        import socket
        old_timeout = socket.getdefaulttimeout()
        socket.setdefaulttimeout(3600)  # 1 hour timeout for long-lived streams
        
        try:
            # Create a standard streaming response
            response = StreamingHttpResponse(
                streaming_content=generate_mjpeg_stream(streaming_redis_client),
                content_type='multipart/x-mixed-replace; boundary=frame'
            )
            
            # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
            response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
            response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
            response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
            response["Access-Control-Allow-Credentials"] = "true"
            response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
            response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
            
            # Additional headers that help with CORB issues
            response["Timing-Allow-Origin"] = "*"
            response["X-Content-Type-Options"] = "nosniff"
            response["Cross-Origin-Opener-Policy"] = "same-origin"
            response["Cross-Origin-Resource-Policy"] = "cross-origin"
            
            # Add cache control headers
            response["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response["Pragma"] = "no-cache"
            response["Expires"] = "0"
            
        finally:
            # Reset socket timeout when done
            socket.setdefaulttimeout(old_timeout)
        
        return response
        
    except Camera.DoesNotExist:
        logger.warning(f"Camera not found: {camera_id}")
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in stream_video: {str(e)}")
        return HttpResponseServerError(f"Error streaming camera: {str(e)}")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@async_compatible  # Make this function async-compatible for Uvicorn
def get_thumbnail(request, camera_id):
    """Get a thumbnail image for a camera
    
    Args:
        request: HTTP request
        camera_id: UUID of the camera
        
    Returns:
        JPEG image as HttpResponse
    """
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        logger.info(f"Thumbnail requested for camera {camera.name}")
        
        # Check if we have a stored thumbnail
        if hasattr(camera, 'thumbnail_path') and camera.thumbnail_path and os.path.exists(camera.thumbnail_path):
            return FileResponse(open(camera.thumbnail_path, 'rb'), content_type='image/jpeg')
        
        # Generate a basic thumbnail
        width, height = 320, 240
        image = Image.new('RGB', (width, height), color=(40, 40, 40))
        draw = ImageDraw.Draw(image)
        draw.text((10, 10), f"Camera: {camera.name}", fill=(255, 255, 255))
        draw.text((10, 40), "No Thumbnail", fill=(200, 200, 200))
        
        # Add border
        draw.rectangle([(0, 0), (width-1, height-1)], outline=(100, 100, 100))
        
        # Convert to JPEG
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        return HttpResponse(buffer.getvalue(), content_type='image/jpeg')
        
    except Camera.DoesNotExist:
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in get_thumbnail: {str(e)}")
        return HttpResponseServerError(f"Error getting thumbnail: {str(e)}")


@api_view(['GET', 'HEAD', 'OPTIONS'])
@renderer_classes([MjpegRenderer])  # Use our custom renderer to handle MJPEG content
# TEMPORARY: Authentication disabled for testing
# @permission_classes([IsAuthenticated])  # Commented out for temporary public access
@async_compatible  # Make this function async-compatible for Uvicorn
def stream_video_with_detections(request, camera_id):
    """Stream video from an RTSP camera through Django as MJPEG with detection data
    
    This endpoint is similar to stream_video but includes detection data in HTTP headers
    for each frame, allowing the frontend to overlay bounding boxes.
    
    Args:
        request: The HTTP request
        camera_id: UUID of the camera to stream
        
    Returns:
        StreamingHttpResponse with MJPEG content and detection metadata
    """
    # Log access for debugging
    logger.info(f"Detection stream endpoint accessed for camera {camera_id} - Method: {request.method}")
    
    # Handle OPTIONS requests for CORS preflight
    if request.method == 'OPTIONS':
        response = Response(status=200)
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response
    
    # Handle HEAD requests explicitly - just return headers, no content
    if request.method == 'HEAD':
        # Get expected content type for a streaming response
        response = Response(status=200)
        response["Content-Type"] = 'multipart/x-mixed-replace; boundary=frame'
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Allow-Credentials"] = "true"
        response["X-Detection-Enabled"] = "true"
        return response
    
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        
        # Record access in logs
        logger.info(f"Detection stream for camera {camera.name} ({camera_id}) accessed at {timezone.now().isoformat()}")
        
        # Option to force simulation mode for testing
        simulate = request.GET.get('simulate') == 'true'
        
        # Generate the MJPEG stream with detections
        logger.info(f"Starting MJPEG stream with detections for camera {camera.name}")
        if simulate:
            return HttpResponse(
                f"<html><body><h1>Simulation Mode Not Implemented</h1><p>Camera: {camera.name}</p></body></html>",
                content_type='text/html'
            )
        else:
            # Stream the MJPEG content directly rather than calling stream_video
            # since DRF Request and Django HttpRequest are not compatible
            
            # Create our own streaming response using the same generator function
            # as in stream_video but without passing the request object
            # to avoid the request type mismatch error
            
            # Set a very high timeout to prevent Django from closing the connection prematurely
            import socket
            old_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(3600)  # 1 hour timeout
            
            try:
                # Create a standard streaming response
                # Using the same frame generator approach as stream_video
                # but implementing it inline to avoid request type issues
                def generate_mjpeg_stream():
                    # Log detailed camera information for diagnostics
                    logger.info(f"Detection stream generator started for camera {camera.name} ({camera_id})")
                    
                    # Update camera last_status_check (this field exists in the model)
                    try:
                        # The camera model has last_status_check which is auto_now, so just saving will update it
                        camera.save(update_fields=['last_status_check'])
                    except Exception as e:
                        logger.warning(f"Could not update camera access time: {e}")
                    
                        # Get the RTSP server stream URL for this camera
                        rtsp_server_url = f"http://{RTSP_SERVER_HOST}:{RTSP_SERVER_PORT}/stream/{camera.id}/mjpeg"
                        logger.info(f"Using RTSP server URL for detection stream: {rtsp_server_url}")
                        
                        # Connect to the RTSP server stream
                        try:
                            # Stream the frames directly from the RTSP server
                            logger.info(f"Connecting to RTSP server for detection stream: {rtsp_server_url}")
                            
                            # Set up a session with keep-alive
                            with requests.Session() as session:
                                # Configure the session
                                session.stream = True  # Enable streaming mode
                                
                                # Make the request to the RTSP server
                                response = session.get(
                                    rtsp_server_url,
                                    stream=True,
                                    timeout=settings.RTSP_CONNECTION_TIMEOUT
                                )
                                
                                # Stream content in chunks
                                for chunk in response.iter_content(chunk_size=16384):
                                    if chunk:
                                        yield chunk
                        except requests.exceptions.RequestException as e:
                            logger.error(f"Error connecting to RTSP server for detection stream: {str(e)}")
                            yield b'--frame\r\n'
                            error_frame = _generate_error_frame(f"Error connecting to RTSP server: {str(e)}")
                            yield error_frame
                            yield b'\r\n'
                    
                
                response = StreamingHttpResponse(
                    streaming_content=generate_mjpeg_stream(),
                    content_type='multipart/x-mixed-replace; boundary=frame'
                )
                
                # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
                # For MJPEG streams, we need to be very explicit with our CORS headers
                response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
                response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
                response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
                response["Access-Control-Allow-Credentials"] = "true"
                response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
                response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
                response["X-Detection-Enabled"] = "true"
                
                # In production, please restrict these to specific origins, headers, etc.
                
                # Add additional headers that help with CORB issues
                response["Timing-Allow-Origin"] = "*"
                response["X-Content-Type-Options"] = "nosniff"
                
                # Add Cross-Origin-Opener-Policy headers to help with isolation policies
                # Note: Chrome is particularly strict with these
                response["Cross-Origin-Opener-Policy"] = "same-origin"
                response["Cross-Origin-Resource-Policy"] = "cross-origin"
            finally:
                # Reset socket timeout when done
                socket.setdefaulttimeout(old_timeout)
            
            return response
    
    except Camera.DoesNotExist:
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in stream_video_with_detections: {str(e)}")
        return HttpResponseServerError(f"Error streaming video with detections: {str(e)}")


@api_view(['GET', 'HEAD', 'OPTIONS'])
@renderer_classes([MjpegRenderer])
# @permission_classes([IsAuthenticated])  # Uncomment to require authentication
@async_compatible
def stream_playback(request, camera_id):
    """Stream historical playback video from a camera.
    
    Query Parameters:
        start_time: Required - UTC timestamp for beginning of playback
        end_time: Optional - UTC timestamp for end of playback
        use_nvr: Optional - If 'true', directly connect to NVR instead of RTSP
    
    Returns:
        StreamingHttpResponse: MJPEG stream
    """
    # Get the start time from the query parameters
    start_time = request.GET.get('start_time')
    if not start_time:
        return HttpResponseBadRequest("Missing required parameter: start_time")
        
    # Get the optional end time from the query parameters
    end_time = request.GET.get('end_time')
    
    # Log the request
    end_time_str = "now" if not end_time else end_time
    logger.info(f"Playback stream endpoint accessed for camera {camera_id} from {start_time} to {end_time_str} using direct NVR connection")
    
    # Handle OPTIONS requests for CORS preflight
    if request.method == 'OPTIONS':
        response = Response(status=200)
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response
    
    # Handle HEAD requests explicitly - just return headers, no content
    if request.method == 'HEAD':
        response = Response(status=200)
        response["Content-Type"] = 'multipart/x-mixed-replace; boundary=frame'
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        return response
    
    try:
        # Get camera object
        camera = get_object_or_404(Camera, id=camera_id)
        
        # Parse and format timestamp for NVR API in the format that works with both RTSP and NVR API
        try:
            # Parse start_time from ISO format or other common formats
            if 'T' in start_time:
                # Handle ISO format timestamps with proper timezone handling
                # Replace Z with +00:00 for UTC timezone if present
                clean_time = start_time
                if clean_time.endswith('Z'):
                    clean_time = clean_time[:-1] + '+00:00'
                elif '+' not in clean_time and '-' not in clean_time[10:]:  # No timezone info
                    clean_time = clean_time + '+00:00'  # Assume UTC
                    
                try:
                    # Parse with fromisoformat which handles timezone info
                    start_dt = datetime.datetime.fromisoformat(clean_time)
                except ValueError:
                    # Fall back to strptime if fromisoformat fails
                    logger.warning(f"Failed to parse with fromisoformat, trying strptime: {clean_time}")
                    start_dt = datetime.datetime.strptime(clean_time.split('.')[0], "%Y-%m-%dT%H:%M:%S")
            else:
                # Try to parse as YYYY-MM-DD HH:MM:SS
                start_dt = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            
            # Format for NVR API - use the format from the working ffplay example: YYYYMMDDTHHMMSSZ
            # This format works with the NVR API and RTSP URL
            start_time_str = start_dt.strftime("%Y%m%dT%H%M%SZ")
            logger.info(f"Formatted start time: {start_time_str}")
            
            # Similarly format end_time if provided
            end_time_str = None
            if end_time:
                if 'T' in end_time:
                    end_dt = datetime.datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                else:
                    end_dt = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                end_time_str = end_dt.strftime("%Y%m%dT%H%M%SZ")
                logger.info(f"Formatted end time: {end_time_str}")
                
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid timestamp format: {e}")
            return HttpResponseBadRequest(f"Invalid timestamp format: {e}")
        
        # Get base camera path from RTSP URL
        camera_path = camera.rtsp_url.split('://')[-1]
        if '@' in camera_path:
            # Already has credentials in URL, remove them to prevent duplication
            camera_path = camera_path.split('@')[-1]
        
        # Get RTSP credentials from environment variables or use the known working credentials
        rtsp_user = os.environ.get('RTSP_USERNAME', 'admin')
        rtsp_pass = os.environ.get('RTSP_PASSWORD', 'Pr0s3gur-smrt')
        
        # Always use direct NVR connection for playback
        # Get NVR connection parameters from settings (with safe fallbacks)
        # Use the same IP address as the RTSP server that was confirmed working
        nvr_host = getattr(settings, 'NVR_HOST', os.environ.get('NVR_HOST', '************'))
        nvr_port = int(getattr(settings, 'NVR_PORT', os.environ.get('NVR_PORT', 80)))
        nvr_username = getattr(settings, 'NVR_USERNAME', os.environ.get('NVR_USERNAME', rtsp_user))
        nvr_password = getattr(settings, 'NVR_PASSWORD', os.environ.get('NVR_PASSWORD', rtsp_pass))
        nvr_use_ssl = getattr(settings, 'NVR_USE_SSL', os.environ.get('NVR_USE_SSL', 'false')).lower() == 'true'
        
        # Get camera channel number from camera object or default to channel 1
        try:
            channel = int(camera.nvr_channel) if hasattr(camera, 'nvr_channel') and camera.nvr_channel else 1
        except (ValueError, TypeError):
            channel = 1
        
        # Prepare NVR parameters
        nvr_params = {
            'host': nvr_host,
            'port': nvr_port,
            'username': nvr_username,
            'password': nvr_password,
            'use_ssl': nvr_use_ssl,
            'channel': channel,
            'rtsp_port': int(getattr(settings, 'RTSP_PORT', os.environ.get('RTSP_PORT', 554)))
        }
        
        logger.info(f"Using direct NVR connection to {nvr_host}:{nvr_port} for camera {camera_id} (channel {channel})")
        
        # Set up streaming response
        try:
            # Store old timeout value so we can restore it
            old_timeout = socket.getdefaulttimeout()
            
            try:
                # Set a timeout for socket operations
                socket.setdefaulttimeout(settings.RTSP_CONNECTION_TIMEOUT)
                
                # Define a generator function to stream MJPEG frames
                def generate_mjpeg_stream():
                    try:
                        # Create a camera stream instance for NVR playback with connection params
                        stream = CameraStream(rtsp_url="", camera_id=camera_id, nvr_params=nvr_params)
                        logger.info(f"CameraStream initialized for NVR playback: {nvr_params['host']}:{nvr_params['port']} (channel {nvr_params['channel']})")
                        
                        # Pass only start_time and end_time to the playback method
                        # The method will use nvr_params from the CameraStream instance
                        frame_generator = stream.nvr_playback_stream(
                            start_time=start_time_str,
                            end_time=end_time_str if end_time_str else None
                        )
                        
                        # Start streaming and verify connectivity
                        try:
                            # Get the first frame to verify connection
                            first_frame = next(frame_generator, None)
                            if not first_frame:
                                # No frames available, create an error message
                                yield b'--frame\r\n'
                                img = Image.new('RGB', (640, 480), color='black')
                                d = ImageDraw.Draw(img)
                                d.text((20, 220), f"Could not connect to camera playback stream", fill='red')
                                img_byte_arr = io.BytesIO()
                                img.save(img_byte_arr, format='JPEG')
                                yield b'Content-Type: image/jpeg\r\n\r\n' + img_byte_arr.getvalue()
                                yield b'\r\n'
                                return
                            else:
                                # First frame is good, yield it and continue
                                yield first_frame
                        except Exception as e:
                            logger.error(f"Error getting first frame: {e}")
                            yield b'--frame\r\n'
                            img = Image.new('RGB', (640, 480), color='black')
                            d = ImageDraw.Draw(img)
                            d.text((20, 220), f"Error getting first frame: {str(e)}", fill='red')
                            img_byte_arr = io.BytesIO()
                            img.save(img_byte_arr, format='JPEG')
                            yield b'Content-Type: image/jpeg\r\n\r\n' + img_byte_arr.getvalue()
                            yield b'\r\n'
                            return
                        
                        # Continue getting frames from the generator
                        frames_received = 0
                        try:
                            # We already yielded the first frame, now continue with the rest
                            for frame_data in frame_generator:
                                if not frame_data:
                                    continue
                                    
                                # Got a frame, yield it (the generator already adds the frame boundaries)
                                frames_received += 1
                                yield frame_data
                                
                        except StopIteration:
                            # Generator ended normally
                            if frames_received == 0:
                                yield b'--frame\r\n'
                                img = Image.new('RGB', (640, 480), color='black')
                                d = ImageDraw.Draw(img)
                                d.text((20, 220), f"No more frames available in playback", fill='red')
                                img_byte_arr = io.BytesIO()
                                img.save(img_byte_arr, format='JPEG')
                                yield b'Content-Type: image/jpeg\r\n\r\n' + img_byte_arr.getvalue()
                                yield b'\r\n'
                            # End normally
                            return
                            
                        except Exception as e:
                            logger.error(f"Error getting frame from playback stream: {e}")
                            yield b'--frame\r\n'
                            img = Image.new('RGB', (640, 480), color='black')
                            d = ImageDraw.Draw(img)
                            d.text((20, 220), f"Error: {str(e)}", fill='red')
                            img_byte_arr = io.BytesIO()
                            img.save(img_byte_arr, format='JPEG')
                            yield b'Content-Type: image/jpeg\r\n\r\n' + img_byte_arr.getvalue()
                            yield b'\r\n'
                            time.sleep(1)
                                
                    except Exception as e:
                        # Create and yield an error frame
                        logger.error(f"Stream error in playback: {str(e)}")
                        yield b'--frame\r\n'
                        # Create error image directly since get_error_frame doesn't exist
                        img = Image.new('RGB', (640, 480), color='black')
                        d = ImageDraw.Draw(img)
                        d.text((20, 220), f"Stream error: {str(e)}", fill='red')
                        img_byte_arr = io.BytesIO()
                        img.save(img_byte_arr, format='JPEG')
                        yield b'Content-Type: image/jpeg\r\n\r\n' + img_byte_arr.getvalue()
                        yield b'\r\n'
                    finally:
                        # Make sure to close the stream
                        if 'stream' in locals() and hasattr(stream, 'stop'):
                            stream.stop()
                
                response = StreamingHttpResponse(
                    streaming_content=generate_mjpeg_stream(),
                    content_type='multipart/x-mixed-replace; boundary=frame'
                )
                
                # Add CORS headers to prevent CORB blocking with MJPEG streams
                response["Access-Control-Allow-Origin"] = "*"
                response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
                response["Access-Control-Allow-Headers"] = "*"
                response["Access-Control-Allow-Credentials"] = "true"
                response["Access-Control-Expose-Headers"] = "*"
                response["Access-Control-Max-Age"] = "86400"
                response["X-Playback-Enabled"] = "true"
                
                # Add additional headers that help with CORB issues
                response["Timing-Allow-Origin"] = "*"
                response["X-Content-Type-Options"] = "nosniff"
                response["Cross-Origin-Opener-Policy"] = "same-origin"
                response["Cross-Origin-Resource-Policy"] = "cross-origin"
                
            finally:
                # Reset socket timeout when done
                socket.setdefaulttimeout(old_timeout)
                
            return response
            
        except Exception as e:
            logger.error(f"Error in playback stream: {e}")
            return HttpResponseServerError(f"Error in playback stream: {str(e)}")
            
    except Camera.DoesNotExist:
        logger.error(f"Camera {camera_id} not found for playback")
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Unexpected error in stream_playback: {e}")
        return HttpResponseServerError(f"Error streaming playback video: {str(e)}")
