"""
Async wrapper utilities for SquirrelSentry RTSP streaming
"""
import asyncio
import functools
import inspect
from typing import AsyncGenerator, Callable, Generator, Any

class AsyncIteratorWrapper:
    """
    Wraps a synchronous iterator to make it compatible with asynchronous iteration.
    This allows synchronous generators to be consumed by Uvicorn's ASGI server.
    """
    def __init__(self, sync_iterator):
        self.sync_iterator = sync_iterator
        
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        try:
            # Run the synchronous iterator's next() in a thread pool
            return await asyncio.to_thread(next, self.sync_iterator)
        except StopIteration:
            raise StopAsyncIteration

def async_compatible(func):
    """
    Decorator to make a function's return value async-compatible.
    
    If the function returns a StreamingHttpResponse with a synchronous
    iterator as content, this wraps it in an AsyncIteratorWrapper.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        # Check if it's a StreamingHttpResponse
        from django.http import StreamingHttpResponse
        if isinstance(result, StreamingHttpResponse):
            # Make sure we have an async-compatible iterator
            result.streaming_content = AsyncIteratorWrapper(result.streaming_content)
        
        return result
    
    return wrapper
