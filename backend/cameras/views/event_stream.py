import json
import logging
import uuid
import os
import asyncio
from django.http import StreamingHttpResponse, HttpResponse
from django.core.serializers.json import DjangoJ<PERSON><PERSON>ncoder
from cameras.models import CameraEvent
from cameras.serializers import CameraEventSerializer
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from asgiref.sync import sync_to_async
from utils.redis_connection import Camera<PERSON>ache


# Custom JSON Encoder to handle UUID objects
class CustomJSONEncoder(DjangoJSONEncoder):
    """JSON encoder that handles UUIDs and other special objects."""

    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


logger = logging.getLogger(__name__)


@csrf_exempt
@login_required
def event_stream(request):
    # Handle OPTIONS requests for CORS preflight
    # if request.method == 'OPTIONS':
    #     response = HttpResponse()
    #     print(f'Request headers: {request.headers.get("Origin", "*")}')
    #     response["Access-Control-Allow-Origin"] = request.headers.get("Origin", "*")
    #     response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    #     response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
    #     response["Access-Control-Allow-Credentials"] = "true"
    #     response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
    #     return response

    async def event_stream_generator():
        print("Starting event stream generator")
        redis_client = CameraCache(host="localhost")
        print("Created Redis client")

        group = "backend_events_group"
        queue_name = "processed_events_queue_stream"
        consumer_name = f"backend_events_consumer_{os.getpid()}"
        print(f"Using consumer name: {consumer_name}")

        try:
            redis_client._redis.xgroup_create(queue_name, group, id="$", mkstream=True)
            print(f"Created Redis stream group: {group}")
        except Exception as e:
            # group already exists; ignore
            if "BUSYGROUP" not in str(e):
                logger.error(f"Error setting up Redis stream: {str(e)}")
                raise
            print("Stream group already exists")

        # Initial request will need to query from the db for all unreviewed events
        # and send them to the client
        print("Fetching initial unreviewed events from database")
        unreviewed_events = await sync_to_async(
            lambda: list(
                CameraEvent.objects.filter(is_reviewed=False)
                .select_related("camera_layer_config__camera", "frame")
                .order_by("-timestamp")[:50]
            )
        )()
        print(f"Found {len(unreviewed_events)} initial unreviewed events")

        serializer = CameraEventSerializer(unreviewed_events, many=True)
        existing_events = list(serializer.data)
        print(f"Serialized {len(existing_events)} existing events")

        is_initial_request = True
        while True:
            try:
                print("Checking for new messages from Redis stream")
                # Then read **new** messages
                resp = redis_client._redis.xreadgroup(
                    group, consumer_name, streams={queue_name: ">"}, count=1, block=500
                )
                new_events = []
                if resp:
                    print(f"Received response from Redis:")
                    for stream, messages in resp:
                        for msg_id, fields in messages:
                            print(f"Processing message ID: {msg_id}")
                            print(f"Received event: {fields.get('id')}")

                            event_id = fields.get("id")
                            print(f"Looking up event ID: {event_id} in database")

                            new_event = await sync_to_async(
                                lambda: CameraEvent.objects.filter(id=event_id)
                                .select_related("camera_layer_config__camera", "frame")
                                .first()
                            )()
                            print(f"Retrieved event from database: {new_event.id}")

                            if new_event:
                                serializer = CameraEventSerializer(
                                    new_event, many=False
                                )
                                new_events = [serializer.data]
                                print(
                                    f"Serialized new event: {new_events[0].get('id')}"
                                )

                                redis_client._redis.xack(queue_name, group, msg_id)
                                print(f"Acknowledged message {msg_id}")

                elif len(existing_events) == 0 and len(new_events) == 0:
                    print("No new messages, sleeping...")
                    await asyncio.sleep(0.5)
                    continue

                print(f"Existing events: {len(existing_events)}")
                print(f"New events: {len(new_events)}")

                if is_initial_request:
                    print("Processing initial request")
                    final_events = existing_events + new_events
                    is_initial_request = False
                    existing_events = []
                    print(f"Sending initial data: {len(final_events)}")
                    for event in final_events:
                        print(f"Event: {event.get('id')}")
                    data = json.dumps(
                        {"type": "events", "data": final_events}, cls=DjangoJSONEncoder
                    )

                else:
                    print("Processing subsequent request")
                    final_events = new_events
                    data = json.dumps(
                        {"type": "events", "data": final_events}, cls=DjangoJSONEncoder
                    )
                    print(f"Sending update data: {data}")

                yield f"data: {data}\n\n"

            except Exception as e:
                logger.error(f"Error in event stream loop: {str(e)}")
                await asyncio.sleep(1)  # Brief pause before retrying
                continue

    response = StreamingHttpResponse(
        event_stream_generator(), content_type="text/event-stream"
    )
    response["Cache-Control"] = "no-cache"
    response["Content-Type"] = "text/event-stream"
    response["X-Accel-Buffering"] = "no"  # Disable nginx buffering
    response["Access-Control-Allow-Origin"] = request.headers.get("Origin", "*")
    response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response["Access-Control-Allow-Headers"] = (
        "Content-Type, Authorization, X-Requested-With"
    )
    response["Access-Control-Allow-Credentials"] = "true"
    response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
    return response
