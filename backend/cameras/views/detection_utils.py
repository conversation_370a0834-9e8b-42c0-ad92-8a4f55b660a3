"""Utility functions for generating and handling object detection data.

This module contains utilities for working with object detection data, including
functions for generating mock detections for testing and development purposes.
"""
import random
import time
import logging

# Create logger
logger = logging.getLogger(__name__)

def generate_random_detections(frame_id, camera_id):
    """Generate random bounding boxes for testing
    
    Args:
        frame_id: Sequential frame number
        camera_id: UUID of the camera
        
    Returns:
        JSON-serializable dictionary with detection data
    """
    # Number of detections - randomly vary between 0-5 objects
    num_detections = random.randint(0, 5)
    
    # Possible object types with their colors
    object_types = [
        {"type": "person", "color": "red"},
        {"type": "vehicle", "color": "blue"},
        {"type": "animal", "color": "green"},
        {"type": "suspicious", "color": "orange"},
        {"type": "unattended_object", "color": "purple"}
    ]
    
    detections = []
    for i in range(num_detections):
        # Select random object type
        obj_type = random.choice(object_types)
        
        # Create random bounding box (x1,y1,x2,y2 format, normalized 0-1)
        x1 = random.uniform(0.1, 0.7)  # Left boundary 
        y1 = random.uniform(0.1, 0.7)  # Top boundary
        width = random.uniform(0.1, 0.3)  # Width of box
        height = random.uniform(0.1, 0.3)  # Height of box
        
        # Ensure box stays within frame bounds
        x2 = min(x1 + width, 0.98)
        y2 = min(y1 + height, 0.98)
        
        # Generate random confidence score
        confidence = round(random.uniform(0.65, 0.99), 2)
        
        # Create detection object
        detection = {
            "id": f"det_{frame_id}_{i}",
            "type": obj_type["type"],
            "color": obj_type["color"],
            "confidence": confidence,
            "bbox": [x1, y1, x2, y2]  # Normalized coordinates
        }
        
        detections.append(detection)
        
    # Create full frame metadata
    metadata = {
        "frame_id": frame_id,
        "camera_id": str(camera_id),
        "timestamp": time.time(),
        "detections": detections
    }
    
    return metadata


def empty_detection_data(frame_id=-1, camera_id=None):
    """Generate an empty detection data object for error frames
    
    Args:
        frame_id: Frame identifier, defaults to -1 for error frames
        camera_id: Camera UUID, optional
        
    Returns:
        Empty detection data dictionary
    """
    return {
        "frame_id": frame_id,
        "camera_id": str(camera_id) if camera_id else None,
        "timestamp": time.time(),
        "detections": []
    }
