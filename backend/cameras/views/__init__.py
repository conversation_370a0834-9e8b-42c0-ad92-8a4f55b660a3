"""
Views for the cameras app - organized by feature
"""
# Import all views to maintain backward compatibility
from .camera_views import (
    camera_list, 
    camera_detail, 
    list_streams
)
from .stream_views import (
    stream_video, 
    get_thumbnail,
)
from .roi_views import (
    roi_list, 
    roi_detail,
    roi_camera_list
)
from .layer_views import (
    layer_list, 
    layer_detail,
    create_default_layers_for_camera
)

from .stream_views import (
    stream_video,
    stream_video_with_detections,
    get_thumbnail,
    stream_playback
)
from .overlay_views import (
    overlay_list,
    overlay_detail,
    overlay_camera_list
)
from .event_views import (
    event_list,
    event_detail,
)
from .event_stream import event_stream

# Export all views to maintain backward compatibility with existing imports
__all__ = [
    'camera_list',
    'camera_detail',
    'stream_video',
    'stream_video_with_detections',
    'stream_playback',
    'get_thumbnail',
    'roi_list',
    'roi_detail',
    'roi_camera_list',
    'layer_list',
    'layer_detail',
    'list_streams',
    'create_default_layers_for_camera',
    'event_stream',
    'event_list',
    'event_detail',
    'overlay_list',
    'overlay_detail',
    'overlay_camera_list',
]
