"""
Permanent Camera Overlay view functions
"""
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from ..models import Camera, PermanentCameraOverlay

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def overlay_list(request):
    """List all permanent overlays for all cameras or create a new one"""
    
    if request.method == 'GET':
        # List all overlays for all cameras
        overlays = PermanentCameraOverlay.objects.all()
        data = []
        for overlay in overlays:
            data.append({
                'id': str(overlay.id),
                'name': overlay.name,
                'description': overlay.description,
                'camera': str(overlay.camera.id),
                'camera_name': overlay.camera.name,
                'coordinates': overlay.coordinates,
                'text': overlay.text,
                'color': overlay.color,
                'created_at': overlay.created_at,
                'updated_at': overlay.updated_at,
            })
        return Response(data)
    
    elif request.method == 'POST':
        # Create a new overlay
        try:
            data = request.data.copy()
            
            # Validate the coordinates
            if 'coordinates' not in data or not isinstance(data['coordinates'], dict):
                return Response({"error": "Coordinates must contain absolute and normalized points"}, 
                               status=status.HTTP_400_BAD_REQUEST)
                
            # Check for camera
            if 'camera' not in data:
                return Response({"error": "Camera ID is required"}, 
                              status=status.HTTP_400_BAD_REQUEST)
                
            # Get the camera
            try:
                camera = Camera.objects.get(id=data['camera'])
            except Camera.DoesNotExist:
                return Response({"error": f"Camera with ID {data['camera']} not found"}, 
                              status=status.HTTP_404_NOT_FOUND)
            
            # Create the overlay
            overlay = PermanentCameraOverlay.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                camera=camera,
                coordinates=data['coordinates'],
                text=data.get('text'),
                color=data.get('color', '#FF0000')
            )
            
            return Response({
                'id': str(overlay.id),
                'name': overlay.name,
                'description': overlay.description,
                'camera': str(overlay.camera.id),
                'camera_name': overlay.camera.name,
                'coordinates': overlay.coordinates,
                'text': overlay.text,
                'color': overlay.color,
                'created_at': overlay.created_at,
                'updated_at': overlay.updated_at,
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating overlay: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def overlay_detail(request, overlay_id):
    """Retrieve, update or delete a permanent overlay"""
    try:
        overlay = PermanentCameraOverlay.objects.get(id=overlay_id)
    except PermanentCameraOverlay.DoesNotExist:
        return Response({"error": "Overlay not found"}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        # Return overlay details
        return Response({
            'id': str(overlay.id),
            'name': overlay.name,
            'description': overlay.description,
            'camera': str(overlay.camera.id),
            'camera_name': overlay.camera.name,
            'coordinates': overlay.coordinates,
            'text': overlay.text,
            'color': overlay.color,
            'created_at': overlay.created_at,
            'updated_at': overlay.updated_at,
        })
        
    elif request.method == 'PUT':
        # Update overlay
        try:
            data = request.data.copy()
            
            # Update basic fields
            if 'name' in data:
                overlay.name = data['name']
            if 'description' in data:
                overlay.description = data['description']
            if 'text' in data:
                overlay.text = data['text']
            if 'color' in data:
                overlay.color = data['color']
            
            # Update camera if provided
            if 'camera' in data:
                try:
                    camera = Camera.objects.get(id=data['camera'])
                    overlay.camera = camera
                except Camera.DoesNotExist:
                    return Response({"error": f"Camera with ID {data['camera']} not found"}, 
                                  status=status.HTTP_404_NOT_FOUND)
            
            # Update coordinates if provided
            if 'coordinates' in data:
                if not isinstance(data['coordinates'], dict):
                    return Response({"error": "Coordinates must contain absolute and normalized points"}, 
                                  status=status.HTTP_400_BAD_REQUEST)
                overlay.coordinates = data['coordinates']
            
            overlay.save()
            
            return Response({
                'id': str(overlay.id),
                'name': overlay.name,
                'description': overlay.description,
                'camera': str(overlay.camera.id),
                'camera_name': overlay.camera.name,
                'coordinates': overlay.coordinates,
                'text': overlay.text,
                'color': overlay.color,
                'created_at': overlay.created_at,
                'updated_at': overlay.updated_at,
            })
            
        except Exception as e:
            logger.error(f"Error updating overlay: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Delete overlay
        overlay.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def overlay_camera_list(request, camera_id):
    """List all permanent overlays for a specific camera"""
    try:
        camera = Camera.objects.get(id=camera_id)
    except Camera.DoesNotExist:
        return Response({"error": "Camera not found"}, status=status.HTTP_404_NOT_FOUND)
    
    overlays = PermanentCameraOverlay.objects.filter(camera=camera)
    data = []
    
    for overlay in overlays:
        data.append({
            'id': str(overlay.id),
            'name': overlay.name,
            'description': overlay.description,
            'camera': str(overlay.camera.id),
            'camera_name': overlay.camera.name,
            'coordinates': overlay.coordinates,
            'text': overlay.text,
            'color': overlay.color,
            'created_at': overlay.created_at,
            'updated_at': overlay.updated_at,
        })
    
    return Response(data)
