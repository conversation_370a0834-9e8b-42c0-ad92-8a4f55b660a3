"""
CamerasLayersConfiguration view functions for detection layers
"""
import logging
from django.http import Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from ..models import Camera, CamerasLayersConfiguration, CamerasLayersConfigurationDependency, RegionOfInterest, Layer

logger = logging.getLogger(__name__)

def create_default_layers_for_camera(camera):
    """
    Create default layers for a new camera
    
    This function is called when a new camera is created to set up the default layer configuration.
    It creates standard layer configurations based on available base layers.
    
    Args:
        camera: The Camera model instance to create default layers for
        
    Returns:
        List of created CamerasLayersConfiguration instances
    """
    logger.info(f"Creating default layers for camera {camera.id}")
    
    # Get all available base layers
    base_layers = Layer.objects.all()
    created_configs = []
    
    # Create a configuration for each base layer
    for idx, layer in enumerate(base_layers):
        # Generate a color based on the layer index
        colors = ['#4287f5', '#42f5a7', '#f54242', '#f5cd42', '#9842f5']
        color = colors[idx % len(colors)]
        
        # Create the configuration layer
        config = CamerasLayersConfiguration.objects.create(
            camera=camera,
            layers=layer,
            name=layer.name,  # Use the base layer name as default
            enabled=True,     # Enable by default
            color=color,      # Assign a color
            is_default=True   # Mark as a default configuration
        )
        created_configs.append(config)
        logger.info(f"Created default layer configuration {config.id} for camera {camera.id}")
    
    return created_configs

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def layer_list(request, camera_id):
    """List all configuration layers for a camera or create a new one"""
    try:
        camera = Camera.objects.get(id=camera_id)
    except Camera.DoesNotExist:
        return Response({"error": "Camera not found"}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        # List all configuration layers for this camera
        config_layers = CamerasLayersConfiguration.objects.filter(camera=camera)
        logger.info(f"Found {config_layers.count()} configuration layers for camera {camera_id}")
        
        # Get all available base layers
        all_base_layers = []
        for base_layer in Layer.objects.all():
            all_base_layers.append({
                'id': str(base_layer.id),
                'name': base_layer.name,
                'layer_type': base_layer.layer_type,
                'function_name': base_layer.function_name,
                'cv_model': base_layer.cv_model.id if base_layer.cv_model else None,
                'created_at': base_layer.created_at,
                'updated_at': base_layer.updated_at
            })
        
        # Get all camera layer configurations
        config_data = []
        
        # Add detailed error handling to help diagnose issues
        try:
            for config_layer in config_layers:
                # Get the associated base Layer
                layer = config_layer.layers  # Access the related Layer model through the foreign key
            
                # Build response data
                layer_data = {
                    'id': str(config_layer.id),
                    'name': config_layer.name,
                    'camera': {
                        'id': str(camera.id),
                        'name': camera.name
                    },
                    'layer': {
                        'id': str(layer.id),
                        'name': layer.name,
                        'layer_type': layer.layer_type,
                        'function_name': layer.function_name,
                        'created_at': layer.created_at,
                        'updated_at': layer.updated_at
                    },
                    'enabled': config_layer.enabled,
                    'color': config_layer.color,
                    'configuration': config_layer.configuration,
                    'created_at': config_layer.created_at,
                    'updated_at': config_layer.updated_at
                }
                
                # Get all regions associated with this layer configuration
                regions_list = list(config_layer.regions.all())
                
                # Add regions to the response
                layer_data['regions'] = []
                
                for region in regions_list:
                    logger.info(f"Region {region.id} coordinates: {region.coordinates}")
                    
                    roi_type = getattr(region, 'roi_type', None)
                    
                    # Include all structured fields for the region
                    layer_data["regions"].append({
                        "id": str(region.id),
                        "name": region.name,
                        "description": region.description,
                        "roi_type": roi_type,
                        "coordinates": region.coordinates,
                        "is_active": region.is_active,
                        "alerts_category": region.alerts_category,
                        "requirement": region.requirement,
                        "pose_keypoints": region.pose_keypoints
                    })
                
                # Add dependencies
                dependencies = []
                for dep in config_layer.dependencies.all():
                    dependencies.append({
                        'id': str(dep.id),
                        'name': dep.name
                    })
                layer_data['dependencies'] = dependencies
                
                # Add this layer to the configurations list
                config_data.append(layer_data)
            
            # Return full response
            response = {
                'configurations': config_data,
                'base_layers': all_base_layers
            }
            
            return Response(response)
            
        except Exception as e:
            logger.error(f"Error processing layers: {str(e)}", exc_info=True)
            return Response({"error": f"Internal server error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    elif request.method == 'POST':
        # Create a new configuration layer
        data = request.data
        
        try:
            # Validate required fields
            if 'name' not in data:
                return Response({"error": "Name is required"}, status=status.HTTP_400_BAD_REQUEST)
            if 'layer_id' not in data:
                return Response({"error": "Layer ID is required"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the base layer
            try:
                base_layer = Layer.objects.get(id=data['layer_id'])
            except Layer.DoesNotExist:
                return Response({"error": "Base layer not found"}, status=status.HTTP_404_NOT_FOUND)
            
            regions_to_add = []

            # Also support multiple regions in regions_ids array
            if 'region_ids' in data and data['region_ids']:
                for region_id in data['region_ids']:
                    try:
                        region = RegionOfInterest.objects.get(id=region_id)
                        if region not in regions_to_add:  # Avoid duplicates
                            regions_to_add.append(region)
                    except RegionOfInterest.DoesNotExist:
                        logger.warning(f"Region {region_id} not found, skipping")
            
            # Create the configuration layer
            config_layer = CamerasLayersConfiguration.objects.create(
                name=data['name'],
                camera=camera,
                layers=base_layer,
                enabled=data.get('enabled', True),
                color=data.get('color', '#777777'),
                configuration=data.get('configuration', {})
            )
            
            # Add regions to the ManyToMany field after creation
            for region in regions_to_add:
                config_layer.regions.add(region)
            
            # Add dependencies if provided
            if 'dependencies' in data and data['dependencies']:
                for dep_id in data['dependencies']:
                    try:
                        dep_config_layer = CamerasLayersConfiguration.objects.get(id=dep_id)
                        if dep_config_layer.id != config_layer.id:  # Prevent self-dependency
                            # Create the dependency through model directly with conditions
                            CamerasLayersConfigurationDependency.objects.create(
                                current_layer=config_layer,
                                dependency_layer=dep_config_layer,
                                conditions={"default": True}  # Default non-null condition
                            )
                            logger.info(f"Created dependency with conditions: {config_layer.id} -> {dep_config_layer.id}")
                    except CamerasLayersConfiguration.DoesNotExist:
                        logger.warning(f"Dependency configuration layer {dep_id} not found")
                    except Exception as e:
                        logger.error(f"Error creating dependency: {str(e)}")
            
            # Build response data
            response_data = {
                'id': str(config_layer.id),
                'name': config_layer.name,
                'camera': {
                    'id': str(camera.id),
                    'name': camera.name
                },
                'layers': {
                    'id': str(base_layer.id),
                    'name': base_layer.name,
                    'layer_type': base_layer.layer_type,
                    'function_name': base_layer.function_name,
                    'created_at': base_layer.created_at,
                    'updated_at': base_layer.updated_at
                },
                'enabled': config_layer.enabled,
                'color': config_layer.color,
                'configuration': config_layer.configuration,
                'created_at': config_layer.created_at,
                'updated_at': config_layer.updated_at
            }
            # Get all regions associated with this layer configuration
            regions_list = list(config_layer.regions.all())
            
            # Add regions to the response
            response_data['regions'] = []
            
            for region in regions_list:
                # We're using coordinates directly now
                response_data["regions"].append({
                    "id": str(region.id),
                    "name": region.name,
                    "roi_type": region.roi_type,
                    "coordinates": region.coordinates  # Only include coordinates
                })
            
            # Get dependencies by querying the through model directly
            dependencies = []
            dependency_relations = CamerasLayersConfigurationDependency.objects.filter(current_layer=config_layer)
            
            for dep_relation in dependency_relations:
                dependency = dep_relation.dependency_layer
                dependencies.append({
                    'id': str(dependency.id),
                    'name': dependency.name,
                    'conditions': dep_relation.conditions  # Include conditions in the response
                })
                
            response_data['dependencies'] = dependencies
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating configuration layer: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def layer_detail(request, camera_id, layer_id):
    """Retrieve, update or delete a configuration layer"""
    try:
        camera = Camera.objects.get(id=camera_id)
        config_layer = CamerasLayersConfiguration.objects.get(id=layer_id, camera=camera)
    except Camera.DoesNotExist:
        return Response({"error": "Camera not found"}, status=status.HTTP_404_NOT_FOUND)
    except CamerasLayersConfiguration.DoesNotExist:
        return Response({"error": "Configuration layer not found"}, status=status.HTTP_404_NOT_FOUND)
    
    # Get the associated base Layer
    layer = config_layer.layers
    
    if request.method == 'GET':
        # Build response data
        layer_data = {
            'id': str(config_layer.id),
            'name': config_layer.name,
            'camera': {
                'id': str(camera.id),
                'name': camera.name
            },
            'layers': {
                'id': str(layer.id),
                'name': layer.name,
                'layer_type': layer.layer_type,
                'function_name': layer.function_name,
                'created_at': layer.created_at,
                'updated_at': layer.updated_at
            },
            'enabled': config_layer.enabled,
            'color': config_layer.color,
            'configuration': config_layer.configuration,
            'created_at': config_layer.created_at,
            'updated_at': config_layer.updated_at
        }
        
        # Get all regions associated with this layer configuration
        regions_list = list(config_layer.regions.all())
        
        # Add regions to the response
        layer_data['regions'] = []
        
        for region in regions_list:
            layer_data["regions"].append({
                "id": str(region.id),
                "name": region.name,
                "roi_type": region.roi_type,
                "coordinates": region.coordinates
            })
        
        # Get dependencies by querying the through model directly
        dependencies = []
        dependency_relations = CamerasLayersConfigurationDependency.objects.filter(current_layer=config_layer)
        
        for dep_relation in dependency_relations:
            dependency = dep_relation.dependency_layer
            dependencies.append({
                'id': str(dependency.id),
                'name': dependency.name,
                'conditions': dep_relation.conditions
            })
            
        layer_data['dependencies'] = dependencies
        
        return Response(layer_data)
    
    elif request.method == 'PUT':
        # Update configuration layer
        data = request.data
        
        try:
            # Update basic fields
            if 'name' in data:
                config_layer.name = data['name']
            
            if 'enabled' in data:
                config_layer.enabled = data['enabled']
            
            if 'color' in data:
                config_layer.color = data['color']
            
            if 'configuration' in data:
                config_layer.configuration = data['configuration']
            
            # Handle multiple regions in region_ids array
            if 'region_ids' in data:
                # Clear existing regions
                config_layer.regions.clear()
                
                if data['region_ids']:
                    for region_id in data['region_ids']:
                        try:
                            region = RegionOfInterest.objects.get(id=region_id)
                            config_layer.regions.add(region)
                        except RegionOfInterest.DoesNotExist:
                            logger.warning(f"Region {region_id} not found, skipping")
            
            # Update dependencies if provided
            if 'dependencies' in data:
                # Clear existing dependencies
                config_layer.dependencies.clear()
                
                # Add new dependencies
                if data['dependencies']:
                    # First clear existing dependencies
                    CamerasLayersConfigurationDependency.objects.filter(current_layer=config_layer).delete()
                    
                    # Then add new dependencies
                    for dep_id in data['dependencies']:
                        try:
                            dep_config_layer = CamerasLayersConfiguration.objects.get(id=dep_id)
                            if dep_config_layer.id != config_layer.id:  # Prevent self-dependency
                                # Create the dependency through model directly with conditions
                                CamerasLayersConfigurationDependency.objects.create(
                                    current_layer=config_layer,
                                    dependency_layer=dep_config_layer,
                                    conditions={"default": True}
                                )
                                logger.info(f"Updated dependency with conditions: {config_layer.id} -> {dep_config_layer.id}")
                        except CamerasLayersConfiguration.DoesNotExist:
                            logger.warning(f"Dependency configuration layer {dep_id} not found")
                        except Exception as e:
                            logger.error(f"Error updating dependency: {str(e)}")
            
            # Save changes
            config_layer.save()
            
            # Build response data (same structure as GET)
            layer_data = {
                'id': str(config_layer.id),
                'name': config_layer.name,
                'camera': {
                    'id': str(camera.id),
                    'name': camera.name
                },
                'layers': {
                    'id': str(layer.id),
                    'name': layer.name,
                    'layer_type': layer.layer_type,
                    'function_name': layer.function_name,
                    'created_at': layer.created_at,
                    'updated_at': layer.updated_at
                },
                'enabled': config_layer.enabled,
                'color': config_layer.color,
                'configuration': config_layer.configuration,
                'created_at': config_layer.created_at,
                'updated_at': config_layer.updated_at
            }
            
            # Get all regions associated with this layer configuration
            regions_list = list(config_layer.regions.all())
            
            # Add regions to the response
            layer_data['regions'] = []
            
            for region in regions_list:
                logger.info(f"Region {region.id} coordinates: {region.coordinates}")
                roi_type = getattr(region, 'roi_type', None)
                
                layer_data["regions"].append({
                    "id": str(region.id),
                    "name": region.name,
                    "roi_type": roi_type,
                    "coordinates": region.coordinates  # Only include coordinates
                })
            
            # Get dependencies by querying the through model directly
            dependencies = []
            dependency_relations = CamerasLayersConfigurationDependency.objects.filter(current_layer=config_layer)
            
            for dep_relation in dependency_relations:
                dependency = dep_relation.dependency_layer
                dependencies.append({
                    'id': str(dependency.id),
                    'name': dependency.name,
                    'conditions': dep_relation.conditions  # Include conditions in the response
                })
                
            layer_data['dependencies'] = dependencies
            
            return Response(layer_data)
        
        except Exception as e:
            logger.error(f"Error updating configuration layer: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Delete configuration layer
        try:
            config_layer.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error deleting configuration layer: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
