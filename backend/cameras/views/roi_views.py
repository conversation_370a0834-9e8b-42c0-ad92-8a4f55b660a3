"""
Region of Interest (ROI) view functions
"""
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from ..models import Camera, CamerasLayersConfiguration, RegionOfInterest

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def roi_list(request):
    """List all regions of interest for all cameras or create a new one"""
    
    if request.method == 'GET':
        # List all ROIs for all cameras
        regions = RegionOfInterest.objects.all()
        data = []
        for region in regions:
            data.append({
                'id': str(region.id),
                'name': region.name,
                'description': region.description,
                'coordinates': region.coordinates,
                'roi_type': region.roi_type,
                'is_active': region.is_active,
                'alerts_category': region.alerts_category,
                'pose_keypoints': region.pose_keypoints,
                'requirement': region.requirement,
                'criteria': region.criteria,
                'created_at': region.created_at,
                'updated_at': region.updated_at,
            })
        return Response(data)
    
    elif request.method == 'POST':
        # Create a new ROI
        try:
            data = request.data.copy()
            data['created_by'] = request.user.id
            
            # Validate the points
            if 'coordinates' not in data or not isinstance(data['coordinates'], list):
                return Response({"error": "Coordinates must be a list of coordinates"}, 
                               status=status.HTTP_400_BAD_REQUEST)
                
            if len(data['coordinates']) == 2:
                data['roi_type'] = 'line'
                # Auto-set requirement based on region type if not specified
                if 'requirement' not in data:
                    data['requirement'] = 'above the line'
            else:
                data['roi_type'] = 'region'
                # Auto-set requirement based on region type if not specified
                if 'requirement' not in data:
                    data['requirement'] = 'inside the region'
                    
            # Validate keypoints if provided
            if 'pose_keypoints' in data:
                valid_keypoints = [choice[0] for choice in RegionOfInterest.KEYPOINT_CHOICES]
                for keypoint in data['pose_keypoints']:
                    if keypoint not in valid_keypoints:
                        return Response({"error": f"Invalid keypoint: {keypoint}. Must be one of: {valid_keypoints}"}, 
                                       status=status.HTTP_400_BAD_REQUEST)
                        
            # Validate requirement if provided
            if 'requirement' in data:
                valid_requirements = [choice[0] for choice in RegionOfInterest.REQUIREMENT_CHOICES]
                if data['requirement'] not in valid_requirements:
                    return Response({"error": f"Invalid requirement: {data['requirement']}. Must be one of: {valid_requirements}"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                    
                # Check requirement matches region type
                if data['roi_type'] == 'line' and data['requirement'] not in ['above the line', 'below the line']:
                    return Response({"error": "For line regions, requirement must be 'above the line' or 'below the line'"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                if data['roi_type'] == 'region' and data['requirement'] not in ['inside the region', 'outside the region']:
                    return Response({"error": "For polygon regions, requirement must be 'inside the region' or 'outside the region'"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                    
            # Validate alerts_category if provided
            if 'alerts_category' in data:
                valid_categories = [choice[0] for choice in RegionOfInterest.ALERT_CATEGORY_CHOICES]
                if data['alerts_category'] not in valid_categories:
                    return Response({"error": f"Invalid alerts_category: {data['alerts_category']}. Must be one of: {valid_categories}"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
            
            # Create the ROI
            region = RegionOfInterest.objects.create(
                name=data.get('name', f"Region {RegionOfInterest.objects.count() + 1}"),
                description=data.get('description', ''),
                coordinates=data['coordinates'],
                roi_type=data['roi_type'],
                is_active=data.get('is_active', True),
                alerts_category=data.get('alerts_category'),
                pose_keypoints=data.get('pose_keypoints'),
                requirement=data.get('requirement'),
                created_by=request.user if request.user.is_authenticated else None
            )
            
            return Response({
                'id': str(region.id),
                'name': region.name,
                'description': region.description,
                'coordinates': region.coordinates,
                'roi_type': region.roi_type,
                'is_active': region.is_active,
                'alerts_category': region.alerts_category,
                'pose_keypoints': region.pose_keypoints,
                'requirement': region.requirement,
                'criteria': region.criteria,
                'created_at': region.created_at,
                'updated_at': region.updated_at,
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating ROI: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def roi_detail(request, roi_id):
    """Retrieve, update or delete a region of interest"""
    try:
        region = RegionOfInterest.objects.get(id=roi_id)
    except RegionOfInterest.DoesNotExist:
        return Response({"error": "Region of interest not found"}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        # Retrieve ROI details
        data = {
            'id': str(region.id),
            'name': region.name,
            'description': region.description,
            'coordinates': region.coordinates,
            'roi_type': region.roi_type,
            'is_active': region.is_active,
            'alerts_category': region.alerts_category,
            'pose_keypoints': region.pose_keypoints,
            'requirement': region.requirement,
            'criteria': region.criteria,
            'created_at': region.created_at,
            'updated_at': region.updated_at,
        }
        return Response(data)
    
    elif request.method == 'PUT':
        # Update ROI
        try:
            data = request.data
            
            # Update fields
            if 'name' in data:
                region.name = data['name']
                
            if 'description' in data:
                region.description = data['description']
            
            if 'coordinates' in data:
                if not isinstance(data['coordinates'], list):
                    return Response({"error": "Coordinates must be a list of coordinates"}, 
                                  status=status.HTTP_400_BAD_REQUEST)
                
                region.coordinates = data['coordinates']
                if len(data['coordinates']) == 2:
                    region.roi_type = 'line'
                else:
                    region.roi_type = 'region'
                    
            if 'is_active' in data:
                region.is_active = data['is_active']
                
            if 'alerts_category' in data:
                valid_categories = [choice[0] for choice in RegionOfInterest.ALERT_CATEGORY_CHOICES]
                if data['alerts_category'] not in valid_categories and data['alerts_category'] is not None:
                    return Response({"error": f"Invalid alerts_category: {data['alerts_category']}. Must be one of: {valid_categories}"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                region.alerts_category = data['alerts_category']
                
            if 'pose_keypoints' in data:
                # Validate keypoints
                if data['pose_keypoints'] is not None:
                    valid_keypoints = [choice[0] for choice in RegionOfInterest.KEYPOINT_CHOICES]
                    for keypoint in data['pose_keypoints']:
                        if keypoint not in valid_keypoints:
                            return Response({"error": f"Invalid keypoint: {keypoint}. Must be one of: {valid_keypoints}"}, 
                                           status=status.HTTP_400_BAD_REQUEST)
                region.pose_keypoints = data['pose_keypoints']
                
            if 'requirement' in data:
                valid_requirements = [choice[0] for choice in RegionOfInterest.REQUIREMENT_CHOICES]
                if data['requirement'] not in valid_requirements and data['requirement'] is not None:
                    return Response({"error": f"Invalid requirement: {data['requirement']}. Must be one of: {valid_requirements}"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                    
                # Check requirement matches region type
                if region.roi_type == 'line' and data['requirement'] not in ['above the line', 'below the line'] and data['requirement'] is not None:
                    return Response({"error": "For line regions, requirement must be 'above the line' or 'below the line'"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                if region.roi_type == 'region' and data['requirement'] not in ['inside the region', 'outside the region'] and data['requirement'] is not None:
                    return Response({"error": "For polygon regions, requirement must be 'inside the region' or 'outside the region'"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                region.requirement = data['requirement']
            
            region.save()
            
            return Response({
                'id': str(region.id),
                'name': region.name,
                'description': region.description,
                'coordinates': region.coordinates,
                'roi_type': region.roi_type,
                'is_active': region.is_active,
                'alerts_category': region.alerts_category,
                'pose_keypoints': region.pose_keypoints,
                'requirement': region.requirement,
                'criteria': region.criteria,
                'created_at': region.created_at,
                'updated_at': region.updated_at,
            })
            
        except Exception as e:
            logger.error(f"Error updating ROI: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Delete ROI
        region.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

"""
List all regions of interest for a specific camera
"""
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def roi_camera_list(request, camera_id):
    try:
        camera = Camera.objects.get(id=camera_id)
    except Camera.DoesNotExist:
        return Response({"error": "Camera not found"}, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        # Get all layer configurations for this camera
        camera_layer_configs = CamerasLayersConfiguration.objects.filter(camera=camera)
        
        # Use a set to keep track of unique region IDs we've seen
        unique_region_ids = set()
        regions_data = []
        
        # For each layer configuration, get its associated regions
        for layer_config in camera_layer_configs:
            # Get regions associated with this layer configuration
            regions = layer_config.regions.all()
            
            for region in regions:
                # Only add each region once
                if str(region.id) not in unique_region_ids:
                    unique_region_ids.add(str(region.id))
                    
                    # Add region data with association details
                    try:
                        # Safe approach to get the base layer ID
                        base_layer_id = None
                        if hasattr(layer_config, 'layers'):
                            try:
                                base_layer = layer_config.layers
                                if base_layer:
                                    base_layer_id = str(base_layer.id)
                            except Exception as e:
                                logger.error(f"Error accessing layers attribute: {str(e)}")
                        
                        regions_data.append({
                            'id': str(region.id),
                            'name': region.name,
                            'description': region.description,
                            'coordinates': region.coordinates,
                            'roi_type': region.roi_type,
                            'is_active': region.is_active,
                            'alerts_category': region.alerts_category,
                            'pose_keypoints': region.pose_keypoints,
                            'requirement': region.requirement,
                            'criteria': region.criteria,
                            'created_at': region.created_at,
                            'updated_at': region.updated_at,
                            'camera_association': {
                                'base_layer_id': base_layer_id,
                                'layer_config_id': str(layer_config.id),
                                'enabled': layer_config.enabled
                            }
                        })
                    except Exception as e:
                        logger.error(f"Error building region data for {region.id}: {str(e)}")
        
        return Response(regions_data)