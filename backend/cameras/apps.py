from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class CamerasConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'cameras'

    def ready(self):
        """
        Start the event stream manager when the Django app is ready.
        This ensures the background Redis listener runs as a service.
        """
        try:
            from .event_stream_manager import start_event_stream
            # Start the event stream manager
            stream_manager = start_event_stream()
            logger.info("Event stream manager started successfully")
        except Exception as e:
            logger.error(f"Failed to start event stream manager: {e}")
