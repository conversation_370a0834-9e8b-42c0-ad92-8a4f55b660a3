# Generated by Django 5.1.1 on 2025-05-26 05:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0017_remove_layer_dependencies_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='cameraevent',
            name='tags',
        ),
        migrations.AlterModelOptions(
            name='eventtag',
            options={},
        ),
        migrations.RemoveField(
            model_name='eventtag',
            name='category',
        ),
        migrations.RemoveField(
            model_name='eventtag',
            name='description',
        ),
        migrations.RemoveField(
            model_name='eventtag',
            name='name',
        ),
        migrations.RemoveField(
            model_name='eventtag',
            name='use_count',
        ),
        migrations.AddField(
            model_name='eventtag',
            name='camera',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tags', to='cameras.camera'),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='camera_layer_config',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tags', to='cameras.cameraslayersconfiguration'),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='detection_results',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='frame_key',
            field=models.CharField(blank=True, db_index=True, max_length=400, null=True),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='layer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tags', to='cameras.layer'),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='eventtag',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='eventtag',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.DeleteModel(
            name='EventTagRelation',
        ),
    ]
