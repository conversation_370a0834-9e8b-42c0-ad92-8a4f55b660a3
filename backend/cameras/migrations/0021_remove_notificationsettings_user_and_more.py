# Generated by Django 5.1.1 on 2025-05-27 12:54

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0020_alter_cameraevent_event_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notificationsettings',
            name='user',
        ),
        migrations.RemoveField(
            model_name='trainingitem',
            name='event',
        ),
        migrations.RemoveField(
            model_name='trainingitem',
            name='reviewed_by',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='frame_bytes',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='layer',
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='camera_layer_config',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='cameras.cameraslayersconfiguration'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='cameraevent',
            name='timestamp',
            field=models.DateTimeField(auto_now_add=True, db_index=True, help_text='Timestamp when the event was detected, matches frame timestamp'),
        ),
        migrations.CreateModel(
            name='Frame',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(db_index=True, help_text='Timestamp when the frame was captured, timestamp matches event timestamp')),
                ('frame_bytes', models.BinaryField(blank=True, null=True)),
                ('frame_number', models.BigIntegerField(blank=True, help_text='Sequential frame number from the stream', null=True)),
                ('width', models.IntegerField(blank=True, null=True)),
                ('height', models.IntegerField(blank=True, null=True)),
                ('format', models.CharField(blank=True, help_text='Image format (JPEG, PNG, etc.)', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='frames', to='cameras.camera')),
            ],
            options={
                'get_latest_by': 'timestamp',
            },
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='frame',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='cameras.frame'),
        ),
        migrations.DeleteModel(
            name='EventTag',
        ),
        migrations.DeleteModel(
            name='NotificationSettings',
        ),
        migrations.DeleteModel(
            name='TrainingItem',
        ),
        migrations.AddIndex(
            model_name='frame',
            index=models.Index(fields=['camera', 'timestamp'], name='cameras_fra_camera__3014f9_idx'),
        ),
    ]
