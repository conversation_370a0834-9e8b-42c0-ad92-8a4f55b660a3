# Generated by Django 5.1.1 on 2025-04-28 05:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Camera',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('location', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('rtsp_url', models.CharField(help_text='RTSP URL for the camera stream', max_length=500)),
                ('username', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('password', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('error', 'Error')], default='offline', max_length=20)),
                ('last_status_check', models.DateTimeField(auto_now=True)),
                ('stream_fps', models.IntegerField(default=1, help_text='Frames per second to capture from RTSP stream')),
                ('stream_id', models.CharField(blank=True, help_text='ID for this stream in the RTSP processor', max_length=100, null=True)),
                ('has_ptz', models.BooleanField(default=False, help_text='Does camera support pan/tilt/zoom?')),
                ('has_audio', models.BooleanField(default=False, help_text='Does camera include audio?')),
                ('resolution', models.CharField(blank=True, max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CameraEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('motion', 'Motion Detected'), ('person', 'Person Detected'), ('object', 'Object Detected'), ('tamper', 'Camera Tampering'), ('offline', 'Camera Offline')], max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('confidence', models.FloatField(default=0.0, help_text='Confidence score of the detection (0-1)')),
                ('details', models.JSONField(blank=True, help_text='Additional details about the event', null=True)),
                ('thumbnail_path', models.CharField(blank=True, help_text='Path to event thumbnail image', max_length=500, null=True)),
                ('video_clip_path', models.CharField(blank=True, help_text='Path to event video clip', max_length=500, null=True)),
                ('reviewed', models.BooleanField(default=False)),
                ('review_timestamp', models.DateTimeField(blank=True, null=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='cameras.camera')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_events', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
