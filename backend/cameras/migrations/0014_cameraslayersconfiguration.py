# Generated by Django 5.1.1 on 2025-05-25 09:12

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0013_camera_required_analytics'),
    ]

    operations = [
        migrations.CreateModel(
            name='CamerasLayersConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('configuration', models.JSONField(help_text='Configuration Dictionary')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='layers_configuration', to='cameras.camera')),
                ('layers', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='layers_configuration', to='cameras.layer')),
            ],
        ),
    ]
