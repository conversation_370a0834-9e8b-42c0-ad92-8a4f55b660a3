# Generated by Django 5.1.1 on 2025-06-02 07:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0030_regionofinterest_pose_keypoints_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='cameraevent',
            name='camera',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='in_training_queue',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='region',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='thumbnail_path',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='training_priority',
        ),
        migrations.RemoveField(
            model_name='cameraevent',
            name='video_clip_path',
        ),
        migrations.AlterField(
            model_name='regionofinterest',
            name='criteria',
            field=models.JSONField(blank=True, help_text='Legacy field for criteria. Still used, save method in this class automatically populates this field with correctly formatted requirements and keypoints.', null=True),
        ),
    ]
