# Generated by Django 5.1.1 on 2025-06-19 03:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0034_cameraevent_alert_received_timestamp_and_more'),
        ('cameras', '0034_permanentcameraoverlay'),
    ]

    operations = [
        migrations.AlterField(
            model_name='camera',
            name='stream_fps',
            field=models.FloatField(default=1.0, help_text='Frames per second to capture from RTSP stream'),
        ),
    ]
