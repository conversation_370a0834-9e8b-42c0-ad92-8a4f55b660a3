# Generated by Django 5.1.1 on 2025-05-02 09:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0002_eventtag_alter_cameraevent_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='camera',
            name='encoding_format',
            field=models.CharField(choices=[('jpg', 'JPEG'), ('png', 'PNG'), ('webp', 'WebP')], default='jpg', help_text='Image format for stream frames', max_length=10),
        ),
        migrations.AddField(
            model_name='camera',
            name='quality',
            field=models.IntegerField(default=2, help_text='Image quality (1-31, lower is better for JPEG)'),
        ),
    ]
