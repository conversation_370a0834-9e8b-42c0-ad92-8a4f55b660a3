# Generated by Django 5.1.1 on 2025-06-18 01:32

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0033_cameraevent_event_severity'),
    ]

    operations = [
        migrations.CreateModel(
            name='PermanentCameraOverlay',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('coordinates', models.J<PERSON><PERSON>ield(help_text='Array of points defining the overlay, has both x and y which define the absolute coordinates and normalizedX/Y which defines the relative coordinates per image frame')),
                ('text', models.CharField(blank=True, help_text='Text to display in the overlay (if applicable)', max_length=255, null=True)),
                ('color', models.CharField(default='#FF0000', help_text='Color of the overlay (hex code or color name)', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='overlays', to='cameras.camera')),
            ],
        ),
    ]
