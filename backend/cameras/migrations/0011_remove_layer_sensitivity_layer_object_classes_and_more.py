# Generated by Django 5.1.1 on 2025-05-23 04:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0010_remove_camera_stream_url'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='layer',
            name='sensitivity',
        ),
        migrations.AddField(
            model_name='layer',
            name='object_classes',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='layer',
            name='type',
            field=models.CharField(choices=[('seg', 'Segmentation'), ('pose', 'Coarse Pose Detection')], default='custom', max_length=50),
        ),
    ]
