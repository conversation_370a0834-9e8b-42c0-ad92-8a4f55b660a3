# Generated by Django 5.1.1 on 2025-05-31 19:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0028_remove_regionofinterest_camera'),
    ]

    operations = [
        migrations.AddField(
            model_name='regionofinterest',
            name='alerts_category',
            field=models.CharField(blank=True, db_index=True, help_text='Category of alerts to be triggered for this region. (Warning, Critical)', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='regionofinterest',
            name='criteria',
            field=models.JSONField(blank=True, help_text='Criteria for triggering alerts for this region', null=True),
        ),
        migrations.AlterField(
            model_name='regionofinterest',
            name='coordinates',
            field=models.JSONField(help_text='Array of points defining the region, has both x and y which     define the absolute coordinates and normalizedX/Y which defines the relative coordiantes per image frame'),
        ),
    ]
