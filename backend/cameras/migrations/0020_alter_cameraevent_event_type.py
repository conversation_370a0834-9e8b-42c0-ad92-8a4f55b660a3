# Generated by Django 5.1.1 on 2025-05-26 09:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0019_cameraevent_frame_bytes'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cameraevent',
            name='event_type',
            field=models.CharField(blank=True, choices=[('coveredPerson', 'Suspicious Person (Covered Face & Clothing)'), ('weaponDetection', 'Person Carrying Offensive Weapon'), ('oversizeObject', 'Person Carrying Oversize Object'), ('unattendedObject', 'Unattended Object Detection'), ('areaBreach', 'Area Breach (Scaling Fence)'), ('custom', 'Custom Detection Rule')], max_length=50, null=True),
        ),
    ]
