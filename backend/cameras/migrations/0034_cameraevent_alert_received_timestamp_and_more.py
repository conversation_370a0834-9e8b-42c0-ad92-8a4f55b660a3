# Generated by Django 5.1.1 on 2025-06-19 01:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0033_cameraevent_event_severity'),
    ]

    operations = [
        migrations.AddField(
            model_name='cameraevent',
            name='alert_received_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='camera_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='detection_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='frame_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='preprocessing_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='socc_acknowledged_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='socc_notification_to_tso_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='tso_acknowledged_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cameraevent',
            name='event_type',
            field=models.CharField(blank=True, choices=[('suspicious_person', 'Suspicious Person'), ('offensive_weapon', 'Offensive Weapon'), ('oversized_object', 'Person Carrying Oversize Object'), ('unattended_object', 'Unattended Object Detection'), ('scaling_gantry', 'Scaling Gantry'), ('custom', 'Custom Detection Rule')], max_length=50, null=True),
        ),
    ]
