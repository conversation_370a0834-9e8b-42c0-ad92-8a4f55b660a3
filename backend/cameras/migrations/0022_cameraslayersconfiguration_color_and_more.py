# Generated by Django 5.1.1 on 2025-05-27 13:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0021_remove_notificationsettings_user_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='cameraslayersconfiguration',
            name='color',
            field=models.CharField(default='#777777', max_length=7),
        ),
        migrations.AddField(
            model_name='cameraslayersconfiguration',
            name='enabled',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='cameraslayersconfiguration',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='layers_configuration', to='cameras.regionofinterest'),
        ),
    ]
