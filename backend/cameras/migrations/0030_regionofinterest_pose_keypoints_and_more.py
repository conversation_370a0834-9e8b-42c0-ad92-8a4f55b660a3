# Generated by Django 5.1.1 on 2025-06-02 05:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0029_regionofinterest_alerts_category_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='regionofinterest',
            name='pose_keypoints',
            field=models.JSONField(blank=True, help_text='List of keypoints used for pose detection criteria', null=True),
        ),
        migrations.AddField(
            model_name='regionofinterest',
            name='requirement',
            field=models.CharField(blank=True, choices=[('above the line', 'Above the line'), ('below the line', 'Below the line'), ('inside the region', 'Inside the region'), ('outside the region', 'Outside the region')], help_text='Requirement for triggering alerts (e.g., above the line, inside the region)', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='regionofinterest',
            name='alerts_category',
            field=models.Char<PERSON>ield(blank=True, choices=[('Warning', 'Warning'), ('Critical', 'Critical')], db_index=True, help_text='Category of alerts to be triggered for this region', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='regionofinterest',
            name='coordinates',
            field=models.JSONField(help_text='Array of points defining the region, has both x and y which define the absolute coordinates and normalizedX/Y which defines the relative coordiantes per image frame'),
        ),
        migrations.AlterField(
            model_name='regionofinterest',
            name='criteria',
            field=models.JSONField(blank=True, help_text='Legacy field for criteria. Use pose_keypoints and requirement instead.', null=True),
        ),
    ]
