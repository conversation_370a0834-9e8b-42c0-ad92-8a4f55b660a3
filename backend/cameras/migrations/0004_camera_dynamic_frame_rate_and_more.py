# Generated by Django 5.1.1 on 2025-05-02 09:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0003_camera_encoding_format_camera_quality'),
    ]

    operations = [
        migrations.AddField(
            model_name='camera',
            name='dynamic_frame_rate',
            field=models.BooleanField(default=True, help_text='Dynamically adjust frame rate based on activity'),
        ),
        migrations.AlterField(
            model_name='camera',
            name='encoding_format',
            field=models.CharField(choices=[('H.264', 'H.264'), ('H.265', 'H.265'), ('jpg', 'JPEG'), ('png', 'PNG'), ('webp', 'WebP')], default='H.264', help_text='Video encoding format', max_length=10),
        ),
        migrations.AlterField(
            model_name='camera',
            name='quality',
            field=models.IntegerField(default=2, help_text='Video quality (1-31, lower is better)'),
        ),
    ]
