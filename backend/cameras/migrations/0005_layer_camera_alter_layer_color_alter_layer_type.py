# Generated by Django 5.1.1 on 2025-05-05 07:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0004_camera_dynamic_frame_rate_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='layer',
            name='camera',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='layers', to='cameras.camera'),
        ),
        migrations.AlterField(
            model_name='layer',
            name='color',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='layer',
            name='type',
            field=models.CharField(choices=[('coveredPerson', 'Suspicious Person (Covered Face & Clothing)'), ('weaponDetection', 'Person Carrying Offensive Weapon'), ('oversizeObject', 'Person Carrying Oversize Object'), ('unattendedObject', 'Unattended Object Detection'), ('areaBreach', 'Area Breach (Scaling Fence)'), ('custom', 'Custom Detection Rule')], default='custom', max_length=50),
        ),
    ]
