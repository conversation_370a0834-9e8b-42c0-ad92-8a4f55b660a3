# Generated by Django 5.1.1 on 2025-05-26 04:48

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0016_remove_cameraslayersconfiguration_layer_index_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='layer',
            name='dependencies',
        ),
        migrations.CreateModel(
            name='CamerasLayersConfigurationDependency',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('conditions', models.JSONField(help_text='Conditions necessary for the current layer to be run')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_layer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependency_links', to='cameras.cameraslayersconfiguration')),
                ('dependency_layer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependent_links', to='cameras.cameraslayersconfiguration')),
            ],
        ),
        migrations.AddField(
            model_name='cameraslayersconfiguration',
            name='dependencies',
            field=models.ManyToManyField(related_name='dependents', through='cameras.CamerasLayersConfigurationDependency', to='cameras.cameraslayersconfiguration'),
        ),
    ]
