# Generated by Django 5.1.1 on 2025-04-30 09:11

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EventTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(blank=True, max_length=50, null=True)),
                ('use_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-use_count'],
            },
        ),
        migrations.AlterModelOptions(
            name='cameraevent',
            options={'ordering': ['-timestamp']},
        ),
        migrations.RenameField(
            model_name='cameraevent',
            old_name='reviewed',
            new_name='in_training_queue',
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='is_reviewed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='is_suspicious',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='review_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='training_priority',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='cameraevent',
            name='details',
            field=models.JSONField(blank=True, help_text='Additional details about the detection', null=True),
        ),
        migrations.AlterField(
            model_name='cameraevent',
            name='event_type',
            field=models.CharField(choices=[('coveredPerson', 'Suspicious Person (Covered Face & Clothing)'), ('weaponDetection', 'Person Carrying Offensive Weapon'), ('oversizeObject', 'Person Carrying Oversize Object'), ('unattendedObject', 'Unattended Object Detection'), ('areaBreach', 'Area Breach (Scaling Fence)'), ('custom', 'Custom Detection Rule')], max_length=50),
        ),
        migrations.CreateModel(
            name='EventTagRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_tags', to=settings.AUTH_USER_MODEL)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tagged_items', to='cameras.cameraevent')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tagged_events', to='cameras.eventtag')),
            ],
            options={
                'unique_together': {('event', 'tag')},
            },
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='tags',
            field=models.ManyToManyField(blank=True, through='cameras.EventTagRelation', to='cameras.eventtag'),
        ),
        migrations.CreateModel(
            name='Layer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('type', models.CharField(choices=[('coveredPerson', 'Suspicious Person (Covered Face & Clothing)'), ('weaponDetection', 'Person Carrying Offensive Weapon'), ('oversizeObject', 'Person Carrying Oversize Object'), ('unattendedObject', 'Unattended Object Detection'), ('areaBreach', 'Area Breach (Scaling Fence)'), ('custom', 'Custom Detection Rule')], max_length=50)),
                ('enabled', models.BooleanField(default=True)),
                ('color', models.CharField(help_text='Hex color code for this layer', max_length=20)),
                ('sensitivity', models.IntegerField(default=75, help_text='Detection sensitivity (0-100)')),
                ('options', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_layers', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='layer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='cameras.layer'),
        ),
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notify_on_covered_person', models.BooleanField(default=True)),
                ('notify_on_weapon_detection', models.BooleanField(default=True)),
                ('notify_on_oversize_object', models.BooleanField(default=False)),
                ('notify_on_unattended_object', models.BooleanField(default=False)),
                ('notify_on_area_breach', models.BooleanField(default=True)),
                ('notify_on_custom', models.BooleanField(default=False)),
                ('notify_on_suspicious_only', models.BooleanField(default=False)),
                ('email_notifications', models.BooleanField(default=True)),
                ('browser_notifications', models.BooleanField(default=True)),
                ('min_confidence_threshold', models.FloatField(default=0.6)),
                ('quiet_hours_enabled', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(blank=True, null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RegionOfInterest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('coordinates', models.JSONField(help_text='Array of points defining the region')),
                ('roi_type', models.CharField(choices=[('line', 'Line Detection'), ('region', 'Region Detection')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='cameras.camera')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_regions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='cameraevent',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='cameras.regionofinterest'),
        ),
        migrations.CreateModel(
            name='TrainingItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('submitted', 'Submitted for Training')], default='pending', max_length=20)),
                ('is_suspicious', models.BooleanField(default=False)),
                ('confidence', models.FloatField(default=1.0, help_text='Reviewer confidence (0-1)')),
                ('notes', models.TextField(blank=True, null=True)),
                ('tags', models.JSONField(blank=True, null=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('external_reference_id', models.CharField(blank=True, max_length=255, null=True)),
                ('event', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='training_item', to='cameras.cameraevent')),
                ('reviewed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_training_items', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-reviewed_at'],
            },
        ),
    ]
