# Generated by Django 5.1.1 on 2025-05-23 10:30

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0011_remove_layer_sensitivity_layer_object_classes_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComputerVisionModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('model_type', models.CharField(choices=[('yolov8-seg', 'YOLOv8 Segmentation'), ('yolov8-pose', 'YOLOv8 Pose'), ('yolov10m', 'YOLOv10 Medium')], max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='layer',
            name='type',
        ),
        migrations.AddField(
            model_name='layer',
            name='dependencies',
            field=models.ManyToManyField(blank=True, related_name='dependent_layers', to='cameras.layer'),
        ),
        migrations.AddField(
            model_name='layer',
            name='function_name',
            field=models.CharField(blank=True, help_text='Name of the function to execute for this layer', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='layer',
            name='layer_type',
            field=models.IntegerField(choices=[(1, 'Layer 1'), (2, 'Layer 2'), (3, 'Layer 3')], default=1, help_text='Numeric layer type (1, 2, 3)'),
        ),
        migrations.AddField(
            model_name='layer',
            name='cv_model',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='layers', to='cameras.computervisionmodel'),
        ),
    ]
