# Generated by Django 5.1.1 on 2025-05-06 07:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cameras', '0008_remove_camera_has_audio_remove_camera_has_ptz_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='camera',
            name='stream_url',
            field=models.CharField(blank=True, help_text='Browser-compatible URL for accessing the camera stream', max_length=500, null=True),
        ),
    ]
