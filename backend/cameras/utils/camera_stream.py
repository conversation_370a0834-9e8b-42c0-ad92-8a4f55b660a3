#!/usr/bin/env python3
"""
Stream MJPEG frames from an RTSP camera.

Adds:
  • CAMERA_ID  – arbitrary label you choose.
  • frame_id   – monotonically increasing counter.
  • Timestamp  – wall-clock time in local timezone.
"""

from hmac import new
import subprocess
import os
import datetime
import time
import threading
import urllib.request
import urllib.parse
import urllib.error
import json
import time
import requests
from typing import Optional, Generator, Tuple, Dict, Any, List
from PIL import Image, ImageDraw
import io
from backend.settings import REDIS
import cv2
import pytz
import numpy as np
import base64
from utils.redis_connection import CameraCache
import logging

logger = logging.getLogger(__name__)

# Set the timezone to SGT
os.environ['TZ'] = 'Asia/Singapore'
time.tzset()

class CameraStream:
    def __init__(
        self,
        rtsp_url: str,
        camera_id: str = "Camera",
        fps: int = 10,
        nvr_params: Dict[str, Any] = None,
    ):
        """
        Initialize the camera stream.
        
        Args:
            rtsp_url: The RTSP URL of the camera
            camera_id: Identifier for the camera
            fps: Frames per second to capture
            nvr_params: Optional parameters for direct NVR connection
                - host: NVR host address
                - port: NVR API port
                - username: NVR username
                - password: NVR password
                - use_ssl: Whether to use HTTPS (default: False)
                - channel: Camera channel number in NVR
        """
        self.rtsp_url = rtsp_url
        self.camera_id = camera_id
        self.fps = fps
        self.is_running = False
        self.nvr_params = nvr_params or {}

    def stop(self):
        """Stop the camera stream and cleanup resources."""
        if self.is_running:
            logger.info(f"Stopping stream for camera {self.camera_id}")
            self.is_running = False

    def _is_frame_corrupted(self, frame: np.ndarray) -> bool:
        """
        Check for image tearing by analyzing the last row of pixels.
        Returns True if the frame is likely corrupted, False otherwise.
        """
        if frame is None:
            return True
        
        # Heuristic: If the last row is mostly one color, it's likely a tear.
        last_row = frame[-1, :]
        unique_pixels, counts = np.unique(last_row, axis=0, return_counts=True)
        
        if not counts.any():
            return False

        most_frequent_count = np.max(counts)
        total_pixels = last_row.shape[0]

        # If >90% of pixels in the last row are identical, flag as corrupt.
        if total_pixels > 0 and (most_frequent_count / total_pixels) > 0.90:
            logger.warning(
                f"Corruption detected: {most_frequent_count}/{total_pixels} pixels in the last row are identical. Skipping frame."
            )
            return True
            
        return False

    def direct_stream(self) -> Generator[bytes, None, None]:
        """Stream directly from a camera using OpenCV."""
        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp"
        if "streaming" in str(self.rtsp_url).lower() and os.environ.get('STREAMING_START_TIME'):
            start_time = os.environ.get('STREAMING_START_TIME')
            self.rtsp_url = f"{self.rtsp_url}?starttime={start_time}"
        else:
            self.rtsp_url = f"{self.rtsp_url}"

        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        if cap.isOpened():
            self.is_running = True
            frame_count = 0
            
            try:
                while self.is_running:
                    ret, frame = cap.read()
                    
                    if not ret:
                        logger.warning("Failed to grab frame, attempting to reconnect...")
                        cap.release()
                        time.sleep(2)
                        cap = cv2.VideoCapture(self.rtsp_url)
                        if not cap.isOpened():
                            logger.error("Failed to reconnect to the stream.")
                            break
                        continue

                    if self._is_frame_corrupted(frame):
                        continue

                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 90]
                    result, jpg_buffer = cv2.imencode('.jpg', frame, encode_param)

                    if not result:
                        logger.warning("Failed to encode frame to JPEG")
                        continue
                    
                    jpg_bytes = jpg_buffer.tobytes()
                    
                    frame_count += 1
                    if frame_count % (self.fps * 10) == 0:
                        logger.debug(f"Processed {frame_count} frames")

                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n'
                           b'Content-Length: ' + str(len(jpg_bytes)).encode() + b'\r\n\r\n' + 
                           jpg_bytes + b'\r\n')
                    
                    time.sleep(1 / self.fps)

            except Exception as e:
                logger.error(f"Error in direct stream: {str(e)}", exc_info=True)
                error_frame = self._generate_error_frame(f"Error: {str(e)}")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            finally:
                logger.info("Releasing video capture and stopping stream.")
                if cap.isOpened():
                    cap.release()
                self.stop()
        else:
            logger.error(f"Failed to open RTSP stream: {self.rtsp_url}")
            error_frame = self._generate_error_frame(f"Failed to open RTSP stream: {self.rtsp_url}")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')

    def stream_from_redis(self, redis_client: CameraCache) -> Generator[bytes, None, None]:
        """
        Stream MJPEG frames from the redis.
        
        Yields:
            bytes: MJPEG frame data with multipart/x-mixed-replace boundary and headers
        """
        # Get the latest messages from the redis
        try:
            ps = redis_client.subscribe_to_channel()
            frame_count = 0
            
            for m in ps.listen():
                start_time = time.time()
                
                data = redis_client.get(redis_client.publish_key) # This only returns the latest frame name
                print(f"Time to get publish key: {(time.time() - start_time)*1000:.2f}ms")
                
                if not data:
                    continue

                frame_key = data.get("frame_key")
                print(f"Frame key: {frame_key}")

                if not frame_key:
                    continue

                # Get the frame from the redis
                redis_start = time.time()
                frame = redis_client.get(frame_key)
                print(f"Time to get frame from Redis: {(time.time() - redis_start)*1000:.2f}ms")

                if not frame:
                    print(f"Frame not found: {frame_key}")
                    continue

                print(f"Frame is found: {frame_key}")

                frame_base64 = frame.get("final_result")
                
                if not frame_base64:
                    continue

                try:
                    # Decode base64 string back to bytes
                    decode_start = time.time()
                    frame_bytes = base64.b64decode(frame_base64)
                    print(f"Time to decode base64: {(time.time() - decode_start)*1000:.2f}ms")

                    # Convert the frame to a JPEG image
                    convert_start = time.time()
                    nparr = np.frombuffer(frame_bytes, np.uint8)
                    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    print(f"Time to convert to image: {(time.time() - convert_start)*1000:.2f}ms")
                    
                    if frame is None:
                        logger.warning("Failed to decode frame from Redis")
                        continue
                    
                    # Encode to JPEG with consistent quality
                    encode_start = time.time()
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 90]
                    _, buffer = cv2.imencode('.jpg', frame, encode_params)
                    jpg = buffer.tobytes()
                    print(f"Time to encode JPEG: {(time.time() - encode_start)*1000:.2f}ms")
                    
                    # Yield the properly formatted MJPEG frame with headers
                    yield_start = time.time()
                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n'
                        b'Content-Length: ' + str(len(jpg)).encode() + b'\r\n\r\n' + 
                        jpg + b'\r\n')
                    print(f"Time to yield frame: {(time.time() - yield_start)*1000:.2f}ms")
                    
                    print(f"Total processing time: {(time.time() - start_time)*1000:.2f}ms")
                    
                    # Small delay to prevent overwhelming the client
                    time.sleep(0.01)  # 10ms delay between frames
                    
                except Exception as e:
                    logger.error(f"Error processing Redis frame: {str(e)}")
                    continue
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error streaming from redis: {str(e)}")
            error_frame = self._generate_error_frame("Failed to stream camera feeds.")
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            
    def stream_from_ram(self, redis_client: CameraCache) -> Generator[bytes, None, None]:
        """
        Stream MJPEG frames from the redis.
        
        Yields:
            bytes: MJPEG frame data with multipart/x-mixed-replace boundary and headers
        """
        # Get the latest messages from the redis
        try:
            ps = redis_client.subscribe_to_channel()
            
            for m in ps.listen():
                start_time = time.time()

                # Read from the /dev/shm/frame/streaming/{camera_id}/streaming_final_result.json
                try:
                    logger.info(f"Reading frame from RAM: {redis_client.camera_id}")
                    print(f"Reading frame from RAM: {redis_client.camera_id}")
                    output_dir = f'/frame/streaming/{redis_client.camera_id}'
                    output_path = os.path.join(output_dir, 'streaming_final_result.json')
                    with open(output_path, 'r') as f:
                        frame = json.load(f)
                    # print(f"Frame: {frame}")

                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"Error reading frame from RAM: {str(e)}")
                    print(f"Error reading frame from RAM: {str(e)}")
                    time.sleep(0.1)
                    continue

                # Do not cache frame bytes in redis
                # data = redis_client.get(redis_client.publish_key) # This only returns the latest frame name
                # print(f"Time to get publish key: {(time.time() - start_time)*1000:.2f}ms")
                
                # if not data:
                #     continue

                # frame_key = data.get("frame_key")
                # print(f"Frame key: {frame_key}")

                # if not frame_key:
                #     continue

                # # Get the frame from the redis
                # redis_start = time.time()
                # frame = redis_client.get(frame_key)
                # print(f"Time to get frame from Redis: {(time.time() - redis_start)*1000:.2f}ms")

                if not frame:
                    logger.warning(f"Frame not found: {output_path}")
                    print(f"Frame not found: {output_path}")
                    continue

                logger.info(f"Frame is found: {output_path}")
                print(f"Frame is found: {output_path}")

                frame_base64 = frame.get("final_result")
                
                if not frame_base64:
                    continue

                try:
                    # Decode base64 string back to bytes
                    decode_start = time.time()
                    frame_bytes = base64.b64decode(frame_base64)
                    print(f"Time to decode base64: {(time.time() - decode_start)*1000:.2f}ms")

                    # Convert the frame to a JPEG image
                    convert_start = time.time()
                    nparr = np.frombuffer(frame_bytes, np.uint8)
                    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    print(f"Time to convert to image: {(time.time() - convert_start)*1000:.2f}ms")
                    
                    if frame is None:
                        logger.warning("Failed to decode frame from Redis")
                        continue
                    
                    # Encode to JPEG with consistent quality
                    encode_start = time.time()
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 90]
                    _, buffer = cv2.imencode('.jpg', frame, encode_params)
                    jpg = buffer.tobytes()
                    print(f"Time to encode JPEG: {(time.time() - encode_start)*1000:.2f}ms")
                    
                    # Yield the properly formatted MJPEG frame with headers
                    yield_start = time.time()
                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n'
                        b'Content-Length: ' + str(len(jpg)).encode() + b'\r\n\r\n' + 
                        jpg + b'\r\n')
                    print(f"Time to yield frame: {(time.time() - yield_start)*1000:.2f}ms")
                    
                    print(f"Total processing time: {(time.time() - start_time)*1000:.2f}ms")
                    
                    # Small delay to prevent overwhelming the client
                    time.sleep(0.01)  # 10ms delay between frames
                    
                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"Error processing Redis frame: {str(e)}")
                    continue
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error streaming from redis: {str(e)}")
            error_frame = self._generate_error_frame("Failed to stream camera feeds.")
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')

    def stream(self) -> Generator[bytes, None, None]:
        """
        Stream MJPEG frames from the camera with Redis results overlay.
        
        Yields:
            bytes: MJPEG frame data with multipart/x-mixed-replace boundary and headers
        """
        if self.is_running:
            print("Stream is already running")
            return

        cap = cv2.VideoCapture(self.rtsp_url)
        if not cap.isOpened():
            error_frame = self._generate_error_frame("Failed to start stream")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            return

        self.is_running = True
        try:
            while self.is_running:
                ret, frame = cap.read()
                if not ret:
                    logger.warning("Failed to grab frame, stream may have ended.")
                    break
                
                if self._is_frame_corrupted(frame):
                    continue
                
                _, buffer = cv2.imencode('.jpg', frame)
                jpg = buffer.tobytes()

                # ─────────── metadata output ───────────
                ts = (datetime.datetime.now(pytz.timezone('Asia/Singapore')) - datetime.timedelta(seconds=1)).strftime("%Y-%m-%d %I:%M:%S")
                # ───────────────────────────────────────

                frame_key = REDIS.build_key(self.camera_id, ts, "final_results")
                final_results = REDIS.get(frame_key)

                if final_results:
                    jpg = self.draw_final_results(jpg, final_results.get('result', []))

                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + jpg + b'\r\n')
                
                time.sleep(1 / self.fps)

        except Exception as e:
            logger.error(f"Fatal error in stream: {str(e)}", exc_info=True)
            error_frame = self._generate_error_frame(f"Fatal error: {str(e)}")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
        finally:
            if cap.isOpened():
                cap.release()
            self.stop()

    def _generate_error_frame(self, message: str) -> bytes:
        """Generate an error frame with the given message."""
        width, height = 640, 480
        image = Image.new('RGB', (width, height), color=(255, 0, 0))
        draw = ImageDraw.Draw(image)
        draw.text((20, 20), "Stream Error", fill=(255, 255, 255))
        draw.text((20, 50), message[:50], fill=(255, 255, 255))
        
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        return buffer.getvalue()
    
    def draw_final_results(self, image_bytes: bytes, final_results: list):
        """Draw detection boxes and labels on the image.
        
        Args:
            image_bytes: The image bytes to draw on
            final_results: List of detection results containing coordinates and styling info
        
        Returns:
            Bytes of the image with drawn detection boxes and labels
        """
        # Convert image bytes to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            print("Failed to decode image")
            return image_bytes

        for detection in final_results:
            coords = detection.get("coordinates", {})
            x1, y1, x2, y2 = (
                coords.get("x1"),
                coords.get("y1"),
                coords.get("x2"),
                coords.get("y2"),
            )
            if None in (x1, y1, x2, y2):
                continue

            # Convert coordinates to integers
            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            
            # Extract styling
            style = detection.get("style", {})
            color = tuple(int(c) for c in style.get("rectangle_color", [0, 255, 0]))
            rect_thick = int(style.get("rectangle_thickness", 2))
            text_thick = int(style.get("text_thickness", 1))
            
            # Draw rectangle
            label = f"{detection.get('object_name', '?')} {detection.get('confidence', 0):.2f}"

            cv2.rectangle(image, (x1, y1), (x2, y2), color, rect_thick)

            (w, h), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_thick)
            cv2.rectangle(image, (x1, y1 - h - 4), (x1 + w, y1), color, -1)
            cv2.putText(
                image,
                label,
                (x1, y1 - 4),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (255, 255, 255),
                text_thick,
                cv2.LINE_AA,
            )
        
        # Convert back to JPEG bytes
        _, buffer = cv2.imencode('.jpg', image)
        return buffer.tobytes()    
    def stream_from_file(self, file_path: str) -> Generator[bytes, None, None]:
        """
        Read frames from a local video file in a loop using OpenCV.
        
        Args:
            file_path: Path to the video file
            
        Yields:
            bytes: MJPEG frame data
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return
            
        self.is_running = True
        
        while self.is_running:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                print(f"Error opening video file: {file_path}")
                time.sleep(5)
                continue
            
            try:
                while self.is_running:
                    ret, frame = cap.read()
                    if not ret:
                        print("End of video file, replaying...")
                        break

                    if self._is_frame_corrupted(frame):
                        continue

                    _, buffer = cv2.imencode('.jpg', frame)
                    jpg = buffer.tobytes()

                    ts = (datetime.datetime.now(pytz.timezone('Asia/Singapore')) - datetime.timedelta(seconds=0.5)).strftime("%Y-%m-%d %I:%M:%S")
                    frame_key = REDIS.build_key(self.camera_id, ts, "final_results")
                    final_results = REDIS.get(frame_key)

                    if final_results:
                        jpg = self.draw_final_results(jpg, final_results.get('result', []))

                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n\r\n' + jpg + b'\r\n')
                    
                    time.sleep(1 / self.fps)
            
            except Exception as e:
                print(f"Error processing video file frame: {str(e)}")
            
            finally:
                cap.release()
                if not self.is_running:
                    break

    def nvr_playback_stream(self, start_time: str, end_time: str = None) -> Generator[bytes, None, None]:
        """
        Stream historical footage by shelling out to ffmpeg (same URL
        you'd use with ffplay) and piping MJPEG back to the client.
        """
        # 1) Validate connection info
        if not self.nvr_params:
            yield self._create_mjpeg_error_frame("NVR parameters not provided")
            return

        host = self.nvr_params.get('host')
        user = self.nvr_params.get('username')
        pw = self.nvr_params.get('password')
        
        # Format channel number as: 101 for camera 1, 201 for camera 2, etc.
        camera_num = int(self.nvr_params.get('channel', 1))
        channel = str((camera_num * 100) + 1)  # Convert to 101, 201, 301 format
        
        rtsp_port = int(self.nvr_params.get('rtsp_port', 554))

        if not all([host, user, pw]):
            yield self._create_mjpeg_error_frame("Missing host/user/password")
            return

        # 2) Robust ISO parser (drop trailing Z + fractional seconds)
        def parse_ts(ts: str) -> datetime.datetime:
            if ts.endswith("Z"):
                ts = ts[:-1]
            if "." in ts:
                ts = ts.split(".", 1)[0]
            if "T" in ts:
                return datetime.datetime.fromisoformat(ts)
            return datetime.datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")

        try:
            start_dt = parse_ts(start_time)
            end_dt = parse_ts(end_time) if end_time else datetime.datetime.now()
        except Exception as e:
            yield self._create_mjpeg_error_frame(f"Invalid timestamp: {e}")
            return

        # 3) Build RTSP URL with simpler format for Hikvision compatibility
        # Format timestamps according to Hikvision format (no 'T', just date then time)
        def fmt(dt: datetime.datetime) -> str:
            return dt.strftime("%Y%m%dT%H%M%SZ")

        # Use a simpler query string format
        starttime_param = fmt(start_dt)

        logger.info(f"Start time: {start_dt}")
        logger.info(f"End time: {end_dt}")

        # Try with raw password instead of URL escaping
        rtsp_url = f"rtsp://{user}:{pw}@{host}:{rtsp_port}/Streaming/tracks/{channel}?starttime={starttime_param}"
        
        logger.info(f"Launching NVR playback RTSP URL: {rtsp_url}")

        # 4) Use shell=True with pre-escaped command to avoid parsing issues
        max_retries = 2  # Maximum number of connection attempts
        retry_count = 0
                
        # Construct command with all the optimized flags
        # Use UDP instead of TCP and request a lower bandwidth stream
        ffmpeg_cmd = f"ffmpeg -hide_banner -loglevel error \
                -fflags nobuffer+discardcorrupt+genpts \
                -avoid_negative_ts make_zero \
                -rtsp_transport tcp \
                -i '{rtsp_url}' \
                -vcodec mjpeg \
                -q:v 7 \
                -f image2pipe \
                -"
            
        logger.info(f"Running ffmpeg command (attempt {retry_count+1}): {ffmpeg_cmd}")
            
        try:
            # Create a process with both stdout and stderr pipes using shell=True
            # This avoids argument parsing issues with special characters in URLs
            proc = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            
            # Track errors for retry logic
            error_detected = False
            
            # Start a thread to read and log stderr output
            def log_stderr():
                nonlocal error_detected
                logger.info("Starting ffmpeg stderr logging thread")
                for line in iter(proc.stderr.readline, b''):
                    decoded = line.decode().strip()
                    if decoded:  # Only log non-empty lines
                        if "error" in decoded.lower() or "failed" in decoded.lower():
                            logger.error(f"ffmpeg error: {decoded}")
                            error_detected = True
                        else:
                            logger.debug(f"ffmpeg: {decoded}")
                logger.info("ffmpeg stderr logging thread completed")
                
            stderr_thread = threading.Thread(target=log_stderr)
            stderr_thread.daemon = True
            stderr_thread.start()
            
            # Wait a moment to catch immediate failures
            time.sleep(1)
            if proc.poll() is not None:
                # Process already terminated
                exit_code = proc.poll()
                logger.error(f"ffmpeg process terminated immediately with exit code {exit_code}")
                retry_count += 1
                
            # Process is running, check the first chunk
            first_chunk = proc.stdout.read(1024)  # Try to read the first chunk
            if not first_chunk:
                logger.error("ffmpeg started but produced no output")
                retry_count += 1
                proc.terminate()
                
            # If we got here, we have a valid stream starting, so break out of retry loop
            # Save the first frame to a file for debugging
            try:
                debug_dir = '/tmp/squirrelsentry_debug'
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                debug_file = f"{debug_dir}/nvr_playback_sample_{timestamp}.jpg"
                
                # Check if this is a proper JPEG chunk by looking for markers
                if b'\xff\xd8' in first_chunk and b'\xff\xd9' in first_chunk:
                    # Extract JPEG from the chunk (may be within MJPEG multipart format)
                    jpeg_start = first_chunk.find(b'\xff\xd8')
                    jpeg_end = first_chunk.find(b'\xff\xd9', jpeg_start) + 2
                    
                    if jpeg_start >= 0 and jpeg_end > jpeg_start:
                        # We found a complete JPEG, save it
                        with open(debug_file, 'wb') as f:
                            f.write(first_chunk[jpeg_start:jpeg_end])
                        logger.info(f"Saved sample frame to {debug_file}")
                    else:
                        # Basic write as binary - might not be a valid image
                        with open(debug_file, 'wb') as f:
                            f.write(first_chunk)
                        logger.info(f"Saved raw bytes to {debug_file} (may not be a valid image)")
                else:
                    # This doesn't appear to be a JPEG - save the raw bytes for inspection
                    with open(debug_file, 'wb') as f:
                        f.write(first_chunk)
                    logger.info(f"Saved raw bytes to {debug_file} (no JPEG markers found)")
                    
                    # Also save first 100 bytes in hex format to log for debugging
                    hex_preview = ' '.join(f'{b:02x}' for b in first_chunk[:100])
                    logger.debug(f"First 100 bytes: {hex_preview}")
            except Exception as e:
                logger.error(f"Error saving debug frame: {e}")
            
            # First, yield the first chunk we already read
            yield first_chunk
        except Exception as e:
            logger.error(f"Error starting ffmpeg: {e}")
            retry_count += 1
        
        # If we've exhausted all retries and still failed
        if retry_count > max_retries:
            logger.error(f"Failed to connect to RTSP stream after {max_retries+1} attempts")
            yield self._create_mjpeg_error_frame(f"Failed to connect to camera playback stream after {max_retries+1} attempts")
            return

        self.is_running = True

        # 5) Stream JPEG frames from ffmpeg
        buffer = b''
        jpeg_start_marker = b'\xff\xd8'
        jpeg_end_marker = b'\xff\xd9'
        frame_count = 0
        
        try:
            while self.is_running:
                # Read a chunk of data from ffmpeg stdout
                chunk = proc.stdout.read(16384)  # Larger buffer for efficiency
                if not chunk:
                    logger.warning("ffmpeg stream ended")
                    break
                
                # Add the new data to our buffer
                buffer += chunk
                
                # Extract complete JPEG frames from the buffer
                while True:
                    # Find start of JPEG
                    start_pos = buffer.find(jpeg_start_marker)
                    if start_pos == -1:
                        # No start marker found, clear buffer except last few bytes
                        # (in case start marker is split across reads)
                        if len(buffer) > 2:
                            buffer = buffer[-2:]
                        break
                    
                    # Find end of JPEG after the start marker
                    end_pos = buffer.find(jpeg_end_marker, start_pos)
                    if end_pos == -1:
                        # No end marker yet, keep reading more data
                        # But trim buffer to start from the start marker
                        buffer = buffer[start_pos:]
                        break
                    
                    # We found a complete JPEG frame
                    end_pos += 2  # Include the end marker itself
                    jpeg_frame = buffer[start_pos:end_pos]
                    
                    # Save a frame periodically for debugging (every 50th frame)
                    frame_count += 1
                    if frame_count % 50 == 0:
                        try:
                            debug_dir = '/tmp/squirrelsentry_debug'
                            os.makedirs(debug_dir, exist_ok=True)
                            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                            debug_file = f"{debug_dir}/nvr_frame_{timestamp}_{frame_count}.jpg"
                            with open(debug_file, 'wb') as f:
                                f.write(jpeg_frame)
                            logger.debug(f"Saved frame #{frame_count} to {debug_file}")
                        except Exception as e:
                            logger.error(f"Error saving debug frame: {e}")
                    
                    # Format as MJPEG and yield the complete frame
                    mjpeg_frame = (b'--frame\r\n'
                                  b'Content-Type: image/jpeg\r\n\r\n' + jpeg_frame + b'\r\n')
                    yield mjpeg_frame
                    
                    # Remove the processed frame from the buffer
                    buffer = buffer[end_pos:]
        finally:
            self.is_running = False
            logger.info("Terminating ffmpeg process")
            proc.terminate()
                
    def _create_mjpeg_error_frame(self, error_message: str) -> bytes:
        """Create a properly formatted MJPEG error frame."""
        error_img = self._generate_error_frame(error_message)
        return (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + error_img + b'\r\n')
