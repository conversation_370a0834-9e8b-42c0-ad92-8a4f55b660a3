"""
Management command to set up and test VoicePing integration
"""
import logging
from django.core.management.base import BaseCommand
from utils.voiceping.service import VoicePingService
from utils.voiceping.client import VoicePingClient
from cameras.services.voiceping_notifier import VoicePingNotifier

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Set up and test VoicePing integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-message',
            action='store_true',
            help='Send a test message to verify integration'
        )
        parser.add_argument(
            '--list-channels',
            action='store_true',
            help='List available VoicePing channels'
        )
        parser.add_argument(
            '--channel-id',
            type=str,
            help='Channel ID to send test message to'
        )

    def handle(self, *args, **options):
        """Handle the command execution"""
        self.stdout.write('Setting up VoicePing integration...')
        
        # Initialize VoicePing service
        vp_service = VoicePingService()
        
        # Check if VoicePing is configured
        if vp_service.is_configured():
            self.stdout.write(self.style.SUCCESS('VoicePing service initialized successfully'))
            
            # Test the authentication
            token = vp_service.client.get_auth_token()
            if token:
                self.stdout.write(self.style.SUCCESS('Successfully authenticated with VoicePing'))
            else:
                self.stdout.write(self.style.ERROR('Failed to authenticate with VoicePing'))
                return
                
            # List channels if requested
            if options['list_channels']:
                self._list_channels(vp_service)
                
            # Send a test message if requested
            if options['test_message']:
                channel_id = options['channel_id']
                if not channel_id:
                    self.stdout.write(self.style.ERROR('Channel ID is required for sending test messages'))
                    return
                    
                self._send_test_message(vp_service, channel_id)
        else:
            self.stdout.write(self.style.ERROR('VoicePing service failed to initialize'))
            
    def _list_channels(self, vp_service):
        """List available channels"""
        self.stdout.write('Fetching available channels...')
        
        channels = vp_service.get_available_channels()
        if not channels:
            self.stdout.write(self.style.ERROR('Failed to retrieve channels'))
            return
            
        self.stdout.write(self.style.SUCCESS(f'Found {len(channels)} channels:'))
        for channel in channels:
            channel_id = channel.get('id', 'unknown')
            name = channel.get('name', 'unnamed')
            type_id = channel.get('type_id', 'unknown')
            
            self.stdout.write(f'  - [{channel_id}] {name} (Type: {type_id})')
            
    def _send_test_message(self, vp_service, channel_id):
        """Send a test message to the specified channel"""
        self.stdout.write(f'Sending test message to channel {channel_id}...')
        
        # Create a test event
        test_event = {
            'id': 'test-event-001',
            'event_type': 'test_event',
            'camera_name': 'Test Camera',
            'event_severity': 'Critical',
            'is_reviewed': False
        }
        
        # Create a notifier instance
        notifier = VoicePingNotifier()
        
        # Override the channel ID for this test
        result = notifier.notify_event(test_event, channel_id=channel_id)
        
        if result:
            self.stdout.write(self.style.SUCCESS('Test message sent successfully!'))
        else:
            self.stdout.write(self.style.ERROR('Failed to send test message'))
