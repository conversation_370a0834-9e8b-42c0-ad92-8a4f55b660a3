"""
Management command to seed the database with default data (cameras, layers, CV models)
"""
import os
import yaml
import logging
from datetime import datetime
from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone
from backend.settings import RTSP_USERNAME, RTSP_PASSWORD, RTSP_SERVER, RTSP_PORT
from cameras.models import Camera, Layer, CamerasLayersConfiguration, CamerasLayersConfigurationDependency, ComputerVisionModel, RegionOfInterest, CameraEvent, PermanentCameraOverlay

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Seed the database with default cameras, layers, and CV models'

    def handle(self, *args, **options):
        """
        Main command entry point - orchestrates seeding the database
        """
        # First create the CV models (required for layers)
        self.create_cv_models()
        
        # Create the global template layers (not associated with specific cameras)
        self.create_template_layers()
        
        # Then load and create the cameras
        self.load_cameras()
        
        # Load regions of interest for cameras
        self.load_regions_of_interest()

        # Need to load in the cameras layers config
        self.load_cameras_layers_config()

        # Finally, configure the layers config for its many to many self dependencies
        self.load_cameras_layers_dependencies()
        
        # Load permanent camera overlays
        self.load_permanent_overlays()
        
        # Load default events for demo purposes
        self.load_default_events()
        
    def load_cameras(self):
        """
        Load and create cameras from YAML configuration file
        """
        self.stdout.write(self.style.NOTICE('Loading default cameras from configuration...'))
        
        # Define path to the default cameras YAML file
        fixtures_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'fixtures',
            'default_cameras.yaml'
        )
        
        if not os.path.exists(fixtures_path):
            self.stdout.write(self.style.WARNING(
                f'Default cameras configuration not found at {fixtures_path}'
            ))
            return
            
        try:
            # Load the YAML configuration
            with open(fixtures_path, 'r') as f:
                config = yaml.safe_load(f)
                
            if not config or 'cameras' not in config:
                self.stdout.write(self.style.WARNING('No cameras defined in configuration file'))
                return
                
            cameras_config = config['cameras']
            self.stdout.write(self.style.SUCCESS(
                f'Found {len(cameras_config)} cameras in configuration'
            ))
            
            # Count created and updated cameras
            created_count = 0
            updated_count = 0
            
            # Process each camera in the configuration
            for camera_data in cameras_config:
                name = camera_data.get('name')
                rtsp_url = camera_data.get('rtsp_url')

                # Construct the full RTSP URL with the username, password, server, and port
                rtsp_url = rtsp_url.replace('USERNAME', RTSP_USERNAME)
                rtsp_url = rtsp_url.replace('PASSWORD', RTSP_PASSWORD)
                rtsp_url = rtsp_url.replace('SERVER', RTSP_SERVER)
                rtsp_url = rtsp_url.replace('PORT', RTSP_PORT)

                if not name or not rtsp_url:
                    self.stdout.write(self.style.WARNING(
                        f'Skipping camera with missing name or RTSP URL: {camera_data}'
                    ))
                    continue
                
                try:
                    # Prepare the defaults dictionary with all camera fields
                    defaults = {
                        'rtsp_url': rtsp_url,
                        'stream_fps': camera_data.get('stream_fps', 1),
                        'encoding_format': camera_data.get('encoding_format', 'H.264'),
                        'dynamic_frame_rate': camera_data.get('dynamic_frame_rate', True),
                        'required_analytics': camera_data.get('required_analytics', False),
                        'description': camera_data.get('description', ''),
                        'location': camera_data.get('location', ''),
                        'status': 'offline'  # Default to offline until connected
                    }
                    
                    # Use get_or_create with name as the unique identifier
                    camera, created = Camera.objects.get_or_create(
                        name=name,
                        defaults=defaults
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(f'Created camera: {name}'))
                    else:
                        # Check if any fields need updating
                        needs_update = False
                        for field, value in defaults.items():
                            if getattr(camera, field) != value:
                                setattr(camera, field, value)
                                needs_update = True
                                
                        if needs_update:
                            camera.save()
                            updated_count += 1
                            self.stdout.write(f'Updated camera: {name}')
                        else:
                            self.stdout.write(f'Camera exists and is up to date: {name}')
                            
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing camera {name}: {str(e)}'))
                    continue
                
            self.stdout.write(self.style.SUCCESS(
                f'Camera loading complete: {created_count} created, {updated_count} updated'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading cameras: {str(e)}'))
            logger.exception('Error loading default cameras')

    def create_template_layers(self):
        """
        Create the unique template layers (not associated with specific cameras)
        """
        try:
            import uuid
            import yaml
            import os
            
            # Path to the YAML file with default layers
            yaml_path = os.path.join(settings.BASE_DIR, 'cameras', 'fixtures', 'default_layers.yaml')
            
            # Check if the file exists
            if not os.path.exists(yaml_path):
                self.stdout.write(self.style.WARNING(f"Layers configuration file not found at {yaml_path}"))
                return []
                
            # Load layers from YAML
            try:
                with open(yaml_path, 'r') as file:
                    config = yaml.safe_load(file)
            except Exception as yaml_error:
                self.stdout.write(self.style.ERROR(f"Error loading layers YAML: {str(yaml_error)}"))
                return []
            
            # Filter out the dependencies section (we'll process this separately)
            layers_config = [item for item in config if isinstance(item, dict) and 'name' in item]
            dependencies_config = next((item.get('dependencies', []) for item in config if isinstance(item, dict) and 'dependencies' in item), [])
            
            # Check which template layers already exist
            template_layer_names = [layer.get('name') for layer in layers_config if layer.get('name')]
            existing_template_layers = Layer.objects.filter(name__in=template_layer_names)
            
            # Create a map of existing layers by name
            existing_layer_map = {layer.name: layer for layer in existing_template_layers}
            
            # Track all layers for dependency setup (both existing and newly created)
            created_layers = list(existing_template_layers)
            layer_map = {layer.name: layer for layer in existing_template_layers}  # Start with existing layers
            
            # Count how many layers we need to create
            layers_to_create = [l for l in layers_config if l.get('name') not in existing_layer_map]
            
            if not layers_to_create:
                self.stdout.write("All template layers already exist, skipping creation")
                return existing_template_layers
            else:
                self.stdout.write(f"Found {len(existing_layer_map)} existing layers, need to create {len(layers_to_create)} new layers")
            
            for layer_data in layers_config:
                layer_name = layer_data.get('name')
                
                # Skip if this layer already exists
                if layer_name in existing_layer_map:
                    self.stdout.write(f"Layer {layer_name} already exists, skipping creation")
                    continue
                    
                # Create a copy of the data to avoid modifying the original
                layer_create_data = layer_data.copy()
                
                # Extract CV model name and get the actual model
                cv_model_name = layer_create_data.pop('cv_model', None)
                cv_model = None
                if cv_model_name:
                    cv_model = ComputerVisionModel.objects.filter(model_type=cv_model_name).first()
                    if not cv_model:
                        self.stdout.write(self.style.WARNING(f"CV model {cv_model_name} not found for layer {layer_create_data.get('name')}"))
                
                # Create the layer
                layer = Layer.objects.create(**layer_create_data)
                
                # Set CV model if available
                if cv_model:
                    layer.cv_model = cv_model
                    layer.save()
                    
                created_layers.append(layer)
                # Store in map for dependency setup
                layer_map[layer.name] = layer
                self.stdout.write(self.style.SUCCESS(f"Created template layer {layer.name}"))
            
            # Set up dependencies based on YAML configuration
            for dep_config in dependencies_config:
                source_name = dep_config.get('source')
                depends_on_names = dep_config.get('depends_on', [])
                
                if not source_name or not depends_on_names:
                    continue
                    
                source_layer = layer_map.get(source_name)
                if not source_layer:
                    self.stdout.write(self.style.WARNING(f"Source layer {source_name} not found for dependency setup"))
                    continue
                    
                for dep_name in depends_on_names:
                    dep_layer = layer_map.get(dep_name)
                    if not dep_layer:
                        self.stdout.write(self.style.WARNING(f"Dependency layer {dep_name} not found"))
                        continue
                        
                    source_layer.dependencies.add(dep_layer)
                    self.stdout.write(f"Added dependency: {source_name} depends on {dep_name}")
            
            return created_layers
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating template layers: {str(e)}"))
            logger.exception("Error creating template layers")
            return []   
            
    def create_cv_models(self):
        """
        Create the computer vision models if they don't exist
        """
        try:
            import uuid
            import yaml
            import os
            # Path to the YAML file with default CV models
            yaml_path = os.path.join(settings.BASE_DIR, 'cameras', 'fixtures', 'default_cv_models.yaml')
            
            # Check if the file exists
            if not os.path.exists(yaml_path):
                self.stdout.write(self.style.WARNING(f"CV models configuration file not found at {yaml_path}"))
                return
                
            # Load CV models from YAML
            try:
                with open(yaml_path, 'r') as file:
                    cv_models_config = yaml.safe_load(file)
            except Exception as yaml_error:
                self.stdout.write(self.style.ERROR(f"Error loading CV models YAML: {str(yaml_error)}"))
                return
            
            created_count = 0
            updated_count = 0
            
            # Process each model in the config
            for model_config in cv_models_config:
                model_type = model_config.get('model_type')
                if not model_type:
                    self.stdout.write(self.style.WARNING("Skipping CV model without model_type"))
                    continue
                    
                try:
                    # Use get_or_create with model_type as the unique identifier
                    model, created = ComputerVisionModel.objects.get_or_create(
                        model_type=model_type,
                        defaults={
                            'id': uuid.uuid4()
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(f"Created CV model: {model.model_type}"))
                    else:
                        # For CV models, we currently don't have additional fields to update
                        self.stdout.write(f"CV model already exists: {model_type}")
                        
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error processing CV model {model_type}: {str(e)}"))
                    continue
                    
            self.stdout.write(self.style.SUCCESS(
                f"CV models loading complete: {created_count} created, {updated_count} updated"
            ))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating CV models: {str(e)}"))
            logger.exception("Error creating CV models")

    def load_cameras_layers_config(self):
        """
        Load camera layer configurations from YAML and populate the CamerasLayersConfiguration table
        with proper dependency relationships
        """
        try:
            # Path to the YAML file with camera layer configurations
            yaml_path = os.path.join(settings.BASE_DIR, 'cameras', 'fixtures', 'default_cameras_layers_config.yml')
            
            # Check if the file exists
            if not os.path.exists(yaml_path):
                self.stdout.write(self.style.WARNING(f"Camera layers configuration file not found at {yaml_path}"))
                return
                
            # Load configurations from YAML
            try:
                with open(yaml_path, 'r') as file:
                    config = yaml.safe_load(file)
            except Exception as yaml_error:
                self.stdout.write(self.style.ERROR(f"Error loading camera layers YAML: {str(yaml_error)}"))
                return
            
            cameras_mapping = config.get('cameras_mapping', [])
            created_count = 0
            updated_count = 0
            
            for camera_config in cameras_mapping:
                camera_name = camera_config.get('camera_name')
                layers_config = camera_config.get('layers', [])
                
                # Find the camera by name
                try:
                    camera = Camera.objects.get(name=camera_name)
                except Camera.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"Camera {camera_name} not found, skipping configuration"))
                    continue
                
                # Process each layer configuration
                for layer_config in layers_config:
                    base_layer_name = layer_config.get('base_layer_name')
                    if not base_layer_name:
                        self.stdout.write(self.style.WARNING(f"Layer name not provided, skipping"))
                        continue
                    
                    # Find the base layer by name
                    try:
                        base_layer = Layer.objects.get(name=base_layer_name)
                    except Layer.DoesNotExist:
                        self.stdout.write(self.style.WARNING(f"Template layer {base_layer_name} not found, skipping"))
                        continue
                    
                    try:
                        # Get configuration data
                        configuration = layer_config.get('configuration', {})
                        
                        # Prepare defaults dictionary
                        defaults = {
                            'configuration': configuration
                        }
                        
                        # Get display name (from top level, not from configuration)
                        if 'camera_layer_config_name' in layer_config:
                            defaults['name'] = layer_config['camera_layer_config_name']
                        else:
                            defaults['name'] = base_layer_name
                        
                        # Get enabled state (from top level, not from configuration)
                        if 'enabled' in layer_config:
                            defaults['enabled'] = layer_config['enabled']
                        
                        # Get color (from top level, not from configuration)
                        if 'color' in layer_config:
                            defaults['color'] = layer_config['color']
                        
                        # Store regions to add after creating the configuration (for ManyToMany field)
                        regions_to_add = []
                        
                        # Handle multiple regions if specified
                        if 'regions' in layer_config and layer_config['regions']:
                            for region_name in layer_config['regions']:
                                try:
                                    region = RegionOfInterest.objects.get(name=region_name)
                                    if region not in regions_to_add:  # Avoid duplicates
                                        regions_to_add.append(region)
                                    self.stdout.write(f"Found additional region {region_name}")
                                except RegionOfInterest.DoesNotExist:
                                    self.stdout.write(self.style.WARNING(
                                        f"Region {region_name} not found, skipping"
                                    ))
                        
                        # Check if configuration already exists to prevent duplicates
                        existing_configs = CamerasLayersConfiguration.objects.filter(
                            camera=camera,
                            layers=base_layer
                        )
                        
                        if existing_configs.count() > 1:
                            # If multiple configurations exist, delete all but the first one
                            self.stdout.write(self.style.WARNING(
                                f"Found {existing_configs.count()} configurations for camera {camera.name}, layer {base_layer_name}. Cleaning up duplicates."
                            ))
                            # Keep the first one and delete the rest
                            first_config = existing_configs.first()
                            existing_configs.exclude(id=first_config.id).delete()
                            
                            # Update the remaining one
                            for key, value in defaults.items():
                                setattr(first_config, key, value)
                            first_config.save()
                            
                            # Clear existing regions and add new ones
                            first_config.regions.clear()
                            for region in regions_to_add:
                                first_config.regions.add(region)
                                
                            config_obj = first_config
                            created = False
                        elif existing_configs.count() == 1:
                            # If exactly one configuration exists, update it
                            config_obj = existing_configs.first()
                            for key, value in defaults.items():
                                setattr(config_obj, key, value)
                            config_obj.save()
                            
                            # Clear existing regions and add new ones
                            config_obj.regions.clear()
                            for region in regions_to_add:
                                config_obj.regions.add(region)
                                
                            created = False
                        else:
                            # If no configuration exists, create a new one
                            config_obj = CamerasLayersConfiguration.objects.create(
                                camera=camera,
                                layers=base_layer,
                                **defaults
                            )
                            
                            # Add regions to the newly created configuration
                            for region in regions_to_add:
                                config_obj.regions.add(region)
                                
                            created = True
                        
                        if created:
                            created_count += 1
                            self.stdout.write(self.style.SUCCESS(
                                f"Created configuration for camera {camera_name}, layer {base_layer_name}"
                            ))
                        else:
                            updated_count += 1
                            self.stdout.write(f"Updated configuration for camera {camera_name}, layer {base_layer_name}")
                        
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(
                            f"Error saving configuration for camera {camera_name}, layer {base_layer_name}: {str(e)}"
                        ))
                        continue
            
            self.stdout.write(self.style.SUCCESS(
                f"Camera layers configuration loading complete: {created_count} created, {updated_count} updated"
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error loading camera layers configuration: {str(e)}"))
            logger.exception('Error loading camera layers configuration')

    def load_cameras_layers_dependencies(self):
        """
        Load camera layer dependencies from YAML and populate the CamerasLayersConfigurationDependency table
        """
        try:
            # Path to the YAML file with camera layer dependencies
            yaml_path = os.path.join(settings.BASE_DIR, 'cameras', 'fixtures', 'default_cameras_layers_dependencies.yaml')
            
            # Check if the file exists
            if not os.path.exists(yaml_path):
                self.stdout.write(self.style.WARNING(f"Camera layers dependencies file not found at {yaml_path}"))
                return
                
            # Load dependencies from YAML
            try:
                with open(yaml_path, 'r') as file:
                    config = yaml.safe_load(file)
            except Exception as yaml_error:
                self.stdout.write(self.style.ERROR(f"Error loading camera layers dependencies YAML: {str(yaml_error)}"))
                return
            
            if not config:
                self.stdout.write(self.style.WARNING("Empty or invalid YAML configuration"))
                return
                
            layer_dependencies = config.get('layer_dependencies', [])
            if not layer_dependencies:
                self.stdout.write(self.style.WARNING("No layer dependencies found in configuration"))
                return

            # Debug output for dependencies found in YAML
            self.stdout.write(f"Found {len(layer_dependencies)} layer dependencies in YAML:")
            for dep in layer_dependencies:
                self.stdout.write(f"  Source: {dep.get('source')}, Depends on: {dep.get('depends_on')}")

            # Get all cameras that have layer configurations
            cameras = Camera.objects.filter(layers_configuration__isnull=False).distinct()
            self.stdout.write(f"Found {cameras.count()} cameras with layer configurations")
            
            # Track statistics
            total_dependencies_created = 0
            total_dependencies_updated = 0
            
            # For each camera, set up the dependencies between its layers
            for camera in cameras:
                self.stdout.write(f"\nProcessing dependencies for camera: {camera.name}")
                
                # Get all layer configurations for this camera
                camera_layer_configs = {}
                configs = CamerasLayersConfiguration.objects.filter(camera=camera).select_related('layers')
                self.stdout.write(f"Found {configs.count()} layer configurations for camera {camera.name}")
                
                for config in configs:
                    camera_layer_configs[config.layers.name] = config
                    self.stdout.write(f"  - Layer config found: {config.layers.name}")
                
                # Process each dependency in the YAML
                for dep_config in layer_dependencies:
                    source_name = dep_config.get('source')
                    depends_on_names = dep_config.get('depends_on', [])
                    conditions = dep_config.get('conditions', {})
                    
                    if not source_name or not depends_on_names:
                        self.stdout.write(self.style.WARNING(f"Invalid dependency config: {dep_config}"))
                        continue
                    
                    # Get the source layer config for this camera
                    source_config = camera_layer_configs.get(source_name)
                    if not source_config:
                        self.stdout.write(self.style.WARNING(
                            f"Source layer {source_name} not found for camera {camera.name}, available layers: {list(camera_layer_configs.keys())}"
                        ))
                        continue
                    
                    # Add dependencies for this layer
                    for dep_name in depends_on_names:
                        dep_config = camera_layer_configs.get(dep_name)
                        if not dep_config:
                            self.stdout.write(self.style.WARNING(
                                f"Dependency layer {dep_name} not found for camera {camera.name}, available layers: {list(camera_layer_configs.keys())}"
                            ))
                            continue
                        
                        # Create the dependency relationship
                        try:
                            dependency, created = CamerasLayersConfigurationDependency.objects.get_or_create(
                                current_layer=source_config,
                                dependency_layer=dep_config,
                                defaults={
                                    'conditions': conditions
                                }
                            )
                            
                            if created:
                                total_dependencies_created += 1
                                self.stdout.write(self.style.SUCCESS(
                                    f"Created dependency: {camera.name} - {source_name} depends on {dep_name}"
                                ))
                            else:
                                # Update conditions if they've changed
                                if dependency.conditions != conditions:
                                    dependency.conditions = conditions
                                    dependency.save()
                                    total_dependencies_updated += 1
                                    self.stdout.write(
                                        f"Updated dependency conditions: {camera.name} - {source_name} depends on {dep_name}"
                                    )
                                else:
                                    self.stdout.write(
                                        f"Dependency already exists: {camera.name} - {source_name} depends on {dep_name}"
                                    )
                                    
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(
                                f"Error creating dependency between {source_name} and {dep_name} for camera {camera.name}: {str(e)}"
                            ))
                            logger.exception(
                                f"Error creating dependency between {source_name} and {dep_name} for camera {camera.name}"
                            )
            
            self.stdout.write(self.style.SUCCESS(
                f"Camera layers dependencies loading complete: {total_dependencies_created} created, {total_dependencies_updated} updated"
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error loading camera layers dependencies: {str(e)}"))
            logger.exception('Error loading camera layers dependencies')
            
    def load_regions_of_interest(self):
        """
        Load and create regions of interest from YAML configuration file
        """
        self.stdout.write(self.style.NOTICE('Loading default regions of interest from configuration...'))
        
        # Define path to the regions of interest YAML file
        fixtures_path = os.path.join(
            settings.BASE_DIR,
            'cameras',
            'fixtures',
            'default_regions_of_interest.yml'
        )
        
        if not os.path.exists(fixtures_path):
            self.stdout.write(self.style.WARNING(
                f'Default regions of interest configuration not found at {fixtures_path}'
            ))
            return
            
        try:
            # Load the YAML configuration
            with open(fixtures_path, 'r') as f:
                config = yaml.safe_load(f)
                
            if not config or 'regions' not in config:
                self.stdout.write(self.style.WARNING('No regions defined in configuration file'))
                return
                
            regions_config = config['regions']
            self.stdout.write(self.style.SUCCESS(
                f'Found {len(regions_config)} region configuration entries'
            ))
            
            # Count existing and new regions
            created_count = 0
            updated_count = 0
            
            # Process each region configuration in the file
            for region_config in regions_config:
                if not region_config:
                    self.stdout.write(self.style.WARNING('No region data found in configuration entry'))
                    continue
                
                # Extract fields from the region configuration
                name = region_config.get('name')
                description = region_config.get('description')
                coordinates = region_config.get('coordinates')
                roi_type = region_config.get('roi_type')
                is_active = region_config.get('is_active', True)
                alerts_category = region_config.get('alerts_category')
                requirement = region_config.get('requirement')
                pose_keypoints = region_config.get('pose_keypoints', [])
                
                # NOTE: We don't extract or set 'criteria' directly
                # Let the model's save() method handle populating it from requirement and pose_keypoints
                
                if not name or not coordinates or not roi_type:
                    self.stdout.write(self.style.WARNING(
                        f'Skipping region with missing required fields: {region_config}'
                    ))
                    continue
                
                # Get camera (for now, use the first camera as default)
                # In a real scenario, you would want to specify which camera each region belongs to
                try:
                    camera = Camera.objects.first()
                    if not camera:
                        self.stdout.write(self.style.WARNING(
                            f'No cameras found, creating a default one for regions'
                        ))
                        # Create a default camera if none exists
                        camera = Camera.objects.create(
                            name="Default Camera",
                            rtsp_url="rtsp://example.com/stream",
                            status="active"
                        )
                
                    # Use get_or_create with name and camera as the unique identifier
                    region, created = RegionOfInterest.objects.get_or_create(
                        name=name,
                        defaults={
                            'description': description,
                            'coordinates': coordinates,
                            'roi_type': roi_type,
                            'is_active': is_active,
                            'alerts_category': alerts_category,
                            'requirement': requirement,
                            'pose_keypoints': pose_keypoints
                            # Note: criteria field is not set directly here
                            # The model's save() method will handle this
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(f'Created region: {name}'))
                    else:
                        # Check if any fields need updating
                        needs_update = False
                        if region.description != description:
                            region.description = description
                            needs_update = True
                        if region.coordinates != coordinates:
                            region.coordinates = coordinates
                            needs_update = True
                        if region.roi_type != roi_type:
                            region.roi_type = roi_type
                            needs_update = True
                        if region.is_active != is_active:
                            region.is_active = is_active
                            needs_update = True
                        if region.alerts_category != alerts_category:
                            region.alerts_category = alerts_category
                            needs_update = True
                        if region.requirement != requirement:
                            region.requirement = requirement
                            needs_update = True
                        if region.pose_keypoints != pose_keypoints:
                            region.pose_keypoints = pose_keypoints
                            needs_update = True
                        
                        # Note: We don't check or update criteria directly
                        # When pose_keypoints or requirement is updated and saved,
                        # the model's save() method will update criteria automatically
                            
                        if needs_update:
                            region.save()
                            updated_count += 1
                            self.stdout.write(f'Updated region: {name}')
                        else:
                            self.stdout.write(f'Region exists and is up to date: {name}')
                            
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing region {name}: {str(e)}'))
                    continue
                
            self.stdout.write(self.style.SUCCESS(
                f'Regions of interest loading complete: {created_count} created, {updated_count} updated'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading regions of interest: {str(e)}'))
            logger.exception('Error loading default regions of interest')
            
    def load_permanent_overlays(self):
        """
        Load and create permanent camera overlays from YAML fixture file
        """
        self.stdout.write(self.style.NOTICE('Loading permanent camera overlays from fixture file...'))
        
        # Define path to the default overlays YAML file
        fixtures_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'fixtures',
            'default_permanentoverlay.yaml'
        )
        
        if not os.path.exists(fixtures_path):
            self.stdout.write(self.style.WARNING(
                f'Permanent overlays fixture file not found at {fixtures_path}'
            ))
            return
            
        try:
            # Load the YAML configuration
            with open(fixtures_path, 'r') as f:
                config = yaml.safe_load(f)
                
            if not config or 'permanent_overlays' not in config:
                self.stdout.write(self.style.WARNING('No permanent overlays defined in fixture file'))
                return
                
            overlays_config = config['permanent_overlays']
            self.stdout.write(self.style.SUCCESS(
                f'Found {len(overlays_config)} permanent overlays in fixture file'
            ))
            
            # Count created and skipped overlays
            created_count = 0
            skipped_count = 0
            
            # Process each overlay in the configuration
            for overlay_data in overlays_config:
                name = overlay_data.get('name')
                camera_name = overlay_data.get('camera')
                coordinates = overlay_data.get('coordinates')
                
                if not name or not camera_name or not coordinates:
                    self.stdout.write(self.style.WARNING(
                        f'Skipping overlay with missing name, camera, or coordinates: {name}'
                    ))
                    skipped_count += 1
                    continue
                
                try:
                    # Find the camera by name
                    try:
                        camera = Camera.objects.get(name=camera_name)
                    except Camera.DoesNotExist:
                        self.stdout.write(self.style.WARNING(
                            f'Camera {camera_name} not found for overlay {name}, skipping'
                        ))
                        skipped_count += 1
                        continue
                    
                    # Check if this overlay already exists for this camera with the same text
                    # This allows for multiple overlays per camera, differentiated by their text value
                    text = overlay_data.get('text')
                    existing_overlay = PermanentCameraOverlay.objects.filter(
                        name=name, 
                        camera=camera,
                        text=text
                    ).first()
                    
                    if existing_overlay:
                        # Update existing overlay with new data
                        existing_overlay.description = overlay_data.get('description', '')
                        existing_overlay.coordinates = coordinates
                        existing_overlay.text = overlay_data.get('text')
                        existing_overlay.color = overlay_data.get('color', '#FF0000')
                        existing_overlay.save()
                        
                        created_count += 1  # Count updates as successful operations
                        self.stdout.write(self.style.SUCCESS(
                            f'Updated existing overlay: {name} for camera {camera.name}'
                        ))
                    else:
                        # Create a new permanent camera overlay
                        overlay = PermanentCameraOverlay.objects.create(
                            name=name,
                            description=overlay_data.get('description', ''),
                            camera=camera,
                            coordinates=coordinates,
                            text=overlay_data.get('text'),
                            color=overlay_data.get('color', '#FF0000')
                        )
                        
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(
                            f'Created new overlay: {name} for camera {camera.name}'
                        ))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing overlay {name}: {str(e)}'))
                    skipped_count += 1
                    continue
            
            self.stdout.write(self.style.SUCCESS(
                f'Permanent overlays loading complete: {created_count} created/updated, {skipped_count} skipped'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading permanent overlays: {str(e)}'))
            logger.exception('Error loading permanent overlays')
    
    def load_default_events(self):
        """
        Load and create default events from YAML fixture file
        """
        self.stdout.write(self.style.NOTICE('Loading default events from fixtures...'))
        
        # Define path to the default events YAML file
        fixtures_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'fixtures',
            'default_events.yaml'
        )
        
        if not os.path.exists(fixtures_path):
            self.stdout.write(self.style.WARNING(
                f'Default events fixture not found at {fixtures_path}'
            ))
            return
            
        try:
            # Load the YAML fixture
            with open(fixtures_path, 'r') as f:
                events_data = yaml.safe_load(f)
                
            self.stdout.write(self.style.SUCCESS(
                f'Found {len(events_data)} events in fixture file'
            ))
            
            # Count created events
            created_count = 0
            skipped_count = 0
            
            # Process each event in the fixture
            for event_data in events_data:
                # Get the event ID and other fields
                event_id = event_data.get('event_id')
                if not event_id:
                    self.stdout.write(self.style.WARNING('Event missing event_id, skipping'))
                    continue
                
                # Skip if this event already exists
                if CameraEvent.objects.filter(id=event_id).exists():
                    skipped_count += 1
                    self.stdout.write(f"Event {event_id} already exists, skipping")
                    continue
                
                try:
                    # First find the camera by name
                    camera_name = event_data.get('camera_name')
                    config_name = event_data.get('camera_layer_config_name')
                    
                    if not camera_name or not config_name:
                        self.stdout.write(self.style.WARNING(
                            f'Event {event_id} missing camera_name or camera_layer_config_name, skipping'
                        ))
                        continue
                        
                    try:
                        camera = Camera.objects.get(name=camera_name)
                    except Camera.DoesNotExist:
                        self.stdout.write(self.style.WARNING(
                            f'Camera {camera_name} not found for event {event_id}, skipping'
                        ))
                        continue
                    
                    # Now find the layer configuration for this camera and config name
                    try:
                        # Find camera layer configurations for this camera with the specified name
                        layer_config = CamerasLayersConfiguration.objects.get(
                            camera=camera,
                            name=config_name
                        )
                        self.stdout.write(f"Found layer config: {layer_config.name} for camera {camera.name}")
                    except CamerasLayersConfiguration.DoesNotExist:
                        # If no exact match, try to find any layer config for this camera
                        layer_configs = CamerasLayersConfiguration.objects.filter(camera=camera)
                        if not layer_configs.exists():
                            self.stdout.write(self.style.WARNING(
                                f'No camera layer configurations found for camera {camera_name}, skipping event {event_id}'
                            ))
                            continue
                        # Use the first available layer config
                        layer_config = layer_configs.first()
                        self.stdout.write(self.style.WARNING(
                            f'Using fallback layer config {layer_config.name} for event {event_id}'
                        ))
                    
                    # Create the event
                    event = CameraEvent.objects.create(
                        id=event_id,
                        camera_layer_config=layer_config,
                        event_type=event_data['event_type'],
                        # Use the provided timestamp or current time
                        timestamp=datetime.fromisoformat(event_data['timestamp'].replace('Z', '+00:00')) 
                                  if 'timestamp' in event_data else timezone.now(),
                        confidence=event_data['confidence'],
                        # Set the event severity (Critical or Warning based on the fixture)
                        event_severity='Critical' if event_data.get('event_severity', '').lower() == 'critical' else 'Warning',
                        bounding_boxes=event_data['bounding_boxes'],
                        is_reviewed=event_data['is_reviewed'],
                        is_suspicious=event_data['is_suspicious'],
                        review_notes=event_data['review_notes'] if event_data.get('review_notes') else None
                    )
                    
                    created_count += 1
                    self.stdout.write(self.style.SUCCESS(
                        f'Created event: {event_id} ({event_data["event_type"]}) for {camera.name}/{layer_config.name}'
                    ))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error creating event {event_id}: {str(e)}'))
                    continue
            
            self.stdout.write(self.style.SUCCESS(
                f'Default events loading complete: {created_count} created, {skipped_count} skipped'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading default events: {str(e)}'))
            logger.exception('Error loading default events')