#!/bin/bash
python manage.py migrate

# Uvicorn startup script for SquirrelSentry (ASYNC STREAMING MODE)
# This script launches the Django application with Uvicorn using async-compatible streaming
# for improved RTSP camera stream performance

# Set environment variables for Django and Uvicorn
export DJANGO_SETTINGS_MODULE=backend.settings
export DJANGO_URLS_MODULE=cameras.urls_async

# Enable Python asyncio debugging for better error messages
export PYTHONASYNCIODEBUG=1

python manage.py load_default_cameras_layers_models
python manage.py load_default_users

# Default parameters
HOST="0.0.0.0"
PORT="4000"

# Optimized for multiple camera streams
WORKERS=4  # Multiple workers to handle concurrent connections
WORKER_CONNECTIONS=1000  # Higher connection limit per worker
THREADS=8  # Thread count per worker
TIMEOUT=300  # Extend timeout for long-lived connections
KEEP_ALIVE=120  # Keep connections alive longer

LOG_LEVEL="info"
RELOAD=false  # Auto-reload on code changes

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --host=*)
      HOST="${1#*=}"
      shift
      ;;
    --port=*)
      PORT="${1#*=}"
      shift
      ;;
    --log-level=*)
      LOG_LEVEL="${1#*=}"
      shift
      ;;
    --reload)
      RELOAD=true
      shift
      ;;
    *)
      echo "Unknown parameter: $1"
      echo "Available options: --host=HOST --port=PORT --log-level=LEVEL --reload"
      exit 1
      ;;
  esac
done

# Print startup information
echo ""
echo "๐ Starting SquirrelSentry with Uvicorn (ASYNC STREAMING MODE) ๐"
echo "==========================================================="
echo "โ Host: $HOST"
echo "โ Port: $PORT"
echo "โ Worker Mode: Multi-worker ($WORKERS workers, optimized for concurrency)"
echo "โ Connections: $WORKER_CONNECTIONS per worker"
echo "โ Threads: $THREADS per worker"
echo "โ Timeout: $TIMEOUT seconds"
echo "โ Keep-Alive: $KEEP_ALIVE seconds"
echo "โ Log Level: $LOG_LEVEL"
echo "โ AsyncIO Debug: Enabled"
echo "โ Auto Reload: $([ "$RELOAD" == "true" ] && echo "Enabled" || echo "Disabled")"
echo "โ Streaming: Using async-compatible implementation"
echo "==========================================================="
echo "Visit: http://$HOST:$PORT/api/cameras/{camera_id}/stream/"
echo ""

# Build the uvicorn command
UVICORN_CMD="uvicorn backend.asgi:application \
    --host localhost \
    --port 4000 \
    --workers 4 \
    --limit-concurrency 1000 \
    --http httptools \
    --loop uvloop \
    --limit-max-requests 1000000 \
    --backlog 2048"

# Add reload flag if requested
if [ "$RELOAD" == "true" ]; then
    UVICORN_CMD="$UVICORN_CMD --reload"
    echo "Auto-reloading enabled - server will restart when code changes"
fi

# Start Uvicorn with optimized settings for streaming
echo "Starting server..."
exec $UVICORN_CMD
