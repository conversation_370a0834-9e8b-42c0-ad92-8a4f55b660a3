from django.contrib import admin
from django.utils import timezone
from user.models import CustomUser


class CustomUserAdmin(admin.ModelAdmin):
    list_display = ('email', 'first_name', 'last_name', 'is_approved', 'approval_date', 'sign_up_platform', 'last_login_datetime')
    list_filter = ('is_approved', 'sign_up_platform')
    search_fields = ('email', 'first_name', 'last_name')
    actions = ['approve_users']
    readonly_fields = ('approval_date',)
    list_editable = ('is_approved',)
    fieldsets = (
        (None, {'fields': ('email', 'username')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'contact_num')}),
        ('Approval', {'fields': ('is_approved', 'approval_date')}),
        ('Login info', {'fields': ('sign_up_platform', 'unique_platform_id', 'last_login_datetime', 'last_activity')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'role')}),
    )

    def approve_users(self, request, queryset):
        updated = queryset.update(is_approved=True, approval_date=timezone.now())
        self.message_user(request, f'{updated} users were successfully approved.')
    approve_users.short_description = "Approve selected users"
    
    def save_model(self, request, obj, form, change):
        # If is_approved changed from False to True, update the approval_date
        if 'is_approved' in form.changed_data and obj.is_approved:
            obj.approval_date = timezone.now()
        super().save_model(request, obj, form, change)


admin.site.register(CustomUser, CustomUserAdmin)
