from django.contrib.auth.validators import UnicodeUsernameValidator
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class CustomUserUsernameValidator(UnicodeUsernameValidator):
    regex = r'^[\w.@+\- ]+$'
    

class CustomUser(AbstractUser):
    class SignUpPlatformChoice(models.TextChoices):
        GOOGLE = 'google', "google"
        OUTLOOK = 'outlook', "outlook"
        
    class UserRoleChoice(models.TextChoices):
        ADMIN = 'admin', "admin"

    # User Accounts Information
    username_validator = CustomUserUsernameValidator()
    email = models.EmailField(unique=True)
    username = models.CharField(
        _('username'),
        max_length=150,
        help_text=_(
            'Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'),
        validators=[username_validator],
        db_index=True
    )
    cognito_id = models.CharField(max_length=200, null=True, db_index=True)
    unique_platform_id = models.CharField(max_length=200, null=True, db_index=True)
    contact_num = models.CharField(max_length=200, null=True, db_index=True)

    # Approval status for authenticated users
    is_approved = models.BooleanField(default=True)
    approval_date = models.DateTimeField(null=True, blank=True)
    
    last_login_datetime = models.DateTimeField(null=True)
    last_activity = models.DateTimeField(null=True)
    sign_up_platform = models.CharField(max_length=200, null=True, choices=SignUpPlatformChoice.choices)
    role = models.CharField(max_length=200, null=True, choices=UserRoleChoice.choices)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email
