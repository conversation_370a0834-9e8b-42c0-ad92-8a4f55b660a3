from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64
import hashlib
import os
import json
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class CryptoUtil:
    """
    Utility for decrypting login credentials from the frontend.
    Uses AES encryption compatible with CryptoJS AES.
    """
    
    @staticmethod
    def decrypt_credentials(encrypted_data):
        """
        Decrypt data that was encrypted by CryptoJS.AES.encrypt() on the frontend.
        
        Args:
            encrypted_data (str): Base64 encoded encrypted string
            
        Returns:
            dict: Decrypted credentials containing username and password
        """
        try:
            # Get the salt key from settings or directly from environment variable as fallback
            salt_key = settings.NEXT_PUBLIC_CREDENTIALS_SALT_KEY
        
            # Check if we have a valid salt key
            if not salt_key:
                logger.error("CREDENTIALS_SALT_KEY is not set in environment or settings")
                raise ValueError("Encryption key not configured properly")
                
            logger.debug(f"Using salt key for decryption (length: {len(salt_key)})")
                
            # Decode the base64 string
            data = base64.b64decode(encrypted_data)
            
            # Extract salt and ciphertext
            # CryptoJS prepends "Salted__" + 8 bytes salt
            salt = data[8:16]
            ciphertext = data[16:]
            
            # Derive key and IV using the same approach as CryptoJS
            # (OpenSSL EVP_BytesToKey with MD5)
            key_iv = b""
            prev = b""
            while len(key_iv) < 48:  # 32 bytes key (AES-256) + 16 bytes IV
                prev = hashlib.md5(prev + salt_key.encode('utf-8') + salt).digest()
                key_iv += prev
            
            key = key_iv[:32]  # AES-256 key
            iv = key_iv[32:48]  # 16 bytes IV
            
            # Decrypt using AES-256-CBC
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted_padded = cipher.decrypt(ciphertext)
            
            # Remove PKCS#7 padding
            decrypted_bytes = unpad(decrypted_padded, AES.block_size)
            decrypted_text = decrypted_bytes.decode('utf-8')
            
            # Parse JSON to get credentials
            credentials = json.loads(decrypted_text)
            
            # Debug log the decrypted credentials structure (without sensitive values)
            logger.debug(f"Decrypted credentials keys: {list(credentials.keys())}")
            
            # Explicitly check for required fields
            if 'password' not in credentials:
                logger.error("Missing 'password' field in decrypted credentials")
                raise ValueError("Required field 'password' is missing from decrypted credentials")
                
            if 'email' not in credentials and 'username' not in credentials:
                logger.error("Missing both 'email' and 'username' fields in decrypted credentials")
                raise ValueError("Required field 'email' or 'username' is missing from decrypted credentials")
            
            return credentials
            
        except Exception as e:
            # Log the error but don't expose details in the response
            logger.error(f"Decryption error: {str(e)}")
            raise ValueError("Failed to decrypt credentials")
