import logging
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Populates the database with test users for authentication testing'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username for the test user')
        parser.add_argument('--email', type=str, help='Email for the test user')
        parser.add_argument('--password', type=str, help='Password for the test user')
        parser.add_argument('--admin', action='store_true', help='Make the user an admin')

    def handle(self, *args, **options):
        username = options.get('username') or 'testuser'
        email = options.get('email') or '<EMAIL>'
        password = options.get('password') or 'password123'
        is_admin = options.get('admin', False)
        
        try:
            # Check if user already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(self.style.WARNING(f'User with email {email} already exists'))
                update = input('Do you want to update this user\'s password? (y/n): ')
                if update.lower() != 'y':
                    self.stdout.write(self.style.WARNING('Aborted.'))
                    return
                user = User.objects.get(email=email)
                user.set_password(password)
                user.save()
                self.stdout.write(self.style.SUCCESS(f'Updated password for user {email}'))
                return
            
            # Create new user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                is_active=True,
                is_approved=True,
                last_login_datetime=timezone.now()
            )
            
            # Set admin status if requested
            if is_admin:
                user.is_staff = True
                user.is_superuser = True
                user.save()
                role = 'admin'
            else:
                role = 'regular'
            
            self.stdout.write(self.style.SUCCESS(f'Successfully created {role} test user'))
            self.stdout.write(f'Username: {username}')
            self.stdout.write(f'Email: {email}')
            self.stdout.write(f'Password: {password}')
            
        except Exception as e:
            raise CommandError(f'Error creating test user: {str(e)}')
