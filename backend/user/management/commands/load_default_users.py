"""
Management command to load default users from YAML configuration
"""
import os
import yaml
import logging
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Loads default users from YAML configuration if they do not exist'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--update-password',
            action='store_true',
            help='Update passwords for existing users'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.NOTICE('Looking for default users configuration...'))
        
        # Define path to the default users YAML file
        fixtures_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'fixtures',
            'default_users.yaml'
        )
        
        if not os.path.exists(fixtures_path):
            self.stdout.write(self.style.WARNING(
                f'Default users configuration not found at {fixtures_path}'
            ))
            return
            
        try:
            # Load the YAML configuration
            with open(fixtures_path, 'r') as f:
                config = yaml.safe_load(f)
                
            if not config or 'users' not in config:
                self.stdout.write(self.style.WARNING('No users defined in configuration file'))
                return
                
            users_config = config['users']
            self.stdout.write(self.style.SUCCESS(
                f'Found {len(users_config)} users in configuration'
            ))
            
            # Count existing and new users
            existing_count = 0
            created_count = 0
            
            # Process each user in the configuration
            for user_data in users_config:
                username = user_data.get('username')
                email = user_data.get('email')
                password = user_data.get('password')
                
                if not username or not email or not password:
                    self.stdout.write(self.style.WARNING(
                        f'Skipping user with missing username, email, or password: {user_data}'
                    ))
                    continue
                    
                # Check if a user with this email already exists
                existing_user = User.objects.filter(email=email).first()
                if existing_user:
                    existing_count += 1
                    self.stdout.write(f'User already exists: {email}')
                    
                    # Update existing user properties if needed
                    update_needed = False
                    
                    # Check if properties need updating
                    if existing_user.username != username:
                        existing_user.username = username
                        update_needed = True
                    
                    if existing_user.is_staff != user_data.get('is_staff', False):
                        existing_user.is_staff = user_data.get('is_staff', False)
                        update_needed = True
                        
                    if existing_user.is_superuser != user_data.get('is_superuser', False):
                        existing_user.is_superuser = user_data.get('is_superuser', False)
                        update_needed = True
                        
                    if existing_user.is_active != user_data.get('is_active', True):
                        existing_user.is_active = user_data.get('is_active', True)
                        update_needed = True
                        
                    if existing_user.is_approved != user_data.get('is_approved', True):
                        existing_user.is_approved = user_data.get('is_approved', True)
                        update_needed = True
                    
                    # Update password if explicitly requested
                    update_password = options.get('update_password', False)
                    if update_password:
                        existing_user.set_password(password)
                        update_needed = True
                        self.stdout.write(self.style.WARNING(f'Updated password for user {email}'))
                    
                    # Save if any changes were made
                    if update_needed:
                        existing_user.save()
                        self.stdout.write(self.style.SUCCESS(f'Updated properties for user {email}'))
                    
                    continue
                
                # Create new user
                user = User(
                    username=username,
                    email=email,
                    is_staff=user_data.get('is_staff', False),
                    is_superuser=user_data.get('is_superuser', False),
                    is_active=user_data.get('is_active', True),
                    is_approved=user_data.get('is_approved', True),
                    last_login_datetime=timezone.now()
                )
                user.set_password(password)
                user.save()
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f'Created user: {email}'))
                
            total_count = len(users_config)
            self.stdout.write(self.style.SUCCESS(
                f'User loading complete: {created_count} created, {existing_count} updated/verified'
            ))
            
            if created_count + existing_count == total_count:
                self.stdout.write(self.style.SUCCESS(
                    f'All {total_count} users from configuration are now in the database'
                ))
            else:
                self.stdout.write(self.style.WARNING(
                    f'Not all users from configuration were processed: {created_count + existing_count}/{total_count}'
                ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading users: {str(e)}'))
            logger.exception('Error loading default users')
