import logging
from django.contrib.auth import login, logout
from django.utils import timezone
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status

from backend.settings import SESSION_COOKIE_NAME, SESSION_EXPIRY_DAYS
from utils.default_viewset_template import DefaultViewset
from utils.response_template import custom_success_response, custom_error_response, custom_success_response_with_cookies
from user.serializers import GetUserBasicInfo, EncryptedLoginSerializer


logger = logging.getLogger(__name__)

class PublicUserViewSet(DefaultViewset):
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['POST'], url_path='login')
    def login(self, request):
        """Secure encrypted login endpoint"""
        logger.info("Processing secure encrypted login")
        try:
            # Check if the payload contains a cipher field (secure login) or uses the old format
            if 'cipher' in request.data:
                # Encrypted login flow
                serializer = EncryptedLoginSerializer(data=request.data)
            else:
                # Legacy login attempt - reject it
                logger.warning("Rejected unencrypted login attempt")
                return custom_error_response(message="Unencrypted login is not supported", status_code=status.HTTP_400_BAD_REQUEST)
                
            serializer.is_valid(raise_exception=True)
            
            # Get the authenticated user from validated data
            user = serializer.validated_data['user']
            
            # Create session for the user
            request.session.create()
            login(request, user)
            request.session.set_expiry(SESSION_EXPIRY_DAYS * 24 * 60 * 60)
            session_id = request.session.session_key
            
            # Update last login timestamp
            user.last_login_datetime = timezone.now()
            user.save()
            
            logger.info(f"User logged in successfully: {user.email}")
            
            # Return user data with session cookie
            data = {
                "message": "Login successful",
                "user": GetUserBasicInfo(user, many=False).data
            }
            return custom_success_response_with_cookies(data, cookies={SESSION_COOKIE_NAME: session_id})
            
        except Exception as e:
            logger.error("Login failed", extra={"error": str(e)}, exc_info=True)
            return custom_error_response(message=str(e), status_code=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['GET'], url_path='check-auth')
    def check_auth(self, request):
        try:
            if request.user.is_authenticated:
                user = request.user
                data = GetUserBasicInfo(user, many=False).data
                return custom_success_response(data)
            else:
                return custom_error_response(message="Not authenticated", status_code=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            logger.error("An error occurred while checking authentication", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)

class UserSessionViewSet(DefaultViewset):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['GET'], url_path='verify-session')
    def verify_session(self, request):
        try:
            user = request.user

            logger.info("Verifying user session.", extra={"user": user})
            # The session is already verified by IsAuthenticated permission
            # Just return the success response
            data = {
                "message": "Session is valid"
            }

            # Update the last activity time
            user.last_activity = timezone.now()
            user.save()

            return custom_success_response(data)
            
        except Exception as e:
            logger.error("An error occurred while verifying session", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['GET'], url_path='logout')
    def logout_user(self, request):
        logger.info("Logging out user from session.")
        try:
            # Log out the user
            logout(request)
            data = {
                "message": "Successfully logged out"
            }
            return custom_success_response(data)
        except Exception as e:
            logger.error("An error occurred during logout", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['GET'], url_path='get_user_info')
    def get_user_info(self, request):
        logger.info("Retrieving user information.")
        try:
            user = request.user
            data = GetUserBasicInfo(user, many=False).data
            return custom_success_response(data)

        except Exception as e:
            logger.error("An error occurred while retrieving user info", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)
