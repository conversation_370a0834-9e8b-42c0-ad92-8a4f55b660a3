from django.contrib.auth import authenticate
from rest_framework import serializers
from user.models import CustomUser
from user.crypto_utils import CryptoUtil
import logging

logger = logging.getLogger(__name__)

class EncryptedLoginSerializer(serializers.Serializer):
    """Serializer for encrypted login credentials"""
    cipher = serializers.CharField(required=True)
    
    def validate(self, data):
        cipher = data.get('cipher')
        
        if not cipher:
            raise serializers.ValidationError("Encrypted credentials are required")
        
        try:
            # Decrypt the credentials
            credentials = CryptoUtil.decrypt_credentials(cipher)
            
            # Extract username/email and password
            username = credentials.get('username')
            email = credentials.get('email')
            password = credentials.get('password')
            
            if not (username or email) or not password:
                raise serializers.ValidationError("Invalid credential format")
                
            # If email is provided, use it as the username for authentication
            if email and not username:
                username = email
                
            # Authenticate the user
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError("Invalid credentials")
                
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled")
                
            data['user'] = user
            return data
            
        except ValueError as ve:
            logger.error(f"Decryption error: {str(ve)}")
            # Provide more specific error message for client debugging
            if "key not configured" in str(ve).lower():
                raise serializers.ValidationError("Encryption key configuration error")
            else:
                raise serializers.ValidationError("Failed to decrypt credentials")
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}")
            if "JSON" in str(e):
                raise serializers.ValidationError("Invalid encrypted data format")
            else:
                raise serializers.ValidationError("Authentication failed")

class GetUserBasicInfo(serializers.ModelSerializer):

    class Meta:
        model = CustomUser
        fields = ['id', 'email', 'first_name', 'last_name', 'last_login', 'date_joined', 'is_approved', 'is_staff']
