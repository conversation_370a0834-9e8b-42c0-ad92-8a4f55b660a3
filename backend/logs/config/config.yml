version: 1

formatters:
  simple:
    format: '%(asctime)s - %(levelname)s - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s'
    class: pythonjsonlogger.jsonlogger.JsonFormatter
  error:
    format: "%(asctime)s - %(levelname)s - <PID %(process)d:%(processName)s> - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s"
    class: pythonjsonlogger.jsonlogger.JsonFormatter

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: simple
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: simple
    filename: output_logs/info.log
    maxBytes: 10485760 # 10MB
    backupCount: 3
    encoding: utf8

root:
  level: DEBUG
  handlers: [console, file]
  propogate: no
