import base64
import logging
import pickle
import requests

import google_auth_oauthlib.flow
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.contrib.auth import login
from django.utils import timezone

from backend.settings import (
    GOOGLE_OAUTH_SCOPES,
    GOOGLE_OAUTH_CLIENT_ID,
    GOOGLE_OAUTH_CLIENT_SECRET,
    GOOGLE_OAUTH_REDIRECT_URI,
    JAVASCRIPT_ORIGIN,
    SESSION_EXPIRY_DAYS,
    SESSION_COOKIE_NAME,
    GOOGLE_PROJECT_ID
)
from utils.cryptography_service import FernetEncryptor
from utils.default_viewset_template import DefaultViewset
from utils.response_template import custom_success_response, custom_error_response, custom_success_response_with_cookies
from user.models import CustomUser


logger = logging.getLogger(__name__)

class GoogleOAuthSet(DefaultViewset):
    permission_classes = []
    
    @action(detail=False, methods=['GET'], url_path='authorize')
    def oauth_authorize(self, request):
        logger.info("Starting to process google oauth authorization.")
        try:
            config_dict = {"web":
                            {
                               "client_id":GOOGLE_OAUTH_CLIENT_ID,
                               "project_id":GOOGLE_PROJECT_ID,
                               "auth_uri":"https://accounts.google.com/o/oauth2/auth",
                               "token_uri":"https://oauth2.googleapis.com/token",
                               "auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs",
                               "client_secret":GOOGLE_OAUTH_CLIENT_SECRET,
                               "redirect_uris":[
                                   f"{GOOGLE_OAUTH_REDIRECT_URI}"
                                ],
                                   "javascript_origins":[
                                       f"{JAVASCRIPT_ORIGIN}"
                                ]
                            }
                        }

            # Initialize the flow
            flow = google_auth_oauthlib.flow.Flow.from_client_config(
                config_dict, scopes=GOOGLE_OAUTH_SCOPES)
            
            # Appending the redirect uri
            flow.redirect_uri = GOOGLE_OAUTH_REDIRECT_URI
            
            # customize state
            state = {
                'platform': 'google'
            }
            state_in_bytes = pickle.dumps(state)
            state_string = base64.b64encode(state_in_bytes).decode('utf-8')
            fernet_encryptor = FernetEncryptor()
            encrypted_state = fernet_encryptor.encrypt(state_string)

            # Retrieve the authorization url
            authorization_url, _ = flow.authorization_url(
                # This parameter enables offline access which gives your application
                # both an access and refresh token.
                access_type='offline',
                # This parameter enables incremental auth.
                include_granted_scopes='true',
                state=encrypted_state,
                prompt="consent")

            data = {
                    "message": authorization_url
                }
            logger.info("Return the authorization url in the response body.", extra={"authorization_url": authorization_url})
            return custom_success_response(data)
        
        except Exception as e:
            logger.error("An error has occured", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['GET'], url_path='oauth2callback')
    def oauth_callback(self, request):
        logger.info("Starting to process Google OAuth authorization callback.")
        try:
            query_params = self.request.query_params

            logger.debug("Validating query parameters.")
            for key_name in ['state', 'code']:
                if key_name not in query_params:
                    logger.error(f"Missing required query parameter: {key_name}")
                    raise Exception(f"{key_name} is not found in the query params.")

            logger.debug("Setting up OAuth configuration.")
            config_dict = {"web": {
                "client_id": GOOGLE_OAUTH_CLIENT_ID,
                "project_id": GOOGLE_PROJECT_ID,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_secret": GOOGLE_OAUTH_CLIENT_SECRET,
                "redirect_uris": [f"{GOOGLE_OAUTH_REDIRECT_URI}"],
                "javascript_origins": [f"{JAVASCRIPT_ORIGIN}"]
            }}
            
            logger.info("Decrypting and validating state.")
            encrypted_state = query_params['state']
            fernet_encryptor = FernetEncryptor()
            decrypted_state = fernet_encryptor.decrypt(encrypted_state)
            base_64_state = base64.b64decode(decrypted_state)
            state = pickle.loads(base_64_state)
            if state['platform'] != 'google':
                logger.error("Invalid OAuth state platform.")
                raise Exception("This is not a Google OAuth state.")

            logger.info("Initializing OAuth flow and fetching token.")
            flow = google_auth_oauthlib.flow.Flow.from_client_config(
                config_dict, scopes=GOOGLE_OAUTH_SCOPES, state=encrypted_state
            )
            flow.redirect_uri = GOOGLE_OAUTH_REDIRECT_URI
            flow.fetch_token(code=query_params['code'])
            credentials = flow.credentials

            logger.info("Verifying client credentials.")
            if credentials.client_id != GOOGLE_OAUTH_CLIENT_ID or credentials.client_secret != GOOGLE_OAUTH_CLIENT_SECRET:
                logger.error("Client ID or secret mismatch.")
                raise Exception("Client ID or secret mismatch")

            logger.info("Retrieving user info from Google.")
            userinfo_endpoint = "https://openidconnect.googleapis.com/v1/userinfo"
            headers = {"Authorization": f"Bearer {credentials.token}"}
            response = requests.get(userinfo_endpoint, headers=headers)
            userinfo = response.json()

            logger.info("Creating or updating user in database.")
            user, is_created = CustomUser.objects.get_or_create(
                email=userinfo['email'],
                sign_up_platform='google',
                unique_platform_id=userinfo['sub'],
                defaults={
                    'username': userinfo['email'],
                    'contact_num': userinfo['email'],
                    'first_name': userinfo['given_name'],
                    'last_name': userinfo['family_name'],
                    'last_login_datetime': timezone.now()
                }
            )
            logger.debug(f"User {'created' if is_created else 'updated'}: {user}")

            # Update the last login if user already exists
            if not is_created:
                user.last_login_datetime = timezone.now()
                user.save()

            logger.info("Setting up user session.")
            request.session.create()
            login(request, user)
            request.session.set_expiry(SESSION_EXPIRY_DAYS * 24 * 60 * 60)
            session_id = request.session.session_key
            logger.info(f"Session created with ID: {session_id}")

            msg = "Successfully logged in with Google."
            data = {
                "message": msg,
                "is_approved": user.is_approved  # Include approval status in response
            }
            logger.info(msg)
            return custom_success_response_with_cookies(data, cookies={SESSION_COOKIE_NAME: session_id})
        
        except Exception as e:
            logger.error("An error has occured", extra={"error": e.args[0]}, exc_info=True)
            return custom_error_response(message=e.args[0], status_code=status.HTTP_400_BAD_REQUEST)
    