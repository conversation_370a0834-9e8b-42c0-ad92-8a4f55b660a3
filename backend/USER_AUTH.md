# Django Authentication System Documentation

## Overview

This document details the authentication system implemented in our Django application. The system uses session-based authentication with cookies and supports Google OAuth for user registration and login.

## Configuration

### Environment Variables

```env
# Cookie Configuration
FRONTEND_COOKIE_SUBDOMAIN=.example.com  # Allows cookies for all subdomains under example.com
SESSION_EXPIRY_DAYS=7                   # Session validity duration in days
SESSION_COOKIE_NAME=session_id          # Name of the session cookie
```

### Cookie Domain Configuration

- `FRONTEND_COOKIE_SUBDOMAIN` determines where cookies can be accessed:
  - `.example.com` allows access from all subdomains under example.com
  - `localhost` for local development
  - Specific subdomain like `app.example.com` for restricted access

## User Creation and Session Management

### 1. User Creation (OAuth Flow)

When a user logs in through Google OAuth (`oauth_provider/views.py`):

1. **OAuth Callback Processing**:
   ```python
   # After successful OAuth verification
   user, is_created = CustomUser.objects.get_or_create(
       email=userinfo['email'],
       sign_up_platform='google',
       unique_platform_id=userinfo['sub'],
       defaults={
           'username': userinfo['email'],
           'first_name': userinfo['given_name'],
           'last_name': userinfo['family_name'],
           'last_login_datetime': timezone.now()
       }
   )
   ```

2. **Session Creation**:
   ```python
   request.session.create()
   login(request, user)
   request.session.set_expiry(SESSION_EXPIRY_DAYS * 24 * 60 * 60)
   session_id = request.session.session_key
   ```

### 2. Session Authentication

The `CustomSessionAuthentication` class (`utils/custom_session_cookies_authentication.py`) handles session validation:

1. **Cookie Extraction**:
   ```python
   session_key = request.COOKIES.get(SESSION_COOKIE_NAME)
   ```

2. **Session Validation**:
   ```python
   session = SessionStore(session_key=session_key)
   if not session.exists(session_key):
       return None
   ```

3. **User Retrieval and Validation**:
   ```python
   user = CustomUser.objects.get(pk=user_id)
   if not user.is_active:
       raise exceptions.AuthenticationFailed("User account is disabled.")
   ```

### 3. Session Management

The `UserSessionViewSet` (`user/views.py`) provides endpoints for session management:

1. **Session Verification**:
   ```python
   @action(detail=False, methods=['GET'], url_path='verify-session')
   def verify_session(self, request):
       user = request.user
       user.last_activity = timezone.now()
       user.save()
   ```

2. **User Logout**:
   ```python
   @action(detail=False, methods=['GET'], url_path='logout')
   def logout_user(self, request):
       logout(request)
   ```

3. **User Information Retrieval**:
   ```python
   @action(detail=False, methods=['GET'], url_path='get_user_info')
   def get_user_info(self, request):
       user = request.user
       data = GetUserBasicInfo(user, many=False).data
   ```

## Security Considerations

1. **Session Security**:
   - Sessions are stored in the database
   - Session IDs are cryptographically secure
   - Sessions expire after configured duration
   - Invalid sessions are automatically cleared

2. **Cookie Security**:
   - Cookies are domain-restricted
   - Should be transmitted over HTTPS only
   - Session cookie is HttpOnly
   - Configured subdomain controls access scope

3. **User Account Security**:
   - OAuth provides secure authentication
   - User activity is tracked
   - Inactive accounts are blocked
   - Last login time is recorded

## Session Lifecycle

1. **Creation**:
   - User logs in via Google OAuth
   - Session created in database
   - Session cookie sent to client

2. **Validation**:
   - Every request includes session cookie
   - Session validated against database
   - User status checked
   - Last activity updated

3. **Termination**:
   - User explicitly logs out
   - Session expires after configured time
   - User account becomes inactive

## Error Handling

1. **Invalid Session**:
   - Returns 401 Unauthorized
   - Clears invalid session
   - Requires re-authentication

2. **Inactive Account**:
   - Returns 401 Unauthorized
   - Prevents access to protected resources
   - Requires account reactivation

3. **Missing Authentication**:
   - Returns 401 Unauthorized
   - Redirects to login flow

## Best Practices

1. **Session Management**:
   - Keep session duration reasonable
   - Implement session cleanup
   - Track user activity

2. **Security**:
   - Use HTTPS everywhere
   - Configure cookie domains correctly
   - Implement rate limiting
   - Monitor failed attempts

3. **User Experience**:
   - Graceful session expiry handling
   - Clear error messages
   - Smooth re-authentication flow