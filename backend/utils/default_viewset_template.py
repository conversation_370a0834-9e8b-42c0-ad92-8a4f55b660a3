from rest_framework.viewsets import ViewSet
from rest_framework.exceptions import MethodNotAllowed


class DefaultViewset(ViewSet):
    def list(self, request):
        raise MethodNotAllowed('GET')

    def create(self, request):
        raise MethodNotAllowed('POST')

    def retrieve(self, request, pk=None):
        raise MethodNotAllowed('GET')

    def update(self, request, pk=None):
        raise MethodNotAllowed('PUT')

    def partial_update(self, request, pk=None):
        raise MethodNotAllowed('PUT')

    def destroy(self, request, pk=None):
        raise MethodNotAllowed('DELETE')
