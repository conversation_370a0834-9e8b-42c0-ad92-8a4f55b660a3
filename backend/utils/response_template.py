"""
This module contains the error response template.
"""

from rest_framework.response import Response
from rest_framework import status

from backend.settings import SECURE, FRONTEND_COOKIE_SUBDOMAIN


def custom_error_response(message: str, status_code: int) -> Response:
    """
    This function returns a custom error response.

    Args:
        message (str): The error message.
        status_code (int): The status code.

    Returns:
        Response: The custom error response.
    """
    error_dict = {
        "status": "error",
        "status_code": status_code,
        "message": message
    }

    return Response(error_dict, status=status_code)


def custom_success_response(data: dict, status_code: int = status.HTTP_200_OK) -> Response:
    """
    This function returns a custom success response.

    Args:
        message (str): The success message.
        status_code (int): The status code.

    Returns:
        Response: The custom success response.
    """
    success_dict = {
        "status": "success",
        "status_code": status_code,
        "data": data
    }

    return Response(success_dict, status=status_code)


def custom_success_response_with_cookies(data: dict, status_code: int = status.HTTP_200_OK, cookies: dict = None) -> Response:
    """
    This function returns a custom success response and sets cookies if provided.

    Args:
        data (dict): The success data.
        status_code (int): The status code.
        cookies (dict): Optional key-value pairs for cookies to be set.

    Returns:
        Response: The custom success response with cookies set if provided.
    """
    success_dict = {
        "status": "success",
        "status_code": status_code,
        "data": data
    }

    response = Response(success_dict, status=status_code)
    response["Access-Control-Allow-Credentials"] = "true"

    if cookies:
        for key, value in cookies.items():
            response.set_cookie(
                key=key,
                value=value,
                domain=FRONTEND_COOKIE_SUBDOMAIN,
                httponly=True,          # Prevents JavaScript access to the cookie
                secure=SECURE,            # Ensures cookie is sent over HTTPS only
                samesite='None'
            )

    return response
