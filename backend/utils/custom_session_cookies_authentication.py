from datetime import datetime

from backend.settings import SESSION_COOKIE_NAME
from django.contrib.sessions.backends.db import Session<PERSON><PERSON>
from rest_framework import authentication, exceptions

from user.models import CustomUser


class CustomSessionAuthentication(authentication.BaseAuthentication):
    
    def authenticate(self, request):
        """Authenticate the request using session cookie."""
        request.user = None
        
        # Get session key from cookie
        session_key = request.COOKIES.get(SESSION_COOKIE_NAME)
        if not session_key:
            return None
            
        # Get session from database
        session = SessionStore(session_key=session_key)
        if not session.exists(session_key):
            return None
            
        # Get user_id from session
        user_id = session.get('_auth_user_id')
        if not user_id:
            return None
            
        try:
            user = CustomUser.objects.get(pk=user_id)
            if not user.is_active:
                msg = "User account is disabled."
                raise exceptions.AuthenticationFailed(msg)
                
            return user, None
            
        except CustomUser.DoesNotExist:
            msg = "No user matching this session was found."
            raise exceptions.AuthenticationFailed(msg)
        except Exception as e:
            raise exceptions.AuthenticationFailed(str(e))
    
    def authenticate_header(self, request):
        """Return value for WWW-Authenticate header."""
        return 'Session'