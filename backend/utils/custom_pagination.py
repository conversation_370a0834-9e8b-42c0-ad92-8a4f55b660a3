from rest_framework.pagination import PageNumberPagination
from .response_template import custom_success_response

class CustomPagination(PageNumberPagination):
    page_size = 50  

    def get_paginated_response(self, data, **kwargs):
        return custom_success_response({
            'total_pages': self.page.paginator.num_pages,
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
            **kwargs
        })


class CustomPaginationWithoutCustomResponse(PageNumberPagination):
    page_size = 50  

    def get_paginated_response(self, data, **kwargs):
        return {
            'total_pages': self.page.paginator.num_pages,
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
            **kwargs
        }

