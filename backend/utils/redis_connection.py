import json
import os
import redis
import base64
from typing import Any, Optional, Dict, List, Union
from collections import defaultdict


class CameraCache:
    """
    Key–value cache for per-camera, per-time-frame data in Redis.

    Key format
    ----------
        <camera_id>_<timeframe>            e.g. "camA_20250526T011513"
        <camera_id>_<timeframe>_<layer_id> e.g. "camA_20250526T011513_L1"

    Examples
    --------
    >>> cache = CameraCache(ttl_default=600)
    >>> cache.set("camA", "20250526T011500", {"boxes": []})
    >>> cache.set("camA", "20250526T011500", {"mask": []}, layer_id="seg")
    >>> cache.get("camA", "20250526T011500")
    {'boxes': []}
    >>> cache.get("camA", "20250526T011500", layer_id="seg")
    {'mask': []}
    """

    # ------------------------------------------------------------------ #
    #  Construction / connection
    # ------------------------------------------------------------------ #

    def __init__(
        self,
        host: str | None = None,
        port: int | None = None,
        db: int | None = None,
        password: str | None = None,
        ttl_default: int = 6000,
        camera_id: str | None = None,
    ) -> None:
        print("Connecting to Redis...")
        self._redis = redis.Redis(
            host=host or os.getenv("REDIS_HOST"),
            port=port or int(os.getenv("REDIS_PORT")),
            db=db or int(os.getenv("REDIS_DB")),
            password=password or os.getenv("REDIS_PASS"),
            decode_responses=True,  # str in / str out so json loads easily
        )
        self._ttl_default = ttl_default
        print("Connected to Redis")

        # For streaming frame for the frontend
        self.camera_id = camera_id
        self.channel_name = f"{self.camera_id}:live_stream"
        self.publish_key = f"{self.camera_id}:last"

    # ------------------------------------------------------------------ #
    #  Public API
    # ------------------------------------------------------------------ #

    def set(
        self,
        key: str,
        value: Any,
        *,
        ttl: Optional[int] = None,
    ) -> None:
        """
        Cache *value* under the standardised key for (*camera_id*, *timeframe*[, *layer_id*]).
        The key expires automatically after *ttl* seconds (default = self._ttl_default).
        """
        payload = json.dumps(value)
        self._redis.set(name=key, value=payload, ex=ttl or self._ttl_default)

    def get(
        self,
        key: str,
        *,
        default: Any = None,
    ) -> Any:
        """
        Retrieve the cached value or return *default* if the key is missing / expired.
        """
        raw = self._redis.get(key)
        return default if raw is None else json.loads(raw)

    # ------------------------------------------------------------------ #
    #  Redis Operations API
    # ------------------------------------------------------------------ #

    def zrangebyscore(self, key: str, min_score: float, max_score: str) -> List[str]:
        """
        Return elements with scores between min_score and max_score.
        """
        return self._redis.zrangebyscore(key, min_score, max_score)

    def hgetall(self, key: str) -> Dict[str, str]:
        """
        Get all fields and values in a hash.
        """
        return self._redis.hgetall(key)

    def pipeline(self) -> redis.client.Pipeline:
        """
        Create a pipeline for batching operations.
        """
        return self._redis.pipeline()

    def hset(self, key: str, mapping: Dict[str, Any]) -> None:
        """
        Set multiple hash fields to multiple values.
        """
        self._redis.hset(key, mapping=mapping)

    def expire(self, key: str, seconds: int) -> None:
        """
        Set a key's time to live in seconds.
        """
        self._redis.expire(key, seconds)

    def zadd(self, key: str, mapping: Dict[str, float]) -> None:
        """
        Add one or more members to a sorted set, or update its score if it already exists.
        """
        self._redis.zadd(key, mapping)

    # ------------------------------------------------------------------ #
    #  Internals
    # ------------------------------------------------------------------ #

    @staticmethod
    def build_key(camera_id: str, timeframe: str, layer_id: str | None) -> str:
        """
        Assemble the canonical Redis key string.
        """
        if layer_id:
            return f"{camera_id}_{timeframe}_{layer_id}"
        return f"{camera_id}_{timeframe}"
    
    # ---------- 1. binary → JSON-friendly ---------------------------------
    @staticmethod
    def to_jsonable_bytes(data: bytes | bytearray) -> str:
        """Encode arbitrary binary as a base-64 ASCII string."""
        return base64.b64encode(data).decode("ascii")

    # ---------- 2. JSON-friendly → binary ---------------------------------
    @staticmethod
    def from_jsonable_bytes(encoded: str) -> bytes:
        """Decode the ASCII base-64 string back to the original bytes."""
        return base64.b64decode(encoded.encode("ascii"))
    
    # Streaming frame fo the frontend
    def publish_latest_frame(self, frame_key, camera_id=None):
        payload = {
            "frame_key": frame_key,
        }

        pipe = self._redis.pipeline()

        if camera_id:
            channel_name = f"{camera_id}:live_stream"
            publish_key = f"{camera_id}:last"
        else:
            channel_name = self.channel_name
            publish_key = self.publish_key

        pipe.set(publish_key, json.dumps(payload))   # serialize to JSON string
        pipe.publish(channel_name, "")       # tiny notification; payload could be a frame-id
        
        pipe.execute()

    def subscribe_to_channel(self):
        ps = self._redis.pubsub()
        ps.subscribe(self.channel_name)
        print(f"Subscribed to channel: {self.channel_name}")
        return ps
    
