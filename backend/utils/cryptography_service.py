from cryptography.fernet import Ferne<PERSON>
from backend.settings import FERNET_KEY

class FernetEncryptor:
    def __init__(self):
        encoded_key = FERNET_KEY.encode()
        self.fernet = Fernet(encoded_key)

    def encrypt(self, data):
        encrypted_data = self.fernet.encrypt(data.encode())
        return encrypted_data.decode()

    def decrypt(self, encrypted_data):
        decrypted_data = self.fernet.decrypt(encrypted_data.encode())
        return decrypted_data.decode()
