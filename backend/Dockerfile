FROM python:3.11-slim

# Creating the work directory
WORKDIR /usr/src/backend

# create the ubuntu user
RUN adduser --system --group ubuntu

# Update and upgrade the system packages
RUN apt-get update && \
    apt-get upgrade -y

# Installing dependencies for Gunicorn and healthcheck
RUN apt-get install -y \
    libpq-dev \
    zip \
    gcc \
    dos2unix \
    cmake \
    pkg-config \
    libdbus-1-dev \
    build-essential \
    curl

RUN apt-get -y update && apt-get install -y ffmpeg

# Copy requirements first for better caching
COPY --chown=ubuntu:ubuntu requirements.txt requirements.txt

# Install dependencies
RUN python -m pip install --no-cache-dir -r requirements.txt 

# Install gunicorn
RUN python -m pip install gunicorn

# Create necessary directories with correct permissions
RUN mkdir -p /nonexistent && \
    chown -R ubuntu:ubuntu /nonexistent

# Copy the rest of the application code
COPY . .

# ------------ License Checker ------------
# Install required packages
RUN mkdir -p /usr/src/backend/keys
RUN pip install --no-cache-dir cryptography
# Set all the files and directories to be read only and executable
RUN chmod -R 555 /usr/src

# Set execute permissions for startup script
RUN chmod 555 ./aws_start.sh
RUN chmod -R 111 ./Dockerfile

RUN mkdir -p ./output_logs && \
    chown -R ubuntu:ubuntu ./output_logs && \
    chmod 755 -R ./output_logs

# Ensure the backend package is in the Python path
ENV PYTHONPATH=/usr/src/backend

EXPOSE 4000
EXPOSE 4001
EXPOSE 4002
EXPOSE 4003

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Cleanup section (Worker Template)
RUN apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# change to the ubuntu user
USER ubuntu
