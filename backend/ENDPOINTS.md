# API Endpoints Documentation

## Authentication System

The authentication system uses session-based authentication with cookies. The custom session authentication is implemented in `CustomSessionAuthentication` class.

### Authentication Flow

1. When a user successfully logs in (either through Google OAuth or other means), a session is created and stored in the database
2. A session cookie (name defined by `SESSION_COOKIE_NAME`) is sent to the client
3. For subsequent requests, the client must include this cookie
4. The authentication system:
   - Extracts the session key from the cookie
   - Validates the session exists in the database
   - Retrieves and validates the associated user
   - Checks if the user account is active

### Error Handling

All endpoints follow a consistent error response format:

```json
{
    "error": true,
    "message": "Error message description"
}
```

Common HTTP status codes:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 500: Internal Server Error

## OAuth Endpoints

### Google OAuth Authorization

**Endpoint:** `/api/oauth/google/authorize`  
**Method:** GET  
**Authentication Required:** No

Initiates the Google OAuth process.

**Response:**
```json
{
    "error": false,
    "message": "https://accounts.google.com/o/oauth2/auth?..."
}
```

### Google OAuth Callback

**Endpoint:** `/api/oauth/google/oauth2callback`  
**Method:** GET  
**Authentication Required:** No

Handles the OAuth callback from Google.

**Query Parameters:**
- `state`: Encrypted state parameter
- `code`: Authorization code from Google

**Response:**
```json
{
    "error": false,
    "message": "Successfully logged in with Google"
}
```

**Cookies Set:**
- Session cookie (name defined by `SESSION_COOKIE_NAME`)

## User Session Endpoints

### Verify Session

**Endpoint:** `/api/user/verify-session`  
**Method:** GET  
**Authentication Required:** Yes

Verifies if the current session is valid.

**Response:**
```json
{
    "error": false,
    "message": "Session is valid"
}
```

### Logout

**Endpoint:** `/api/user/logout`  
**Method:** GET  
**Authentication Required:** Yes

Logs out the current user by invalidating their session.

**Response:**
```json
{
    "error": false,
    "message": "Successfully logged out"
}
```

### Get User Information

**Endpoint:** `/api/user/get_user_info`  
**Method:** GET  
**Authentication Required:** Yes

Retrieves information about the currently authenticated user.

**Response:**
```json
{
    "error": false,
    "data": {
        "id": "user_id",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "last_login": "2025-02-27T04:11:08Z"
    }
}
```

## Authentication Headers

For authenticated endpoints, include the session cookie in your requests:

```
Cookie: session_cookie_name=session_value
```

## Error Scenarios

### 1. Invalid Session

```json
{
    "error": true,
    "message": "No user matching this session was found."
}
```
Status Code: 401

### 2. Disabled Account

```json
{
    "error": true,
    "message": "User account is disabled."
}
```
Status Code: 401

### 3. Missing Authentication

```json
{
    "error": true,
    "message": "Authentication credentials were not provided."
}
```
Status Code: 401

### 4. OAuth Error

```json
{
    "error": true,
    "message": "[Specific OAuth error message]"
}
```
Status Code: 400

## Notes

1. All successful responses follow the format:
   ```json
   {
       "error": false,
       "data": {}, // Optional
       "message": "Success message" // Optional
   }
   ```

2. Session Management:
   - Sessions are stored in the database
   - Session expiry is configured via `SESSION_EXPIRY_DAYS`
   - User's last activity is updated on session verification

3. Security Considerations:
   - OAuth state parameter is encrypted using Fernet encryption
   - Session cookies should be transmitted over HTTPS only
   - User activity is logged for security monitoring