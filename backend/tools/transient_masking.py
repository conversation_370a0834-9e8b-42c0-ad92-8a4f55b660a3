#!/usr/bin/env python3
"""
Transient-Only Masking for Efficient SAM/Grounded-SAM Processing

This module implements a lightweight CPU-only delta filter that:
1. Builds a long-memory background model updated slowly
2. Compares each new frame to the background to identify transient pixels
3. Masks out static regions (with a solid color like blue) so only dynamic pixels remain
4. Outputs masked frames for SAM/Grounded-SAM processing
"""

import os
import cv2
import numpy as np
from pathlib import Path
import argparse
import time
import matplotlib.pyplot as plt
from tqdm import tqdm
import glob


class TransientMaskingEngine:
    def __init__(self, 
                 alpha=0.001,         # Background model update rate
                 diff_threshold=30,   # L1 difference threshold to trigger movement
                 downscale_factor=8,  # Downscale factor for faster diff computation
                 blue_mask_color=(255, 0, 0),  # BGR color for masking static regions
                 blur_size=5):        # Size of blur kernel for smoothing
        """
        Initialize the transient masking engine.
        
        Args:
            alpha: Background update rate (lower = slower updates)
            diff_threshold: Pixel-wise L1 difference threshold to mark transient pixels
            downscale_factor: Factor to downscale images for faster processing
            blue_mask_color: BGR color for masking static regions
            blur_size: Size of blur kernel for smoothing the difference mask
        """
        self.alpha = alpha
        self.diff_threshold = diff_threshold
        self.downscale_factor = downscale_factor
        self.blue_mask_color = blue_mask_color
        self.blur_size = blur_size
        
        # Initialize background model
        self.background_model = None
        self.is_initialized = False
        
        print(f"TransientMaskingEngine initialized with parameters:")
        print(f" - Alpha: {self.alpha}")
        print(f" - Diff Threshold: {self.diff_threshold}")
        print(f" - Downscale Factor: 1/{self.downscale_factor}")
        print(f" - Mask Color (BGR): {self.blue_mask_color}")
        
    def reset_background(self):
        """Reset the background model"""
        self.background_model = None
        self.is_initialized = False
    
    def initialize_background(self, frames):
        """
        Initialize background model from a set of frames.
        
        Args:
            frames: List of frames to initialize background from
        """
        if not frames:
            raise ValueError("No frames provided for background initialization")
            
        print(f"Initializing background model with {len(frames)} frames...")
        
        # Get first frame to determine dimensions
        first_frame = frames[0]
        h, w = first_frame.shape[:2]
        
        # Use first frame as initial background
        self.background_model = first_frame.copy().astype(np.float32)
        
        # Update with remaining frames
        for i, frame in enumerate(frames[1:], 1):
            # Slowly incorporate each frame
            self.update_background(frame)
            
        self.is_initialized = True
        print("Background model initialized")
        return self.background_model.astype(np.uint8)
    
    def update_background(self, frame):
        """
        Update background model with a new frame.
        
        Args:
            frame: New frame to update the background model
        """
        if not self.is_initialized:
            # First frame, just copy
            self.background_model = frame.copy().astype(np.float32)
            self.is_initialized = True
            return
            
        # Compute the slow-moving average
        # B[t+1] = (1 - alpha) * B[t] + alpha * F[t]
        self.background_model = (1 - self.alpha) * self.background_model + self.alpha * frame.astype(np.float32)
    
    def compute_transient_mask(self, frame):
        """
        Compute a binary mask of transient pixels.
        
        Args:
            frame: Input frame to compare against background
            
        Returns:
            Binary mask where 1 = transient pixels, 0 = static pixels
        """
        if not self.is_initialized:
            raise RuntimeError("Background model not initialized")
            
        h, w = frame.shape[:2]
        
        # Resize for faster processing
        small_frame = cv2.resize(frame, (w // self.downscale_factor, h // self.downscale_factor))
        small_bg = cv2.resize(self.background_model.astype(np.uint8), 
                             (w // self.downscale_factor, h // self.downscale_factor))
        
        # Compute absolute difference
        diff = cv2.absdiff(small_frame, small_bg)
        
        # Calculate L1 norm across channels
        if len(diff.shape) == 3:  # Color image
            diff_l1 = np.sum(diff, axis=2)
        else:  # Grayscale image
            diff_l1 = diff
            
        # Apply threshold to detect transient pixels
        mask_small = (diff_l1 > self.diff_threshold).astype(np.uint8) * 255
        
        # Apply morphological operations to clean up mask
        kernel = np.ones((3, 3), np.uint8)
        mask_small = cv2.morphologyEx(mask_small, cv2.MORPH_OPEN, kernel)
        mask_small = cv2.morphologyEx(mask_small, cv2.MORPH_CLOSE, kernel)
        
        # Apply smoothing blur
        mask_small = cv2.GaussianBlur(mask_small, (self.blur_size, self.blur_size), 0)
        
        # Resize back to original size using nearest neighbor to preserve binary values
        mask = cv2.resize(mask_small, (w, h), interpolation=cv2.INTER_NEAREST)
        
        # Normalize mask to binary values
        mask = (mask > 127).astype(np.uint8) * 255
        
        return mask
    
    def apply_mask(self, frame, mask):
        """
        Apply the transient mask to the frame, replacing static regions with blue.
        
        Args:
            frame: Original frame
            mask: Binary mask where 255 = transient pixels, 0 = static pixels
            
        Returns:
            Masked frame with static regions replaced by blue
        """
        # Create a copy of the original frame
        masked_frame = frame.copy()
        
        # Create blue screen background
        blue_background = np.zeros_like(frame)
        blue_background[:] = self.blue_mask_color
        
        # Apply mask: keep original pixels where mask is non-zero, use blue background where mask is zero
        masked_frame = np.where(mask[:, :, np.newaxis] > 0, masked_frame, blue_background)
        
        return masked_frame
    
    def process_frame(self, frame, update_bg=True):
        """
        Process a frame: compute transient mask and apply it.
        
        Args:
            frame: Input frame
            update_bg: Whether to update background model after processing
            
        Returns:
            Tuple of (masked_frame, transient_mask)
        """
        # Compute transient mask
        transient_mask = self.compute_transient_mask(frame)
        
        # Apply mask to frame
        masked_frame = self.apply_mask(frame, transient_mask)
        
        # Update background model if requested
        if update_bg:
            self.update_background(frame)
        
        return masked_frame, transient_mask
    

def visualize_results(original, background, mask, masked_frame):
    """
    Visualize the results of transient masking.
    
    Args:
        original: Original input frame
        background: Current background model
        mask: Binary transient mask
        masked_frame: Masked frame with static regions replaced
    """
    # Create a figure with 2x2 subplots
    plt.figure(figsize=(12, 10))
    
    # Original frame
    plt.subplot(2, 2, 1)
    plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
    plt.title('Original Frame')
    plt.axis('off')
    
    # Background model
    plt.subplot(2, 2, 2)
    plt.imshow(cv2.cvtColor(background.astype(np.uint8), cv2.COLOR_BGR2RGB))
    plt.title('Background Model')
    plt.axis('off')
    
    # Transient mask
    plt.subplot(2, 2, 3)
    plt.imshow(mask, cmap='gray')
    plt.title('Transient Mask')
    plt.axis('off')
    
    # Masked frame
    plt.subplot(2, 2, 4)
    plt.imshow(cv2.cvtColor(masked_frame, cv2.COLOR_BGR2RGB))
    plt.title('Masked Frame (Blue Screen)')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()


def process_separate_folders(bg_folder, transient_folder, output_folder, 
                          alpha=0.001, diff_threshold=30, visualize=True):
    """
    Process separate background and transient frame folders.
    
    Args:
        bg_folder: Path to folder containing background frames
        transient_folder: Path to folder containing transient frames to be masked
        output_folder: Path to save output masked frames and visualizations
        alpha: Background update rate
        diff_threshold: Pixel-wise difference threshold
        visualize: Whether to visualize results
    """
    # Create output folder if it doesn't exist
    output_dir = Path(output_folder)
    output_dir.mkdir(exist_ok=True)
    
    # Get list of background image files
    bg_files = sorted(glob.glob(str(Path(bg_folder) / "*.jpg")))
    
    if not bg_files:
        print(f"No background images found in {bg_folder}")
        return
    
    # Get list of transient image files
    transient_files = sorted(glob.glob(str(Path(transient_folder) / "*.jpg")))
    
    if not transient_files:
        print(f"No transient images found in {transient_folder}")
        return
    
    print(f"Processing frames:")
    print(f" - Using {len(bg_files)} background frames for model training")
    print(f" - Processing {len(transient_files)} transient frames")
    
    # Initialize masking engine
    masking_engine = TransientMaskingEngine(alpha=alpha, diff_threshold=diff_threshold)
    
    # Load background frames
    background_frames = []
    for file_path in tqdm(bg_files, desc="Loading background frames"):
        frame = cv2.imread(file_path)
        if frame is None:
            print(f"Warning: Could not read image {file_path}")
            continue
        background_frames.append(frame)
    
    # Initialize background model
    background = masking_engine.initialize_background(background_frames)
    
    # Save background model
    bg_path = output_dir / "background_model.jpg"
    cv2.imwrite(str(bg_path), background)
    print(f"Background model saved to {bg_path}")
    
    # Process transient frames
    for i, file_path in enumerate(tqdm(transient_files, desc="Processing transient frames")):
        # Read frame
        frame = cv2.imread(file_path)
        if frame is None:
            print(f"Warning: Could not read image {file_path}")
            continue
            
        # Process frame
        masked_frame, mask = masking_engine.process_frame(frame, update_bg=False)
        
        # Save results
        filename = Path(file_path).name
        base_name = Path(filename).stem
        
        # Save masked frame and mask
        masked_frame_path = output_dir / f"{base_name}_masked.jpg"
        mask_path = output_dir / f"{base_name}_mask.jpg"
        
        cv2.imwrite(str(masked_frame_path), masked_frame)
        cv2.imwrite(str(mask_path), mask)
        
        # Visualize if requested
        if visualize and i == 0:  # Only visualize first transient frame
            visualize_results(frame, background, mask, masked_frame)
    
    print(f"Processing complete! Results saved to {output_dir}")


def process_screenshot_folder(folder_path, output_folder, training_ratio=0.8, 
                             alpha=0.001, diff_threshold=30, visualize=True):
    """
    Process a folder of screenshots to build background model and generate masked frames.
    
    Args:
        folder_path: Path to folder containing screenshots
        output_folder: Path to save output masked frames and visualizations
        training_ratio: Ratio of images to use for background model training
        alpha: Background update rate
        diff_threshold: Pixel-wise difference threshold
        visualize: Whether to visualize results
    """
    # Create output folder if it doesn't exist
    output_dir = Path(output_folder)
    output_dir.mkdir(exist_ok=True)
    
    # Get list of image files, sorted by name
    image_files = sorted(glob.glob(str(Path(folder_path) / "*.jpg")))
    
    if not image_files:
        print(f"No images found in {folder_path}")
        return
    
    # Split into training and inference sets
    num_images = len(image_files)
    num_training = int(num_images * training_ratio)
    
    training_files = image_files[:num_training]
    inference_files = image_files[num_training:]
    
    print(f"Processing {num_images} images:")
    print(f" - Using {len(training_files)} images for background model training")
    print(f" - Using {len(inference_files)} images for inference")
    
    # Initialize masking engine
    masking_engine = TransientMaskingEngine(alpha=alpha, diff_threshold=diff_threshold)
    
    # Load training frames
    training_frames = []
    for file_path in tqdm(training_files, desc="Loading training frames"):
        frame = cv2.imread(file_path)
        if frame is None:
            print(f"Warning: Could not read image {file_path}")
            continue
        training_frames.append(frame)
    
    # Initialize background model
    background = masking_engine.initialize_background(training_frames)
    
    # Save background model
    bg_path = output_dir / "background_model.jpg"
    cv2.imwrite(str(bg_path), background)
    print(f"Background model saved to {bg_path}")
    
    # Process inference frames
    for i, file_path in enumerate(tqdm(inference_files, desc="Processing inference frames")):
        # Read frame
        frame = cv2.imread(file_path)
        if frame is None:
            print(f"Warning: Could not read image {file_path}")
            continue
            
        # Process frame
        masked_frame, mask = masking_engine.process_frame(frame, update_bg=False)
        
        # Save results
        filename = Path(file_path).name
        base_name = Path(filename).stem
        
        # Save masked frame and mask
        masked_frame_path = output_dir / f"{base_name}_masked.jpg"
        mask_path = output_dir / f"{base_name}_mask.jpg"
        
        cv2.imwrite(str(masked_frame_path), masked_frame)
        cv2.imwrite(str(mask_path), mask)
        
        # Visualize if requested
        if visualize and i == 0:  # Only visualize first inference frame
            visualize_results(frame, background, mask, masked_frame)
    
    print(f"Processing complete! Results saved to {output_dir}")


def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(
        description="Transient-Only Masking for Efficient SAM/Grounded-SAM Processing"
    )
    
    parser.add_argument(
        "--bg-folder", 
        default="rtsp_screenshots/original_background",
        help="Folder containing background frames"
    )
    
    parser.add_argument(
        "--transient-folder", 
        default="rtsp_screenshots/transient",
        help="Folder containing transient frames to be masked"
    )
    
    parser.add_argument(
        "--input", 
        default=None,
        help="Input directory containing screenshots (for backward compatibility)"
    )
    
    parser.add_argument(
        "--output", 
        default="transient_masks_output",
        help="Output directory for masked frames and visualizations"
    )
    
    parser.add_argument(
        "--alpha", 
        type=float, 
        default=0.001,
        help="Background model update rate"
    )
    
    parser.add_argument(
        "--threshold", 
        type=int, 
        default=30,
        help="Difference threshold for transient detection"
    )
    
    parser.add_argument(
        "--training-ratio", 
        type=float, 
        default=0.8,
        help="Ratio of images to use for background model training (only used with --input)"
    )
    
    parser.add_argument(
        "--no-visualize", 
        action="store_true",
        help="Disable visualization"
    )
    
    args = parser.parse_args()
    
    # Determine which mode to use
    if args.input is not None:
        # Backward compatibility mode
        print("Using single folder mode with automatic training/inference split")
        process_screenshot_folder(
            args.input,
            args.output,
            training_ratio=args.training_ratio,
            alpha=args.alpha,
            diff_threshold=args.threshold,
            visualize=not args.no_visualize
        )
    else:
        # New mode with separate background and transient folders
        print("Using separate background and transient folders mode")
        process_separate_folders(
            args.bg_folder,
            args.transient_folder,
            args.output,
            alpha=args.alpha,
            diff_threshold=args.threshold,
            visualize=not args.no_visualize
        )


if __name__ == "__main__":
    main()
