#!/usr/bin/env python3
"""
Region Selector Tool for SquirrelSentry

This script allows you to:
1. Connect to a video stream (or use a static image)
2. Click to define polygon points for regions of interest
3. Output coordinates in both absolute and normalized formats
4. Save the coordinates in a format compatible with the RegionOfInterest model

Usage:
    python region_selector.py --stream rtsp://username:password@*************/live
    or
    python region_selector.py --image /path/to/image.jpg

Controls:
    Left click: Add a point to the polygon
    Right click: Remove the last point
    'c' key: Clear all points
    's' key: Save the current polygon
    'q' key: Quit
"""

import argparse
import cv2
import numpy as np
import json
import os
import time
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegionSelector:
    def __init__(self, source, output_dir=None, roi_name=None, roi_type="region", exit_on_save=False):
        """
        Initialize the Region Selector
        
        Args:
            source: Path to video file, RTSP URL, or image file
            output_dir: Directory to save output files
            roi_name: Name of the region of interest
            roi_type: Type of ROI ("line" or "region")
            exit_on_save: Whether to exit after saving
        """
        self.source = source
        self.roi_name = roi_name or f"ROI_{int(time.time())}"
        self.roi_type = roi_type
        self.exit_on_save = exit_on_save
        
        # Create output directory if it doesn't exist
        self.output_dir = output_dir or Path.cwd() / "region_definitions"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize variables
        self.cap = None
        self.frame = None
        self.points = []
        self.frame_width = 0
        self.frame_height = 0
        self.drawing = False
        
        logger.info(f"Initializing Region Selector for source: {source}")
        
    def open_stream(self):
        """Open the video stream or image file"""
        if self.source.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tif', '.tiff')):
            # Input is an image file
            self.frame = cv2.imread(self.source)
            if self.frame is None:
                raise ValueError(f"Could not open image: {self.source}")
            self.frame_height, self.frame_width = self.frame.shape[:2]
            logger.info(f"Loaded image with dimensions {self.frame_width}x{self.frame_height}")
            return True
        else:
            # Input is a video stream
            self.cap = cv2.VideoCapture(self.source)
            if not self.cap.isOpened():
                raise ValueError(f"Could not open video source: {self.source}")
            
            # Get frame dimensions
            self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            logger.info(f"Connected to stream with dimensions {self.frame_width}x{self.frame_height}")
            return True
            
    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # Left click: Add a point
            self.points.append((x, y))
            logger.info(f"Added point at ({x}, {y})")
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            # Right click: Remove last point
            if len(self.points) > 0:
                removed = self.points.pop()
                logger.info(f"Removed point at {removed}")
    
    def get_normalized_coordinates(self):
        """Convert absolute coordinates to normalized (0-1 range)"""
        normalized_points = []
        for x, y in self.points:
            normalized_x = x / self.frame_width
            normalized_y = y / self.frame_height
            normalized_points.append((normalized_x, normalized_y))
        return normalized_points
    
    def format_coordinates_for_model(self):
        """Format coordinates as required by the RegionOfInterest model"""
        normalized_points = self.get_normalized_coordinates()
        formatted_coords = []
        
        for i, ((x, y), (normalized_x, normalized_y)) in enumerate(zip(self.points, normalized_points)):
            point = {
                "id": i,
                "x": x,
                "y": y,
                "normalizedX": normalized_x,
                "normalizedY": normalized_y
            }
            formatted_coords.append(point)
            
        return formatted_coords
    
    def save_coordinates(self):
        """Save coordinates to a JSON file"""
        if len(self.points) < 2:
            logger.warning("Not enough points to save (minimum 2 required)")
            return False
            
        formatted_coords = self.format_coordinates_for_model()
        
        # Create the output structure
        output = {
            "name": self.roi_name,
            "roi_type": self.roi_type,
            "coordinates": formatted_coords,
            "frame_width": self.frame_width,
            "frame_height": self.frame_height,
            "timestamp": time.time()
        }
        
        # Save to file
        filename = f"{self.roi_name.replace(' ', '_')}_{int(time.time())}.json"
        output_path = os.path.join(self.output_dir, filename)
        
        with open(output_path, 'w') as f:
            json.dump(output, f, indent=2)
            
        logger.info(f"Saved region definition to {output_path}")
        
        # Also print the coordinates to console
        print("\n=== REGION DEFINITION ===")
        print(json.dumps(output, indent=2))
        print("========================\n")
        
        return True
    
    def draw_overlay(self, frame):
        """Draw the polygon overlay on the frame"""
        # Draw points
        for i, (x, y) in enumerate(self.points):
            cv2.circle(frame, (x, y), 5, (0, 0, 255), -1)
            cv2.putText(frame, str(i), (x + 10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
        # Draw lines connecting points
        if len(self.points) > 1:
            for i in range(len(self.points) - 1):
                cv2.line(frame, self.points[i], self.points[i + 1], (0, 255, 0), 2)
                
            # If it's a region (not a line), connect the last point to the first
            if self.roi_type == "region" and len(self.points) > 2:
                cv2.line(frame, self.points[-1], self.points[0], (0, 255, 0), 2)
                
        return frame
    
    def run(self):
        """Main execution loop"""
        if not self.open_stream():
            logger.error("Failed to open stream")
            return
        
        window_name = f"Region Selector - {self.roi_name} ({self.roi_type})"
        cv2.namedWindow(window_name)
        cv2.setMouseCallback(window_name, self.mouse_callback)
        
        # Instructions overlay
        instructions = [
            "LEFT CLICK: Add point",
            "RIGHT CLICK: Remove last point",
            "C: Clear all points",
            "S: Save region",
            "Q: Quit"
        ]
        
        while True:
            # If using a video stream, get the next frame
            if self.cap is not None:
                ret, self.frame = self.cap.read()
                if not ret:
                    logger.error("Failed to read frame from stream")
                    break
            
            # Create a copy of the frame for drawing
            display_frame = self.frame.copy()
            
            # Draw the polygon
            display_frame = self.draw_overlay(display_frame)
            
            # Draw instructions
            for i, instruction in enumerate(instructions):
                y_pos = 30 + (i * 25)
                cv2.putText(display_frame, instruction, (10, y_pos), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Show the frame
            cv2.imshow(window_name, display_frame)
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("Exiting...")
                break
            elif key == ord('c'):
                logger.info("Clearing all points")
                self.points = []
            elif key == ord('s'):
                saved = self.save_coordinates()
                if saved and self.exit_on_save:
                    logger.info("Saved coordinates and exiting as requested")
                    break
        
        # Clean up
        if self.cap is not None:
            self.cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Region of Interest Selector Tool")
    
    source_group = parser.add_mutually_exclusive_group(required=True)
    source_group.add_argument("--stream", help="RTSP stream URL")
    source_group.add_argument("--image", help="Path to image file")
    
    parser.add_argument("--name", help="Name of the region of interest", default=None)
    parser.add_argument("--type", choices=["line", "region"], default="region",
                       help="Type of region (line or polygon)")
    parser.add_argument("--output", help="Directory to save output files", default=None)
    parser.add_argument("--exit-on-save", action="store_true", 
                       help="Exit after saving coordinates")
    
    args = parser.parse_args()
    
    # Determine the source
    source = args.stream if args.stream else args.image
    
    try:
        selector = RegionSelector(
            source=source,
            output_dir=args.output,
            roi_name=args.name,
            roi_type=args.type,
            exit_on_save=args.exit_on_save
        )
        selector.run()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
