#!/usr/bin/env python3
"""
Camera Stream Region Selector

This script allows you to:
1. Connect to an RTSP camera stream
2. Click to define polygon points for regions of interest
3. See the points highlighted and the polygon shaded
4. Print the coordinates in both absolute and normalized formats

Usage:
    python select_camera_region.py
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

try:
    import cv2
    import numpy as np
except ImportError:
    print("OpenCV and NumPy are required. Installing...")
    subprocess.run([sys.executable, "-m", "pip", "install", "opencv-python", "numpy"])
    import cv2
    import numpy as np

# Define camera streams
CAMERA_STREAMS = {
    "1": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/102",
    "2": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/202",
    "3": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/302",
    "4": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/402",
    "5": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/502",
    "8": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/802",
    "9": "rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/Channels/902",
}

class RegionSelector:
    def __init__(self, stream_url, display_width=960, display_height=540):
        self.stream_url = stream_url
        self.cap = None
        self.frame = None
        self.points = []
        self.drawing = False
        self.display_width = display_width  # Default display size width
        self.display_height = display_height  # Default display size height
        self.window_name = "Region Selector - Click to define points, press S to save, Q to quit"
    
    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # Left click: Add a point
            # Convert coordinates back to original scale
            original_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            scale_factor = min(self.display_width / original_width, self.display_height / original_height)
            
            # Convert display coordinates to original image coordinates
            original_x = int(x / scale_factor)
            original_y = int(y / scale_factor)
            
            self.points.append((original_x, original_y))
            print(f"Added point at ({original_x}, {original_y})")
            self.print_points_summary()
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            # Right click: Remove last point
            if len(self.points) > 0:
                removed = self.points.pop()
                print(f"Removed point at {removed}")
                self.print_points_summary()
                
    def print_points_summary(self):
        """Print a clear summary of all current points"""
        if not self.cap or not self.cap.isOpened():
            print("Stream not available for coordinate normalization")
            return
            
        # Get frame dimensions for normalization
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print("\n=== CURRENT POINTS ===")
        print(f"Frame dimensions: {width}x{height}")
        
        if not self.points:
            print("No points defined yet.")
        else:
            print(f"Total points: {len(self.points)}")
            print("{:<5} {:<12} {:<20}".format("ID", "ABSOLUTE (x,y)", "NORMALIZED (0-1 range)"))
            print("-" * 60)
            
            for i, (x, y) in enumerate(self.points):
                # Calculate normalized coordinates
                norm_x = round(x / width, 4)
                norm_y = round(y / height, 4)
                print("{:<5} {:<12} {:<20}".format(
                    i, f"({x}, {y})", f"({norm_x}, {norm_y})")
                )
            
            # Create a formatted array with both absolute and normalized coordinates
            formatted_points = []
            for i, (x, y) in enumerate(self.points):
                norm_x = round(x / width, 4)
                norm_y = round(y / height, 4)
                formatted_points.append({
                    "id": i,
                    "x": x,
                    "y": y,
                    "normalizedX": norm_x,
                    "normalizedY": norm_y
                })
            
            # Print as a JSON format for easy copying
            print("\nModel-compatible JSON format:")
            print(json.dumps(formatted_points, indent=2))
            
            # Also print simple arrays for convenience
            absolute_points = [[p[0], p[1]] for p in self.points]
            normalized_points = [[round(p[0]/width, 4), round(p[1]/height, 4)] for p in self.points]
            
            print("\nAbsolute points array:")
            print(json.dumps(absolute_points))
            
            print("\nNormalized points array:")
            print(json.dumps(normalized_points))
            
        print("=====================\n")
    
    def get_normalized_coordinates(self, width, height):
        """Convert absolute coordinates to normalized (0-1 range)"""
        normalized_points = []
        for x, y in self.points:
            normalized_x = x / width
            normalized_y = y / height
            normalized_points.append((normalized_x, normalized_y))
        return normalized_points
    
    def format_coordinates_for_model(self, width, height):
        """Format coordinates as required by the RegionOfInterest model"""
        normalized_points = self.get_normalized_coordinates(width, height)
        formatted_coords = []
        
        for i, ((x, y), (normalized_x, normalized_y)) in enumerate(zip(self.points, normalized_points)):
            point = {
                "id": i,
                "x": x,
                "y": y,
                "normalizedX": normalized_x,
                "normalizedY": normalized_y
            }
            formatted_coords.append(point)
            
        return formatted_coords
    
    def save_coordinates(self, width, height):
        """Save coordinates to a JSON file and print to console"""
        if len(self.points) < 2:
            print("Not enough points to save (minimum 2 required)")
            return False
            
        formatted_coords = self.format_coordinates_for_model(width, height)
        
        # Create the output structure
        output = {
            "name": f"ROI_{int(time.time())}",
            "roi_type": "region",  # Default to region
            "coordinates": formatted_coords,
            "frame_width": width,
            "frame_height": height,
            "timestamp": time.time()
        }
        
        # Create directory if it doesn't exist
        output_dir = Path.cwd() / "region_definitions"
        os.makedirs(output_dir, exist_ok=True)
        
        # Save to file
        filename = f"ROI_{int(time.time())}.json"
        output_path = output_dir / filename
        
        with open(output_path, 'w') as f:
            json.dump(output, f, indent=2)
            
        print(f"\nSaved region definition to {output_path}")
        
        # Also print the coordinates to console
        print("\n=== REGION DEFINITION ===")
        print(json.dumps(output, indent=2))
        print("========================\n")
        
        return True
    
    def run(self):
        """Main execution loop"""
        # Open the stream
        print(f"Opening stream: {self.stream_url}")
        self.cap = cv2.VideoCapture(self.stream_url)
        
        if not self.cap.isOpened():
            print(f"Error: Could not open video stream {self.stream_url}")
            return False
        
        # Get frame dimensions
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        print(f"Stream dimensions: {width}x{height}")
        
        # Create window and set mouse callback
        cv2.namedWindow(self.window_name)
        cv2.setMouseCallback(self.window_name, self.mouse_callback)
        
        # Instructions to show on frame
        instructions = [
            "LEFT CLICK: Add point",
            "RIGHT CLICK: Remove last point",
            "P: Print points to terminal",
            "S: Save region",
            "C: Clear all points",
            "Q: Quit"
        ]
        
        print("\nControls:")
        for instruction in instructions:
            print(f"- {instruction}")
        
        try:
            while True:
                # Read a frame from the stream
                ret, frame = self.cap.read()
                if not ret:
                    print("Error reading frame from stream")
                    break
                
                # Resize frame for display
                original_height, original_width = frame.shape[:2]
                scale_factor = min(self.display_width / original_width, self.display_height / original_height)
                new_width = int(original_width * scale_factor)
                new_height = int(original_height * scale_factor)
                resized_frame = cv2.resize(frame, (new_width, new_height))
                
                # Create a copy for drawing
                display = resized_frame.copy()
                
                # Draw the points and polygon
                if len(self.points) > 0:
                    # Convert original coordinates to display coordinates
                    original_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    original_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    scale_factor = min(self.display_width / original_width, self.display_height / original_height)
                    
                    display_points = []
                    for x, y in self.points:
                        display_x = int(x * scale_factor)
                        display_y = int(y * scale_factor)
                        display_points.append((display_x, display_y))
                    
                    # Draw points
                    for i, (x, y) in enumerate(display_points):
                        cv2.circle(display, (x, y), 5, (0, 0, 255), -1)  # Red circle
                        cv2.putText(display, str(i), (x + 10, y), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                    
                    # Draw lines between points
                    if len(display_points) > 1:
                        for i in range(len(display_points) - 1):
                            pt1 = display_points[i]
                            pt2 = display_points[i + 1]
                            cv2.line(display, pt1, pt2, (0, 255, 0), 2)  # Green line
                        
                        # Connect the last point to the first to close the polygon
                        if len(display_points) > 2:
                            cv2.line(display, display_points[-1], display_points[0], (0, 255, 0), 2)
                            
                            # Fill the polygon with semi-transparent color
                            overlay = display.copy()
                            points_array = np.array(display_points, dtype=np.int32)
                            cv2.fillPoly(overlay, [points_array], (0, 200, 0, 128))  # Green fill
                            cv2.addWeighted(overlay, 0.3, display, 0.7, 0, display)  # Blend images
                
                # Display instructions on the frame
                for i, text in enumerate(instructions):
                    y = 30 + (i * 30)
                    # Black background for legibility
                    cv2.putText(display, text, (10, y), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)
                    # White text
                    cv2.putText(display, text, (10, y), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)
                
                # Show the frame
                cv2.imshow(self.window_name, display)
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("Quitting...")
                    break
                elif key == ord('c'):
                    print("Clearing all points")
                    self.points = []
                elif key == ord('p'):
                    print("Printing current points:")
                    self.print_points_summary()
                elif key == ord('s'):
                    self.save_coordinates(width, height)
        
        finally:
            # Clean up
            self.cap.release()
            cv2.destroyAllWindows()
            return True

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the application header"""
    print("\n" + "=" * 80)
    print("                   SquirrelSentry Camera Region Selector")
    print("=" * 80)
    print("\nThis tool allows you to select regions of interest on camera streams.")
    print("You can click on the video to create polygon points and save the coordinates.\n")

def choose_stream():
    """Present a menu to choose a camera stream"""
    print("\nAvailable camera streams:")
    
    for key, url in CAMERA_STREAMS.items():
        # Extract a readable name from the URL
        if "://" in url:
            parts = url.split("://")[1].split("/")[0]
            if "@" in parts:
                parts = parts.split("@")[1]
            name = parts
        else:
            name = url
        
        print(f"{key}. {name}")
    
    print("C. Enter custom RTSP URL")
    print("Q. Quit")
    
    choice = input("\nSelect a stream (1-9, C, or Q): ").strip().upper()
    
    if choice == 'Q':
        return None
    elif choice == 'C':
        custom_url = input("Enter custom RTSP URL: ").strip()
        if custom_url:
            return custom_url
    elif choice in CAMERA_STREAMS:
        return CAMERA_STREAMS[choice]
    else:
        print("Invalid choice. Please try again.")
        return choose_stream()

def main():
    """Main function"""
    clear_screen()
    print_header()
    
    try:
        while True:
            # Choose stream
            stream_url = choose_stream()
            if not stream_url:
                print("\nExiting...")
                break
            
            # Ask for window size
            print("\nChoose display size:")
            print("1. Small (640x360)")
            print("2. Medium (960x540) [default]")
            print("3. Large (1280x720)")
            size_choice = input("Select size [2]: ").strip() or "2"
            
            if size_choice == "1":
                display_width, display_height = 640, 360
            elif size_choice == "3":
                display_width, display_height = 1280, 720
            else:
                display_width, display_height = 960, 540
            
            # Run the region selector
            try:
                selector = RegionSelector(stream_url, display_width, display_height)
                selector.run()
            except Exception as e:
                print(f"Error in region selector: {e}")
                import traceback
                traceback.print_exc()
            
            # Ask to continue
            choice = input("\nSelect another stream? (Y/N): ").strip().upper()
            if choice != 'Y':
                break
                
    except KeyboardInterrupt:
        print("\nProgram interrupted. Exiting...")
    
    print("\nThank you for using the Camera Region Selector!")

if __name__ == "__main__":
    main()
