#!/usr/bin/env python3
import cv2
import os
import time
import argparse
from pathlib import Path
import subprocess
from datetime import datetime

def ensure_opencv_installed():
    """Ensure OpenCV is installed."""
    try:
        import cv2
    except ImportError:
        print("OpenCV not found. Installing opencv-python...")
        subprocess.check_call(["pip", "install", "opencv-python"])
        print("OpenCV installed successfully.")
        import cv2

def capture_frames_from_camera(camera_id, rtsp_base_url, num_frames=10, use_tcp=True, output_dir=None):
    """
    Capture frames from a specific camera
    
    Args:
        camera_id: Camera ID (stream number)
        rtsp_base_url: Base RTSP URL to connect to
        num_frames: Number of frames to capture
        use_tcp: Whether to use TCP (more reliable) or UDP (faster)
        output_dir: Output directory to save frames
    
    Returns:
        bool: True if at least one frame was captured
    """
    # Format the stream URL for this specific camera
    rtsp_url = f"{rtsp_base_url}/{camera_id}"
    
    # Add TCP transport if requested
    if use_tcp and '?' not in rtsp_url:
        rtsp_url = f"{rtsp_url}?tcp"
    
    # Create output directory if not provided
    if output_dir is None:
        output_dir = Path("captured_frames") / datetime.now().strftime("%Y-%m-%d")
        output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Connecting to camera stream {camera_id}...")
    print(f"URL: {rtsp_url}")
    
    # Connect to RTSP stream
    cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
    
    if not cap.isOpened():
        print(f"Error: Could not open RTSP stream for camera {camera_id}. Check URL and network connection.")
        return False
    
    # Set OpenCV parameters for better performance
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Disable buffering to get latest frame
    
    print(f"Connected! Capturing {num_frames} frames from camera {camera_id}...")
    
    frames_captured = 0
    
    try:
        for i in range(num_frames):
            print(f"Capturing frame {i+1}/{num_frames} for camera {camera_id}...", end="\r")
            
            # Read frame
            ret, frame = cap.read()
            
            # Handle frame read failures
            if not ret or frame is None:
                print(f"\nWarning: Failed to read frame {i+1} from camera {camera_id}. Reconnecting...")
                
                # Try to reconnect
                cap.release()
                cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
                
                if not cap.isOpened():
                    print(f"Error: Failed to reconnect to camera {camera_id}.")
                    break
                
                # Try one more time to get a frame
                ret, frame = cap.read()
                
                if not ret or frame is None:
                    print(f"Error: Still couldn't read frame after reconnecting to camera {camera_id}. Skipping.")
                    continue
            
            # Save the frame with the requested naming pattern
            output_file = output_dir / f"camera_{camera_id}_default_bg_{i+1}.jpg"
            cv2.imwrite(str(output_file), frame)
            print(f"Saved frame {i+1} for camera {camera_id}      ")
            
            frames_captured += 1
            
            # Small delay between captures to avoid overwhelming the camera
            time.sleep(0.5)
    
    except KeyboardInterrupt:
        print(f"\nCapture interrupted by user for camera {camera_id}.")
    finally:
        cap.release()
    
    print(f"Completed! Captured {frames_captured} frames from camera {camera_id}.")
    return frames_captured > 0

def capture_daytime_frames(rtsp_base_url, num_frames=10, use_tcp=True):
    """
    Capture frames from all VA cameras
    
    Args:
        rtsp_base_url: Base RTSP URL to connect to (without stream number)
        num_frames: Number of frames to capture per camera
        use_tcp: Whether to use TCP (more reliable) or UDP (faster)
    
    Returns:
        bool: True if at least one frame was captured
    """
    # VA camera stream IDs
    va_camera_ids = [101, 201, 301, 401, 501, 801, 901]
    
    # Create output directory with date
    output_dir = Path("captured_frames") / datetime.now().strftime("%Y-%m-%d")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Starting capture of {num_frames} frames for each of the {len(va_camera_ids)} VA cameras")
    print(f"Images will be saved to: {output_dir.absolute()}")
    
    total_frames_captured = 0
    successful_cameras = 0
    
    try:
        for camera_id in va_camera_ids:
            print(f"\n===== Processing camera {camera_id} =====\n")
            success = capture_frames_from_camera(camera_id, rtsp_base_url, num_frames, use_tcp, output_dir)
            if success:
                total_frames_captured += num_frames
                successful_cameras += 1
    
    except KeyboardInterrupt:
        print("\nCapture process interrupted by user.")
    finally:
        cv2.destroyAllWindows()
    
    print(f"\nAll captures completed!")
    print(f"Successfully processed {successful_cameras} of {len(va_camera_ids)} cameras")
    print(f"Captured approximately {total_frames_captured} frames total")
    print(f"Images saved in: {output_dir.absolute()}")
    
    return total_frames_captured > 0

def main():
    parser = argparse.ArgumentParser(description="Capture frames from all VA cameras")
    parser.add_argument("--url", 
                      default="rtsp://admin:Pr0s3gur-smrt@************:554/Streaming/tracks", 
                      help="Base RTSP URL to connect to (without the stream ID)")
    parser.add_argument("--frames", type=int, default=10, 
                      help="Number of frames to capture per camera")
    parser.add_argument("--use-udp", action="store_true", 
                      help="Use UDP transport for RTSP (faster but less reliable)")
    
    args = parser.parse_args()
    
    # Ensure OpenCV is installed
    ensure_opencv_installed()
    
    # Capture frames from all VA cameras
    capture_daytime_frames(args.url, args.frames, not args.use_udp)

if __name__ == "__main__":
    main()
