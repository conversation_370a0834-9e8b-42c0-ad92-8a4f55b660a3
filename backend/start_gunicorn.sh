#!/bin/bash

# Run migrations first
python manage.py migrate

# Load default users if none exist
python manage.py load_default_users

# Load default cameras if none exist
python manage.py load_default_cameras_layers_models

# Gunicorn startup script for SquirrelSentry
# This script launches the Django application with Uvicorn using async-compatible streaming
# for improved RTSP camera stream performance

# Set environment variables for Django and Uvicorn
export DJANGO_SETTINGS_MODULE=backend.settings
export DJANGO_URLS_MODULE=cameras.urls_async

# Enable Python asyncio debugging for better error messages
export PYTHONASYNCIODEBUG=1

# Default parameters
HOST="0.0.0.0"
PORT="4000"
WORKERS=1  # Single worker mode is best for streaming with ffmpeg
LOG_LEVEL="info"
RELOAD=false  # Auto-reload on code changes

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --host=*)
      HOST="${1#*=}"
      shift
      ;;
    --port=*)
      PORT="${1#*=}"
      shift
      ;;
    --log-level=*)
      LOG_LEVEL="${1#*=}"
      shift
      ;;
    --reload)
      RELOAD=true
      shift
      ;;
    *)
      echo "Unknown parameter: $1"
      echo "Available options: --host=HOST --port=PORT --log-level=LEVEL --reload"
      exit 1
      ;;
  esac
done

# Print startup information
echo ""
echo "๐ Starting SquirrelSentry with Uvicorn (ASYNC STREAMING MODE) ๐"
echo "==========================================================="
echo "โ Host: $HOST"
echo "โ Port: $PORT"
echo "โ Worker Mode: Single worker (optimized for streaming)"
echo "โ Log Level: $LOG_LEVEL"
echo "โ AsyncIO Debug: Enabled"
echo "โ Auto Reload: $([ "$RELOAD" == "true" ] && echo "Enabled" || echo "Disabled")"
echo "โ Streaming: Using async-compatible implementation"
echo "==========================================================="
echo "Visit: http://$HOST:$PORT/api/cameras/{camera_id}/stream/"
echo ""

# Build the gunicorn command
GUNICORN="gunicorn backend.asgi:application \
    -k uvicorn.workers.UvicornWorker \
    --workers 3 \
    --bind $HOST:$PORT \
    --log-level $LOG_LEVEL \
    --keep-alive 300 \
    --worker-connections 1000 \
    --threads=10 \
    --graceful-timeout=30"

# # Add reload flag if requested
# if [ "$RELOAD" == "true" ]; then
#     echo "Auto-reloading enabled - server will restart when code changes"
# fi

# Start Uvicorn with optimized settings for streaming
echo "Starting server..."
exec $GUNICORN
