"""
ASGI config for SquirrelSentry project.

It exposes the ASGI callable as a module-level variable named ``application``.

This version supports both standard Django development server and Uvicorn ASGI server.
The actual URL routing and view selection (sync vs async) is handled in urls.py.

For more information on ASGI, see:
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import os

# Set up settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# Get the standard ASGI application
from django.core.asgi import get_asgi_application
application = get_asgi_application()
