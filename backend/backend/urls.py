"""SquirrelSentry URL Configuration

This URL configuration supports both standard Django development server and
Uvicorn ASGI server with async-compatible streaming views for RTSP cameras.

"""
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse
from cameras.urls import urlpatterns as cameras_urls

urlpatterns = [
    # This is to prevent search robot from crawling the site
    path('robots.txt', lambda *args: HttpResponse("User-Agent: *\nDisallow:", content_type="text/plain"), name="robots_file"),

    # Admin Dashboard
    path('squirrel-sentry-admin/', admin.site.urls),
    path('oauth-provider/', include('oauth_provider.urls')),
    path('user/', include('user.urls')),
    
    # Include camera URLs with the appropriate implementation based on environment
    path('api/', include((cameras_urls, 'cameras')))  # Include camera URLs with appropriate namespace
]
