import subprocess
import json
import os

from os.path import join
from pathlib import Path
from sys import platform
from dotenv import load_dotenv


def load_env():
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Load Secrets
    if 'linux' == platform and os.path.isdir('/opt/elasticbeanstalk/bin'):
        process = subprocess.Popen([r"/opt/elasticbeanstalk/bin/get-config",
                                    "environment"],
                                   stdout=subprocess.PIPE)
        process = process.communicate()[0]
        obj = json.loads(process)
        for key in obj:
            os.environ[key] = obj[key]

    if os.environ.get('SECRET_KEY') is None:
        dotenv_path = join(os.path.dirname(base_dir), '.env')
        load_dotenv(dotenv_path)

    if os.environ.get('SECRET_KEY') is None:
        dotenv_path = join(base_dir, '.env')
        load_dotenv(dotenv_path=dotenv_path)
