import multiprocessing
import os

from dotenv import load_dotenv

# Load Env first to ensure variables are available
load_dotenv(os.path.join(".", ".env"))

# Use environment variable for port or default to 4000
domain = os.environ.get("BACKEND_DOMAIN")
port = domain.split(":")[-1] if ":" in domain else "" 
bind = f"0.0.0.0:{port}"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
threads = 4

keepalive = 10
graceful_timeout = 240
timeout = 240

reload = True

# Environment variables loaded at the top of the file