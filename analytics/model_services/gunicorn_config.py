import multiprocessing
import os

# Bind to 0.0.0.0:4000
bind = "0.0.0.0:4010"

# Worker settings
workers = 1  # Single worker for CUDA
worker_class = "sync"  # Use sync workers for CUDA compatibility
preload_app = False  # Important: Don't preload the app

# Maximum number of simultaneous clients
worker_connections = 1000

# Number of threads per worker (not used with sync worker)
threads = 1

# Timeout configurations
keepalive = 10
graceful_timeout = 240
timeout = 240

# Enable auto-reload during development
reload = True

# Server hooks
def on_starting(server):
    """Called just before the master process is initialized."""
    import torch.multiprocessing as mp
    mp.set_start_method('spawn')

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def pre_fork(server, worker):
    """Called just prior to forking the worker."""
    pass

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("Server is ready. Spawning workers")
