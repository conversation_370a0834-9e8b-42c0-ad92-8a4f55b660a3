FROM python:3.11-slim

WORKDIR /app

# create the ubuntu user
RUN adduser --system --group ubuntu

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install libGL and other dependencies required for OpenCV/YOLO
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY . .

# Download YOLO models by running the script
RUN python functions/yolo_models/objects_detection.py
RUN python functions/yolo_models/pose_detection.py 
RUN python functions/yolo_models/segmentation.py
# RUN python functions/sammy/common_func.py

# Download the weights for the gammy
RUN apt-get update && \
    apt-get install -y wget && \
    rm -rf /var/lib/apt/lists/*
RUN mkdir -p /app/functions/gammy/weights && \
    wget -q -P /app/functions/gammy/weights https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth

# Set proper permissions for scripts and files
RUN chmod +x app.py gunicorn_config.py \
    && chmod -R 555 functions/

# ------------ License Checker ------------
# Install required packages
RUN mkdir -p /app/keys
RUN pip install --no-cache-dir cryptography
# Set all the files and directories to be read only and executable
RUN chmod -R 555 /app
RUN chmod -R 777 /tmp
RUN chmod -R 111 ./Dockerfile

RUN mkdir -p /nonexistent && chmod -R 777 /nonexistent

# Expose the port the app runs on
EXPOSE 4010

# change to the ubuntu user
USER ubuntu

# CMD ["bash", "aws_start.sh"]
CMD ["python", "start.py"]