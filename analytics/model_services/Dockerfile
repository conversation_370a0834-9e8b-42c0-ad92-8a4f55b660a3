# Build stage
FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir cryptography

# Download YOLO models by running the script
COPY functions/ ./functions/
# RUN python functions/yolo_models/objects_detection.py
RUN python functions/yolo_models/pose_detection.py 
# RUN python functions/yolo_models/segmentation.py

# Download the weights for the gammy
# RUN apt-get update && \
#     apt-get install -y wget && \
#     rm -rf /var/lib/apt/lists/*
# RUN mkdir -p /app/functions/gammy/weights && \
#     wget -q -P /app/functions/gammy/weights https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth

# Make build script executable and run it
COPY build_exes.sh .
COPY start.py .
COPY gunicorn_config.py .
COPY app.py .
COPY license/ ./license/
COPY db_connection.py .
COPY redis_connection.py .
COPY logger_setup.py .
COPY config_encryption_utils.py .
# COPY functions/gammy/ ./functions/gammy/
RUN chmod +x build_exes.sh
RUN ./build_exes.sh

# Final stage
FROM python:3.11-slim

WORKDIR /app

# create the ubuntu user
RUN adduser --system --group ubuntu

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Gunicorn
RUN pip install --no-cache-dir gunicorn

# Create necessary directories
RUN mkdir -p /app/keys /app/functions/yolo_models /app/license /app/_internal/mediapipe && \
    chown -R ubuntu:ubuntu /app

# Copy the executable and necessary files from builder
COPY --from=builder /app/dist/model_service /app/
COPY --from=builder /usr/local/lib/python3.11/site-packages/mediapipe/modules /app/_internal/mediapipe/modules
COPY --from=builder /app/functions/va_camera_backgrounds /app/functions/va_camera_backgrounds
COPY --from=builder /app/functions/yolo_models /app/functions/yolo_models
# COPY --from=builder /app/functions/detections_prompt.yml /app/functions/detections_prompt.yml
# COPY --from=builder /app/functions/gammy /app/functions/gammy
COPY --from=builder /app/gunicorn_config.py /app/
# COPY --from=builder /app/functions/gammy/weights /app/functions/gammy/weights

# Copy license files
COPY license/license.json /app/license/
COPY license/public_key.pem /app/license/

# Copy environment file
COPY .env /app/.env

RUN mkdir -p /app/output_logs && \
    chown -R ubuntu:ubuntu /app/output_logs && \
    chmod 755 -R /app/output_logs

RUN mkdir -p /app/configuration && \
    chown -R ubuntu:ubuntu /app/configuration && \
    chmod 755 -R /app/configuration

# Set permissions
RUN chmod +x /app/model_service && \
    chmod -R 555 /app/functions && \
    chmod -R 555 /app/license && \
    chmod -R 777 /app/_internal/mediapipe && \
    chmod -R 777 /tmp && \
    chown -R ubuntu:ubuntu /app && \
    chmod 444 /app/gunicorn_config.py

RUN mkdir -p /nonexistent && chmod -R 777 /nonexistent

# Expose the port the app runs on
EXPOSE 4010

# change to the ubuntu user
USER ubuntu

# Create a startup script that runs the app with Gunicorn
RUN echo '#!/bin/bash\n\
# Run the main application with environment variables\n\
export OMP_NUM_THREADS=3\n\
export MKL_NUM_THREADS=4\n\
export OPENBLAS_NUM_THREADS=3\n\
exec ./model_service' > /app/start.sh && \
    chmod +x /app/start.sh

CMD ["./start.sh"]