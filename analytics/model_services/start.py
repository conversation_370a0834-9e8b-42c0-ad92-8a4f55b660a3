# start.py
import app               # <— this ensures PyInstaller bundles app.py
import sys, os
import gunicorn.glogging     # <— ensures glogging.py is bundled
import gunicorn.app.base
import gunicorn.app.wsgiapp
import gunicorn.workers.sync
import gunicorn.http.wsgi
import gunicorn.util
from gunicorn.app.wsgiapp import run

if getattr(sys, "frozen", False):
    base = sys._MEIPASS
else:
    base = os.path.dirname(__file__)

# sys.argv = [
#     "gunicorn",
#     "--config", os.path.join(base, "gunicorn_config.py"),
#     "app:create_app()",
# ]
# run()

from license.license_checker import main
main()

# Get the PORT_NUMBER from the environment variable
port_number = os.getenv('PORT_NUMBER', 4020)

if __name__ == '__main__':
    flask_app = app.create_app()
    flask_app.run(host='0.0.0.0', port=port_number, use_reloader=False)