#!/bin/bash

# Exit on any error
set -e

echo "Installing PyInstaller..."
pip install pyinstaller

echo "Compiling start.py..."
pyinstaller --onedir \
    --name model_service \
    --add-data "license:license" \
    --add-data "gunicorn_config.py:." \
    --add-data "app.py:." \
    # --add-data "functions/yolo_models/models/yolov8s-pose:functions/yolo_models/models/yolov8s-pose" \
    # --add-data "functions/yolo_models/models/yolov10m:functions/yolo_models/models/yolov10m" \
    # --add-data "functions/yolo_models/models/yolov8s-seg:functions/yolo_models/models/yolov8s-seg" \
    --add-data "functions/gammy:functions/gammy" \
    --hidden-import=gunicorn \
    --hidden-import=gunicorn.app.base \
    --hidden-import=gunicorn.app.wsgiapp \
    --hidden-import=gunicorn.glogging \
    --hidden-import=gunicorn.workers.sync \
    --hidden-import=gunicorn.http.wsgi \
    --hidden-import=gunicorn.util \
    --hidden-import=gunicorn.workers.gthread \
    --hidden-import=cryptography \
    --hidden-import=pythonjsonlogger.jsonlogger \
    --hidden-import=pythonjsonlogger \
    --hidden-import=transformers \
    --hidden-import=transformers.models \
    --hidden-import=transformers.utils \
    --hidden-import=transformers.modeling_utils \
    --hidden-import=transformers.configuration_utils \
    --hidden-import=transformers.tokenization_utils \
    --hidden-import=groundingdino-py \
    --collect-all=groundingdino-py \
    start.py

echo "Build complete! Executable is in the dist/ directory" 