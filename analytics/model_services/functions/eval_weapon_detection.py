import yaml
import os
from typing import List, Dict, Any
from redis_connection import <PERSON><PERSON><PERSON>

config_path = os.path.join(os.path.dirname(__file__), "detections-prompt.yml")
with open(config_path, 'r') as f:
    config = yaml.safe_load(f)
    config = config.get('weapon', {})
    WEAPON_FILTERED_WORDS = config.get('filter_words', [])
    WEAPON_CONFIDENCE_THRESHOLD = config.get('confidence_threshold', 0.3)

class WeaponDetectionEvaluator:
    def __init__(self):
        self.filter_words = WEAPON_FILTERED_WORDS
        self.confidence_threshold = WEAPON_CONFIDENCE_THRESHOLD
    
    def evaluate(self, camera_id: str, timestamp: str, objects_array: List[Dict[str, Any]]) -> List[str]:
        """
        Evaluate the dependency result and filter phrases containing weapon-related words
        while excluding phrases with specified exclude words.
        
        Args:
            dependency_result: Dictionary containing the dependency parsing results
            
        Returns:
            List of filtered phrases that potentially contain weapon references
        """
        filtered_objects = []

        frame_id = f"{camera_id}_{timestamp}"
        
        for object in objects_array:
            object_name = object.get('object_name', '').lower()
            
            # Check if any filter word is in the phrase
            has_filter_word = any(word.lower() in object_name for word in self.filter_words)
            
            # Only add phrases that have filter words but no exclude words
            if has_filter_word and object.get('confidence', 0) > self.confidence_threshold:
                # Format the dictionary
                filtered_objects.append({
                    "state": "critical",
                    "oid": object.get('oid', ''),
                    "frame_id": frame_id,
                    "bounding_box": {
                        "x1": object.get('coordinates', {}).get('x1', 0),
                        "y1": object.get('coordinates', {}).get('y1', 0),
                        "x2": object.get('coordinates', {}).get('x2', 0),
                        "y2": object.get('coordinates', {}).get('y2', 0)
                    },
                    "reason": "Weapon detected"
                })

            elif has_filter_word:
                filtered_objects.append({
                    "state": "warning",
                    "oid": object.get('oid', ''),
                    "frame_id": frame_id,
                    "bounding_box": {
                        "x1": object.get('coordinates', {}).get('x1', 0),
                        "y1": object.get('coordinates', {}).get('y1', 0),
                        "x2": object.get('coordinates', {}).get('x2', 0),
                        "y2": object.get('coordinates', {}).get('y2', 0)
                    },
                    "reason": "Likely weapon detected"
                })

            else:
                filtered_objects.append({
                    "state": "normal",
                    "oid": object.get('oid', ''),
                    "frame_id": frame_id,
                    "bounding_box": {
                        "x1": object.get('coordinates', {}).get('x1', 0),
                        "y1": object.get('coordinates', {}).get('y1', 0),
                        "x2": object.get('coordinates', {}).get('x2', 0),
                        "y2": object.get('coordinates', {}).get('y2', 0)
                    },
                    "reason": object.get('object_name', '')
                })
        
        return filtered_objects

# Create an instance for use
def eval_offensive_weapon_detection(camera_id: str, timestamp: str, redis_client: CameraCache, **kwargs) -> List[str]:
    # Get the final results from Redis
    frame_key = redis_client.build_key(camera_id, timestamp, "object_tracking")
    objects_array = redis_client.get(frame_key)
    
    filtered_objects = []
    if objects_array:
        evaluator = WeaponDetectionEvaluator()
        filtered_objects = evaluator.evaluate(camera_id, timestamp, objects_array)

    results = {
        "cardinal_requirements": "offensive_weapon",
        "final_results": filtered_objects
    }

    return results
