import time, uuid, json, math, redis, torch, random
import numpy as np
from typing import List, <PERSON><PERSON>, Dict
import os
import cv2
from pathlib import Path

from redis_connection import CameraCache
from functions.commons_func import bytes_to_image


# ───────────────────────────────────────────────────────── helpers ────────────
def iou_gpu(boxA: torch.Tensor, boxB: torch.Tensor) -> torch.Tensor:
    """ boxA (N,4), boxB (M,4)  →  (N,M) IoU matrix """
    # Ensure boxes are 2D tensors
    if boxA.dim() == 1:
        boxA = boxA.unsqueeze(0)
    if boxB.dim() == 1:
        boxB = boxB.unsqueeze(0)
        
    xA = torch.maximum(boxA[:, None, 0], boxB[None, :, 0])
    yA = torch.maximum(boxA[:, None, 1], boxB[None, :, 1])
    xB = torch.minimum(boxA[:, None, 2], boxB[None, :, 2])
    yB = torch.minimum(boxA[:, None, 3], boxB[None, :, 3])

    inter = torch.clamp(xB - xA, min=0) * torch.clamp(yB - yA, min=0)
    areaA = (boxA[:, 2] - boxA[:, 0]) * (boxA[:, 3] - boxA[:, 1])
    areaB = (boxB[:, 2] - boxB[:, 0]) * (boxB[:, 3] - boxB[:, 1])
    iou = inter / torch.clamp(areaA[:, None] + areaB[None, :] - inter, min=1e-5)
    return iou


def colour_stats(frame: torch.Tensor, mask: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    frame : 3×H×W  uint8 CUDA
    mask  : H×W    bool  CUDA  (person segmentation)
    returns: top_mean(3), bottom_mean(3), combined_mean(3)
    """
    H = frame.shape[1]
    top_mask    = mask.clone()
    top_mask[H//2:] = False
    bottom_mask = mask.clone()
    bottom_mask[:H//2] = False

    def _mean(m):                    # m bool mask
        count = m.sum().item()
        if count == 0:
            return torch.zeros(3, device=frame.device)
        return frame[:, m].float().mean(dim=1)

    top_mean    = _mean(top_mask)
    bottom_mean = _mean(bottom_mask)
    combined    = (top_mean + bottom_mean) / 2
    return top_mean, bottom_mean, combined


def colour_dist(a: torch.Tensor, b: torch.Tensor) -> float:
    """euclidean distance normalised to 0-1 (lower → more similar)"""
    return torch.linalg.norm(a - b).item() / 255.0

# ───────────────────────────────────────────────────────── main class ─────────
class GPURedisReID:
    CAM_TTL    = 300                   # s
    IOU_TH     = 0.3
    COL_TH     = 0.40               # distance threshold (0…1)

    def __init__(self, redis_conn: redis.Redis, cam_id: str):
        self.r   = redis_conn
        self.cam = cam_id

    # ------------ redis helpers ---------------------------------------------
    def _recent_pids_cam(self, now):
        return self.r.zrangebyscore(f"camera:{self.cam}:people", now - self.CAM_TTL, "+inf")

    def _recent_pids_all(self, now):
        return self.r.zrangebyscore("active:people", now - self.CAM_TTL, "+inf")

    def _load_snaps(self, pids: List[str]) -> Dict[str, Dict]:
        if not pids:
            return {}
        pipe = self.r.pipeline()
        for pid in pids:
            pipe.hgetall(f"color:{pid}:{self.cam}")
        res = pipe.execute()
        return {pid: snap for pid, snap in zip(pids, res) if snap}

    # ------------ core pipeline ---------------------------------------------
    def _generate_short_pid(self):
        """Generate a short 6-character ID using timestamp and random elements"""
        timestamp = int(time.time() * 1000)  # millisecond timestamp
        random_part = random.randint(0, 35)  # 0-35 for base36
        # Convert to base36 (0-9,a-z) and take last 6 chars
        pid = hex(timestamp)[2:] + hex(random_part)[2:]
        return pid[-6:].upper()

    def tag_frame(self,
                  frame_ts: float,
                  frame_tensor: torch.Tensor,
                  detections: List[Dict]) -> List[Dict]:
        """
        detections element keys:
            coordinates (dict with x1,y1,x2,y2),
            keypoints (dict),
            segmentation (dict with x,y lists)  (pixels),
        """
        now      = frame_ts
        pids_cam = self._recent_pids_cam(now)
        snaps    = self._load_snaps(pids_cam)

        # tensors for fast IoU
        if not detections:  # Handle empty detections
            return []

        det_boxes = torch.tensor([[d["coordinates"]["x1"], 
                                  d["coordinates"]["y1"],
                                  d["coordinates"]["x2"],
                                  d["coordinates"]["y2"]] for d in detections],
                                device=frame_tensor.device)

        if not snaps:  # Handle empty snaps
            snap_boxes = torch.empty((0,4), device=frame_tensor.device)
        else:
            snap_boxes = torch.tensor(
                [[float(json.loads(s["coordinates"])["x1"]),
                  float(json.loads(s["coordinates"])["y1"]),
                  float(json.loads(s["coordinates"])["x2"]),
                  float(json.loads(s["coordinates"])["y2"])] for s in snaps.values()],
                device=frame_tensor.device)

        matches   = {}          # det_idx -> pid
        free_det  = list(range(len(detections)))
        free_pids = list(snaps.keys())

        # ---------- 1. IoU + colour in same cam -----------------------------
        if snap_boxes.numel() > 0:
            iou_mat = iou_gpu(det_boxes, snap_boxes)
            for di in range(iou_mat.shape[0]):
                # take best IoU candidate
                best_j = torch.argmax(iou_mat[di]).item()
                if iou_mat[di, best_j] < self.IOU_TH:
                    continue

                pid = free_pids[best_j]
                # colour distance
                det = detections[di]
                top, bot, comb = self._colour_from_seg(frame_tensor, det)
                snap_top  = torch.tensor(list(map(float, snaps[pid]["top_mean"].split(","))),
                                         device=frame_tensor.device)
                snap_bot  = torch.tensor(list(map(float, snaps[pid]["bottom_mean"].split(","))),
                                         device=frame_tensor.device)
                d_top = colour_dist(top, snap_top)
                d_bot = colour_dist(bot, snap_bot)
                d_all = (d_top + d_bot) / 2
                if d_all < self.COL_TH:
                    matches[di] = pid

        # ---------- 2. fall back to global ----------------------------------
        global_loaded = False
        snaps_global  = {}
        for di in [idx for idx in free_det if idx not in matches]:
            if not global_loaded:
                pids_all   = self._recent_pids_all(now)
                snaps_global = self._load_snaps(pids_all)
                snap_boxes_all = torch.tensor(
                    [[float(json.loads(s["coordinates"])["x1"]),
                      float(json.loads(s["coordinates"])["y1"]),
                      float(json.loads(s["coordinates"])["x2"]),
                      float(json.loads(s["coordinates"])["y2"])] for s in snaps_global.values()],
                    device=frame_tensor.device) if snaps_global else torch.empty((0,4), device=frame_tensor.device)
                global_loaded = True

            pid = None
            if snap_boxes_all.numel():
                iou_row = iou_gpu(det_boxes[di:di+1], snap_boxes_all)[0]
                best_j  = torch.argmax(iou_row).item()
                if iou_row[best_j] >= self.IOU_TH:
                    pid_cand = list(snaps_global.keys())[best_j]
                    # colour check
                    det     = detections[di]
                    top, bot, comb = self._colour_from_seg(frame_tensor, det)
                    snap_top = torch.tensor(list(map(float, snaps_global[pid_cand]["top_mean"].split(","))),
                                            device=frame_tensor.device)
                    snap_bot = torch.tensor(list(map(float, snaps_global[pid_cand]["bottom_mean"].split(","))),
                                            device=frame_tensor.device)
                    d_all = (colour_dist(top, snap_top) + colour_dist(bot, snap_bot)) / 2
                    if d_all < self.COL_TH:
                        pid = pid_cand

            if pid:
                matches[di] = pid

        # ---------- 3. assign new PIDs where necessary ----------------------
        for di in free_det:
            if di not in matches:
                matches[di] = self._generate_short_pid()

        # ---------- 4. write back to redis ----------------------------------
        pipe = self.r.pipeline()
        active_update = {}
        results = []

        for di, pid in matches.items():
            det = detections[di]
            top, bot, comb = self._colour_from_seg(frame_tensor, det)

            # dictionary to return
            det["pid"] = pid
            det["colour_stats"] = {
                "top":    top.tolist(),
                "bottom": bot.tolist(),
                "mean":   comb.tolist()
            }
            results.append(det)

            # redis writes
            col_key = f"color:{pid}:{self.cam}"
            pipe.hset(col_key, mapping={
                "ts": now,
                "coordinates": json.dumps({
                    "x1": det["coordinates"]["x1"],
                    "y1": det["coordinates"]["y1"],
                    "x2": det["coordinates"]["x2"],
                    "y2": det["coordinates"]["y2"]
                }),
                "top_mean": ",".join(map(lambda x: f"{x:.2f}", top.tolist())),
                "bottom_mean": ",".join(map(lambda x: f"{x:.2f}", bot.tolist())),
            })
            pipe.expire(col_key, 300)
            pipe.hset(f"person:{pid}", mapping={
                "last_seen_ts"  : now,
                "last_seen_cam" : self.cam,
            })
            active_update[pid] = now

        if active_update:
            pipe.zadd(f"camera:{self.cam}:people", active_update)
            pipe.expire(f"camera:{self.cam}:people", self.CAM_TTL)
            pipe.zadd("active:people", active_update)
            pipe.expire("active:people", self.CAM_TTL)

        pipe.execute()
        return results

    # ------------ GPU colour helper ----------------------------------------
    def _colour_from_bbox(self, frame: torch.Tensor, det: Dict):
        """Fallback method to extract colors using just the bounding box when segmentation is not available"""
        # build mask from bounding box
        H, W = frame.shape[1:]
        x1 = max(0, int(det["coordinates"]["x1"]))
        y1 = max(0, int(det["coordinates"]["y1"]))
        x2 = min(W, int(det["coordinates"]["x2"]))
        y2 = min(H, int(det["coordinates"]["y2"]))
        
        mask = torch.zeros((H, W), dtype=torch.bool, device=frame.device)
        mask[y1:y2, x1:x2] = True
        return colour_stats(frame, mask)   # returns top, bottom, mean

    def _colour_from_seg(self, frame: torch.Tensor, det: Dict):
        # build mask
        H, W = frame.shape[1:]
        if "segmentation" in det:
            xs = torch.tensor(det["segmentation"]["x"], device=frame.device).long()
            ys = torch.tensor(det["segmentation"]["y"], device=frame.device).long()
            mask = torch.zeros((H, W), dtype=torch.bool, device=frame.device)
            mask[ys, xs] = True
            return colour_stats(frame, mask)   # returns top, bottom, mean
        else:
            # Fallback to bounding box if no segmentation
            return self._colour_from_bbox(frame, det)


def people_tracking(frame_bytes: bytes, redis_client: CameraCache, camera_id: str, timestamp: str, **kwargs) -> List[Dict]:
    """
    Detect people in a frame using a YOLO model and store the results in Redis.

    Args:
        frame_bytes (bytes): The input frame to process.
        redis_client (redis.Redis): The Redis client to use for storing results.
        camera_id (str): The ID of the camera to associate with the results.
    """

    reid = GPURedisReID(redis_client, camera_id)

    from app import DEVICE # To resolve round import error
    # Convert bytes to numpy array using the common function
    frame_np = bytes_to_image(frame_bytes)
    
    if DEVICE == "cuda":
        frame = torch.from_numpy(frame_np).cuda().permute(2,0,1)  # 3×H×W
    else:
        frame = torch.from_numpy(frame_np).permute(2,0,1)  # 3×H×W

    # Get the detections from the redis
    pose_key = redis_client.build_key(camera_id, timestamp, "get_pose_position")
    pose_detections = redis_client.get(pose_key)

    # Get Segmentation from the redis
    seg_key = redis_client.build_key(camera_id, timestamp, "detect_object_segmentation")
    seg_detections = redis_client.get(seg_key)

    # Merge the detections based on coordinates
    detections = []
    for pose_det in pose_detections:
        # Find matching segmentation by comparing coordinates
        matching_seg = next(
            (seg for seg in seg_detections 
                if abs(seg["coordinates"]["x1"] - pose_det["coordinates"]["x1"]) < 5 and
                abs(seg["coordinates"]["y1"] - pose_det["coordinates"]["y1"]) < 5),
            None
        )
        
        # Create detection with pose data
        detection = {
            "coordinates": pose_det["coordinates"],
            "keypoints": pose_det["keypoints"],
            "confidence": pose_det["confidence"]
        }
        
        # Add segmentation if available
        if matching_seg:
            detection["segmentation"] = matching_seg["segmentation"]
            
        detections.append(detection)

    tagged = reid.tag_frame(time.time(), frame, detections)

    return tagged
