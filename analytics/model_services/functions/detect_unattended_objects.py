# plan
# 1. get the detection, if no detection -> skip
# 2. get the stability mask
# 3. compare the bounding box with stability mask, and filter out the bounding box

import base64
import datetime
import io
import time
import json
import os

import cv2
import numpy as np
import pandas as pd
import pytz
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from functions.unattended_objects_helpers import (
    build_modal_frame,
    compute_overlap_percentages,
    debug_save_image,
    highlight_changes,
)
from redis_connection import CameraCache

CACHE_TTL = 60 * 5  # 5 minutes

CONFIG = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
IMAGE_DIFFERENCE_THRESHOLD = CONFIG["UNATTENDED_OBJECT"]["IMAGE_DIFFERENCE_THRESHOLD"]
GRID_ROWS = CONFIG["UNATTENDED_OBJECT"]["GRID_ROWS"]
GRID_COLS = CONFIG["UNATTENDED_OBJECT"]["GRID_COLS"]
OVERLAP_THRESHOLD = CONFIG["UNATTENDED_OBJECT"]["OVERLAP_THRESHOLD"]
DEBUG = CONFIG["UNATTENDED_OBJECT"].get("DEBUG", False)
USE_BACKGROUND_FROM_DISK = CONFIG["UNATTENDED_OBJECT"].get(
    "USE_BACKGROUND_FROM_DISK", True
)
UNATTENDED_DETECTION_DURATION = CONFIG["UNATTENDED_OBJECT"][
    "UNATTENDED_DETECTION_DURATION"
]


def map_camera_name_to_background_key(camera_name):
    """Map camera names to background keys"""
    camera_mapping = {
        "101": "cam_one",
        "201": "cam_two",
        "301": "cam_three",
        "401": "cam_four",
        "501": "cam_five",
        "801": "cam_eight",
        "901": "cam_nine",
        "video_one.mp4": "cam_one",
        "video_two.mp4": "cam_two",
        "video_three.mp4": "cam_three",
        "video_four.mp4": "cam_four",
        "video_five.mp4": "cam_five",
        "video_eight.mp4": "cam_eight",
        "video_nine.mp4": "cam_nine",
    }

    return camera_mapping.get(camera_name, camera_name)


def fast_deserialize_grayscale(frame_bytes, redis_client: CameraCache):
    """Deserialize grayscale image from binary payload"""
    nparr = np.frombuffer(frame_bytes, np.uint8)
    cv_image = cv2.imdecode(nparr, cv2.IMREAD_GRAYSCALE)
    return cv2.resize(cv_image, (854, 480))


def load_camera_background_image(camera_name):
    try:
        default_bg = cv2.imread(
            f"functions/va_camera_backgrounds/{camera_name}/default_bg.jpg"
        )
        if default_bg is None:
            print(
                f"Unattended Objects: ⚠️ Could not load background image for {camera_name}"
            )
            return None
        default_bg = cv2.cvtColor(default_bg, cv2.COLOR_RGB2GRAY)
        default_bg = cv2.resize(default_bg, (854, 480))
        return default_bg
    except Exception as e:
        print(
            f"Unattended Objects: ⚠️ Error loading background image for {camera_name}: {e}"
        )
        return None


# Initialize background images with error handling
BACKGROUND_IMAGE = {}
background_cameras = [
    "cam_one",
    "cam_two",
    "cam_three",
    "cam_four",
    "cam_five",
    "cam_eight",
    "cam_nine",
]

for cam in background_cameras:
    bg_image = load_camera_background_image(cam)
    if bg_image is not None:
        BACKGROUND_IMAGE[cam] = bg_image
        print(f"Unattended Objects: ✅ Loaded background image for {cam}")
    else:
        print(
            f"Unattended Objects: ⚠️ Skipping {cam} due to failed background image loading"
        )

print(
    f"Unattended Objects: 📸 Successfully loaded {len(BACKGROUND_IMAGE)} background images"
)


def serialize_np_array(array: np.ndarray) -> bytes:
    buffer = io.BytesIO()
    np.savez_compressed(buffer, data=array)
    return buffer.getvalue()


# Deserialize
def deserialize_np_array(byte_data: bytes) -> np.ndarray:
    buffer = io.BytesIO(byte_data)
    return np.load(buffer)["data"]


def encode_frame(frame: np.ndarray) -> str:
    # Use JPEG encoding with quality=80 for better performance while maintaining acceptable quality
    # ok, buf = cv2.imencode(".jpg", frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
    ok, buf = cv2.imencode(".png", frame)
    if not ok:
        raise ValueError("Failed to encode frame")
    return base64.b64encode(buf).decode("ascii")


def decode_frame(b64_str: str) -> np.ndarray:
    buf = base64.b64decode(b64_str)
    return cv2.imdecode(np.frombuffer(buf, np.uint8), cv2.IMREAD_GRAYSCALE)


def detect_unattended_objects(
    camera_name,
    camera_id,
    frame_key,
    frame_bytes,
    frame_history_ids,
    redis_client: CameraCache,
    **kwargs,
):
    """
    Detect unattended objects by comparing stable regions with control image.

    Args:
        cam_id (str): Camera ID
        frame_bytes (list): List of frames to analyze
        frame_history_ids (list): List of frame history IDs

    Returns:
        tuple: (list of detections, dict of results for visualization)
    """
    start_time = time.time()
    preprocessing_time = datetime.datetime.now(
        pytz.timezone("Asia/Singapore")
    ).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    step_start = time.time()  # Initialize step_start at the beginning
    print(
        f"Unattended Objects: 🚀 Starting unattended object detection for camera {camera_id}"
    )

    print("\n=== Starting Redis Stream Querying ===")
    prediction_results = None
    while prediction_results is None:
        prediction_results = redis_client.get(f"{frame_key}:gammy_output")

        # if time is more than 0.3 seconds, break
        if time.time() - start_time > 0.3:
            print("\nTimeout reached after 0.3 seconds")
            break

        if prediction_results is None:
            continue

        if prediction_results:
            print(
                f"Unattended Objects: Number of final results: {len(prediction_results)}"
            )

        break

    print("Done checking result for frame_key: ", frame_key)

    # Handle case where prediction_results is None
    if prediction_results is None:
        print("Unattended Objects: No prediction results found")
        return {
            "preprocessing_time": preprocessing_time,
            "result": {
                "cardinal_requirements": "unattended_object",
                "final_results": [],
            },
        }

    print(f"Unattended Objects: Prediction results: {prediction_results}")
    print(f"Unattended Objects: mark a: {time.time() - start_time}")

    unattended_object_detection = []
    for result in prediction_results:
        if result["category"] == "unattended_object":
            unattended_object_detection.append(result)

    if len(unattended_object_detection) == 0:
        return {
            "preprocessing_time": preprocessing_time,
            "result": {
                "cardinal_requirements": "unattended_object",
                "final_results": [],
            },
        }

    # Reset step_start for accurate timing
    step_start = time.time()
    nparr = np.frombuffer(frame_bytes, np.uint8)
    current_gray = cv2.imdecode(nparr, cv2.IMREAD_GRAYSCALE)
    current_gray_resized = cv2.resize(current_gray, (854, 480))
    print(f"Unattended Objects: Current frame shape: {current_gray_resized.shape}")

    # ---------------------------- Handling caching ----------------------------
    # Retrieve the cache
    cache_key = f"{camera_id}:unattended_objects_gray"

    # Read from the /dev/shm/frame/unattended_objects/{camera_id}/unattended_objects_cache.json
    output_dir = f"/frame/unattended_objects/{camera_id}"

    # Create the directory if it does not exist
    os.makedirs(output_dir, exist_ok=True)

    # Check if file exists
    output_path = os.path.join(output_dir, "unattended_objects_cache.json")
    if os.path.exists(output_path):
        with open(output_path, "r") as f:
            cache_frames = json.load(f)
    else:
        cache_frames = []

    print(f"Unattended Objects: Cache frames: {len(cache_frames)}")

    # cache_frames = redis_client.get(cache_key, default=[])

    # Convert to dataframe and remove any row where the time is more than 5 minutes
    cv_frames = []
    if cache_frames:
        # Convert to dataframe and remove any row where the time is more than 5 minutes
        cache_df = pd.DataFrame(cache_frames)
        cache_df = cache_df[
            cache_df["time"] > time.time() - UNATTENDED_DETECTION_DURATION
        ]

        # Sort by time
        cache_df = cache_df.sort_values(by="time")

        # Convert back to list
        cache_frames = cache_df.to_dict(orient="records")

        # Convert to bytes
        cv_frames = cache_df["frame"].apply(decode_frame).values.tolist()

    # Adding the current frame to the cache
    cache_frames.append(
        {
            "time": time.time(),
            "frame": encode_frame(current_gray_resized),  # <-- encoded
        }
    )

    cv_frames.append(current_gray_resized)

    # if len(cache_frames) == 40:
    #     for frame in cache_frames:
    #         cv2.imwrite(
    #             f"/app/output_logs/unattended_object_cache_{frame['time']}.jpg",
    #             decode_frame(frame["frame"]),
    #         )

    # Saving the cache to the RAM under the RAM (/dev/shm)
    output_dir = f"/frame/unattended_objects/{camera_id}"
    os.makedirs(output_dir, exist_ok=True)
    output_path_temp = os.path.join(output_dir, "unattended_objects_cache_temp.json")
    output_path = os.path.join(output_dir, "unattended_objects_cache.json")
    with open(output_path_temp, "w") as f:
        json.dump(cache_frames, f)
    os.rename(output_path_temp, output_path)

    # # Saving the cache back to redis
    # redis_client.set(cache_key, cache_frames, ttl=CACHE_TTL)

    print(
        f"Frame history retrieval completed in {time.time() - step_start:.3f}s (retrieved {len(cv_frames)} frames)"
    )

    # Get background image for comparison
    step_start = time.time()
    background_key = map_camera_name_to_background_key(camera_name)

    # Check if we have a valid mapping for this camera
    if background_key is None:
        print(
            f"⚠️ No background image mapping found for camera {camera_name} (camera_id: {camera_id})"
        )
        print(
            f"Available camera mappings: Cam01, Cam02, Cam03, Cam04, Cam05, Cam08, Cam09"
        )
        return {
            "preprocessing_time": preprocessing_time,
            "result": {
                "cardinal_requirements": "unattended_object",
                "final_results": [],
            },
        }

    # Try to get the background image from Redis first
    control_frame = None
    redis_bg_key = f"{background_key}:background_image"
    redis_bg = redis_client.get(redis_bg_key)

    # First attempt: Try Redis background image
    if redis_bg and not USE_BACKGROUND_FROM_DISK:
        try:
            bg_bytes = redis_client.from_jsonable_bytes(redis_bg)
            control_frame = fast_deserialize_grayscale(bg_bytes)
            print(f"Using background image from Redis for {camera_name}")
        except Exception as e:
            print(f"Error loading Redis background for {camera_name}: {e}")
            control_frame = None  # Reset to try disk-based image next

    # Second attempt: Try disk-based background image if Redis failed
    if control_frame is None:
        try:
            control_frame = BACKGROUND_IMAGE.get(background_key)
            if control_frame is not None:
                print(f"Using disk-based background image for {camera_name}")
            else:
                print(
                    f"⚠️ No control frame found for camera {camera_name} (background_key: {background_key})"
                )
                print(
                    f"Unattended Objects: Available background images: {list(BACKGROUND_IMAGE.keys())}"
                )
                return {
                    "preprocessing_time": preprocessing_time,
                    "result": {
                        "cardinal_requirements": "unattended_object",
                        "final_results": [],
                    },
                }
        except Exception as e:
            print(
                f"Unattended Objects: ⚠️ Error loading disk-based control frame for {camera_id}: {e}"
            )
            return {
                "preprocessing_time": preprocessing_time,
                "result": {
                    "cardinal_requirements": "unattended_object",
                    "final_results": [],
                },
            }

    print(f"Unattended Objects: mark f: {time.time() - start_time}")

    # Compute stability mask with specified dimensions
    grid_rows = GRID_ROWS
    grid_cols = GRID_COLS
    fps = kwargs.get("fps", 0.5)
    print(f"Unattended Objects: fps: {fps}")

    step_start = time.time()

    modal_frame = build_modal_frame(
        cv_frames,
        grid_rows=grid_rows,
        grid_cols=grid_cols,
    )

    stability_mask = highlight_changes(
        modal_frame,
        control_frame,
        grid_rows=grid_rows,
        grid_cols=grid_cols,
        diff_threshold=IMAGE_DIFFERENCE_THRESHOLD,
    )
    filtered_detections = compute_overlap_percentages(
        unattended_object_detection,
        stability_mask,
        target_shape=current_gray.shape,
        overlap_threshold=OVERLAP_THRESHOLD,
    )
    if DEBUG:
        debug_save_image(
            modal_frame,
            unattended_object_detection,
            stability_mask,
            save_path=f"/app/output_logs/unattended_object_{frame_key}.jpg",
        )

    print(
        f"Unattended Objects: Filtered detections: {len(filtered_detections)} from {len(unattended_object_detection)}"
    )
    print("Unattended objects total time taken: ", time.time() - start_time)
    cache_key = f"{frame_key}:unattended_object"
    redis_client.set(cache_key, filtered_detections, ttl=360)

    return {
        "preprocessing_time": preprocessing_time,
        "result": {
            "cardinal_requirements": "unattended_object",
            "final_results": filtered_detections,
        },
    }
