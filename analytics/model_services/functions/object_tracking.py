import time, uuid, json, math, redis, torch, random
import numpy as np
from typing import List, <PERSON><PERSON>, Dict
import os
import cv2
from pathlib import Path

from redis_connection import CameraCache
from functions.commons_func import bytes_to_image


# ───────────────────────────────────────────────────────── helpers ────────────
def iou_gpu(boxA: torch.Tensor, boxB: torch.Tensor) -> torch.Tensor:
    """ boxA (N,4), boxB (M,4)  →  (N,M) IoU matrix """
    # Ensure boxes are 2D tensors
    if boxA.dim() == 1:
        boxA = boxA.unsqueeze(0)
    if boxB.dim() == 1:
        boxB = boxB.unsqueeze(0)
        
    xA = torch.maximum(boxA[:, None, 0], boxB[None, :, 0])
    yA = torch.maximum(boxA[:, None, 1], boxB[None, :, 1])
    xB = torch.minimum(boxA[:, None, 2], boxB[None, :, 2])
    yB = torch.minimum(boxA[:, None, 3], boxB[None, :, 3])

    inter = torch.clamp(xB - xA, min=0) * torch.clamp(yB - yA, min=0)
    areaA = (boxA[:, 2] - boxA[:, 0]) * (boxA[:, 3] - boxA[:, 1])
    areaB = (boxB[:, 2] - boxB[:, 0]) * (boxB[:, 3] - boxB[:, 1])
    iou = inter / torch.clamp(areaA[:, None] + areaB[None, :] - inter, min=1e-5)
    return iou


def colour_stats(frame: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
    """
    frame : 3×H×W  uint8 CUDA
    mask  : H×W    bool  CUDA  (object segmentation)
    returns: mean color of the object
    """
    def _mean(m):                    # m bool mask
        count = m.sum().item()
        if count == 0:
            return torch.zeros(3, device=frame.device)
        return frame[:, m].float().mean(dim=1)

    return _mean(mask)


def colour_dist(a: torch.Tensor, b: torch.Tensor) -> float:
    """euclidean distance normalised to 0-1 (lower → more similar)"""
    return torch.linalg.norm(a - b).item() / 255.0

# ───────────────────────────────────────────────────────── main class ─────────
class GPURedisObjectReID:
    CAM_TTL    = 300                   # s
    IOU_TH     = 0.3
    COL_TH     = 0.40               # distance threshold (0…1)

    def __init__(self, redis_conn: redis.Redis, cam_id: str):
        self.r   = redis_conn
        self.cam = cam_id

    # ------------ redis helpers ---------------------------------------------
    def _recent_oids_cam(self, now, object_name: str):
        return self.r.zrangebyscore(f"camera:{self.cam}:objects:{object_name}", now - self.CAM_TTL, "+inf")

    def _recent_oids_all(self, now, object_name: str):
        return self.r.zrangebyscore(f"active:objects:{object_name}", now - self.CAM_TTL, "+inf")

    def _load_snaps(self, oids: List[str], object_name: str) -> Dict[str, Dict]:
        if not oids:
            return {}
        pipe = self.r.pipeline()
        for oid in oids:
            pipe.hgetall(f"color:{oid}:{object_name}:{self.cam}")
        res = pipe.execute()
        return {oid: snap for oid, snap in zip(oids, res) if snap}

    # ------------ core pipeline ---------------------------------------------
    def _generate_short_oid(self):
        """Generate a short 6-character ID using timestamp and random elements"""
        timestamp = int(time.time() * 1000)  # millisecond timestamp
        random_part = random.randint(0, 35)  # 0-35 for base36
        # Convert to base36 (0-9,a-z) and take last 6 chars
        oid = hex(timestamp)[2:] + hex(random_part)[2:]
        return oid[-6:].upper()

    def tag_frame(self,
                  frame_ts: float,
                  frame_tensor: torch.Tensor,
                  detections: List[Dict]) -> List[Dict]:
        """
        detections element keys:
            object_name (str),
            coordinates (dict with x1,y1,x2,y2),
            segmentation (dict with x,y lists)  (pixels),
            confidence (float)
        """
        if not detections:
            return []

        # Group detections by class
        class_groups = {}
        for det in detections:
            object_name = det["object_name"]
            if object_name not in class_groups:
                class_groups[object_name] = []
            class_groups[object_name].append(det)

        all_results = []
        now = frame_ts

        # Process each class separately
        for object_name, class_detections in class_groups.items():
            oids_cam = self._recent_oids_cam(now, object_name)
            snaps = self._load_snaps(oids_cam, object_name)

            # tensors for fast IoU
            det_boxes = torch.tensor([[d["coordinates"]["x1"], 
                                    d["coordinates"]["y1"],
                                    d["coordinates"]["x2"],
                                    d["coordinates"]["y2"]] for d in class_detections],
                                    device=frame_tensor.device)

            if not snaps:  # Handle empty snaps
                snap_boxes = torch.empty((0,4), device=frame_tensor.device)
            else:
                snap_boxes = torch.tensor(
                    [[float(json.loads(s["coordinates"])["x1"]),
                    float(json.loads(s["coordinates"])["y1"]),
                    float(json.loads(s["coordinates"])["x2"]),
                    float(json.loads(s["coordinates"])["y2"])] for s in snaps.values()],
                    device=frame_tensor.device)

            matches = {}          # det_idx -> oid
            free_det = list(range(len(class_detections)))
            free_oids = list(snaps.keys())

            # ---------- 1. IoU + colour in same cam -----------------------------
            if snap_boxes.numel() > 0:
                iou_mat = iou_gpu(det_boxes, snap_boxes)
                for di in range(iou_mat.shape[0]):
                    # take best IoU candidate
                    best_j = torch.argmax(iou_mat[di]).item()
                    if iou_mat[di, best_j] < self.IOU_TH:
                        continue

                    oid = free_oids[best_j]
                    # colour distance
                    det = class_detections[di]
                    color_mean = self._colour_from_seg(frame_tensor, det)
                    snap_mean = torch.tensor(list(map(float, snaps[oid]["color_mean"].split(","))),
                                            device=frame_tensor.device)
                    d_color = colour_dist(color_mean, snap_mean)
                    if d_color < self.COL_TH:
                        matches[di] = oid

            # ---------- 2. fall back to global ----------------------------------
            global_loaded = False
            snaps_global = {}
            for di in [idx for idx in free_det if idx not in matches]:
                if not global_loaded:
                    oids_all = self._recent_oids_all(now, object_name)
                    snaps_global = self._load_snaps(oids_all, object_name)
                    snap_boxes_all = torch.tensor(
                        [[float(json.loads(s["coordinates"])["x1"]),
                        float(json.loads(s["coordinates"])["y1"]),
                        float(json.loads(s["coordinates"])["x2"]),
                        float(json.loads(s["coordinates"])["y2"])] for s in snaps_global.values()],
                        device=frame_tensor.device) if snaps_global else torch.empty((0,4), device=frame_tensor.device)
                    global_loaded = True

                oid = None
                if snap_boxes_all.numel():
                    iou_row = iou_gpu(det_boxes[di:di+1], snap_boxes_all)[0]
                    best_j = torch.argmax(iou_row).item()
                    if iou_row[best_j] >= self.IOU_TH:
                        oid_cand = list(snaps_global.keys())[best_j]
                        # colour check
                        det = class_detections[di]
                        color_mean = self._colour_from_seg(frame_tensor, det)
                        snap_mean = torch.tensor(list(map(float, snaps_global[oid_cand]["color_mean"].split(","))),
                                                device=frame_tensor.device)
                        d_color = colour_dist(color_mean, snap_mean)
                        if d_color < self.COL_TH:
                            oid = oid_cand

                if oid:
                    matches[di] = oid

            # ---------- 3. assign new OIDs where necessary ----------------------
            for di in free_det:
                if di not in matches:
                    matches[di] = self._generate_short_oid()

            # ---------- 4. write back to redis ----------------------------------
            pipe = self.r.pipeline()
            active_update = {}
            
            for di, oid in matches.items():
                det = class_detections[di]
                color_mean = self._colour_from_seg(frame_tensor, det)

                # Update detection with tracking info
                det["oid"] = oid
                det["colour_stats"] = {
                    "mean": color_mean.tolist()
                }
                all_results.append(det)

                # redis writes
                col_key = f"color:{oid}:{object_name}:{self.cam}"
                pipe.hset(col_key, mapping={
                    "ts": now,
                    "coordinates": json.dumps({
                        "x1": det["coordinates"]["x1"],
                        "y1": det["coordinates"]["y1"],
                        "x2": det["coordinates"]["x2"],
                        "y2": det["coordinates"]["y2"]
                    }),
                    "color_mean": ",".join(map(lambda x: f"{x:.2f}", color_mean.tolist())),
                })
                pipe.expire(col_key, 300)
                pipe.hset(f"object:{oid}", mapping={
                    "last_seen_ts": now,
                    "last_seen_cam": self.cam,
                    "object_name": object_name
                })
                active_update[oid] = now

            if active_update:
                pipe.zadd(f"camera:{self.cam}:objects:{object_name}", active_update)
                pipe.expire(f"camera:{self.cam}:objects:{object_name}", self.CAM_TTL)
                pipe.zadd(f"active:objects:{object_name}", active_update)
                pipe.expire(f"active:objects:{object_name}", self.CAM_TTL)

            pipe.execute()

        """
        [
            {
                "object_name": str,        # The name/class of the detected object
                "coordinates": {           # Bounding box coordinates
                    "x1": float,
                    "y1": float,
                    "x2": float,
                    "y2": float
                },
                "segmentation": {         # Segmentation data
                    "x": List[float],
                    "y": List[float]
                },
                "confidence": float,      # Detection confidence score
                "oid": str,              # Object tracking ID (added during tracking)
                "colour_stats": {        # Color statistics of the object
                    "mean": List[float]  # RGB color mean values
                }
            }
        ]
        """

        return all_results

    # ------------ GPU colour helper ----------------------------------------
    def _colour_from_bbox(self, frame: torch.Tensor, det: Dict):
        """Fallback method to extract colors using just the bounding box when segmentation is not available"""
        # build mask from bounding box
        H, W = frame.shape[1:]
        x1 = max(0, int(det["coordinates"]["x1"]))
        y1 = max(0, int(det["coordinates"]["y1"]))
        x2 = min(W, int(det["coordinates"]["x2"]))
        y2 = min(H, int(det["coordinates"]["y2"]))
        
        mask = torch.zeros((H, W), dtype=torch.bool, device=frame.device)
        mask[y1:y2, x1:x2] = True
        return colour_stats(frame, mask)

    def _colour_from_seg(self, frame: torch.Tensor, det: Dict):
        # build mask
        H, W = frame.shape[1:]
        if "segmentation" in det:
            xs = torch.tensor(det["segmentation"]["x"], device=frame.device).long()
            ys = torch.tensor(det["segmentation"]["y"], device=frame.device).long()
            mask = torch.zeros((H, W), dtype=torch.bool, device=frame.device)
            mask[ys, xs] = True
            return colour_stats(frame, mask)
        else:
            # Fallback to bounding box if no segmentation
            return self._colour_from_bbox(frame, det)


def object_tracking(frame_bytes: bytes, redis_client: CameraCache, camera_id: str, timestamp: str, **kwargs) -> List[Dict]:
    """
    Track objects in a frame and store the results in Redis.

    Args:
        frame_bytes (bytes): The input frame to process.
        redis_client (redis.Redis): The Redis client to use for storing results.
        camera_id (str): The ID of the camera to associate with the results.
        timestamp (str): The timestamp of the frame.
    """
    reid = GPURedisObjectReID(redis_client, camera_id)

    from app import DEVICE  # To resolve round import error
    # Convert bytes to numpy array using the common function
    frame_np = bytes_to_image(frame_bytes)
    
    if DEVICE == "cuda":
        frame = torch.from_numpy(frame_np).cuda().permute(2,0,1)  # 3×H×W
    else:
        frame = torch.from_numpy(frame_np).permute(2,0,1)  # 3×H×W

    # Get object detections from redis
    det_key = redis_client.build_key(camera_id, timestamp, "predict_with_gammy")
    object_detections = redis_client.get(det_key)

    # Get Segmentation from redis
    seg_key = redis_client.build_key(camera_id, timestamp, "detect_object_segmentation")
    seg_detections = redis_client.get(seg_key)

    if not object_detections and not seg_detections:
        return []

    # Merge the detections based on coordinates
    detections = []
    for obj_det in object_detections:
        matching_seg = None
        if obj_det.get("segmentation"):
            # Find matching segmentation by comparing coordinates
            matching_seg = next(
                (seg for seg in seg_detections 
                    if abs(seg["coordinates"]["x1"] - obj_det["coordinates"]["x1"]) < 5 and
                    abs(seg["coordinates"]["y1"] - obj_det["coordinates"]["y1"]) < 5 and
                    seg["object_name"] == obj_det["object_name"]),
                None
            )
        
        # Create detection with object data
        detection = {
            "object_name": obj_det["object_name"],
            "coordinates": obj_det["coordinates"],
            "confidence": obj_det["confidence"]
        }
        
        # Add segmentation if available
        if matching_seg:
            detection["segmentation"] = matching_seg["segmentation"]
            
        detections.append(detection)

    tagged = reid.tag_frame(time.time(), frame, detections)

    return tagged
