import torch
import os
import numpy as np
import cv2
from ultralytics import YOL<PERSON>

def check_cuda_availability():
    """
    Check if CUDA is available
    """
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    return device


def load_yolo_model(model_path, device):
    """
    Load a YOLO model from local path if exists, otherwise download it
    Args:
        model_path: Path or name of the YOLO model
        device: Device to load the model on ('cuda' or 'cpu')
    Returns:
        YOLO model loaded on specified device
    """    
    # Define local models directory
    local_models_dir = os.path.join(os.getcwd(), "functions", "yolo_models", f"models/{model_path}")
    os.makedirs(local_models_dir, exist_ok=True)
    
    # Check if model exists locally
    local_model_path = os.path.join(local_models_dir, f"{model_path}.pt")
    if os.path.exists(local_model_path):
        print(f"Loading model from local path: {local_model_path}")
        model = YOLO(local_model_path)
    else:
        print(f"Downloading model {model_path} to {local_model_path}")
        model = YOLO(model_path)
        # Save the model locally for future use
        model.save(local_model_path)
    
    return model.to(device)


def bytes_to_image(image_bytes):
    """
    Convert bytearray to image object that YOLO can process
    Args:
        image_bytes: Image in bytes/bytearray format
    Returns:
        Image that can be processed by YOLO model
    """
    print("Converting bytes to image...")
    # Convert bytes to numpy array
    nparr = np.frombuffer(image_bytes, np.uint8)
    
    # Decode numpy array to image
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    print("Image converted to numpy array")
    return img


def download_models(model_path):
    """
    Download YOLO models from Ultralytics
    """
    print("Downloading YOLO models...")
    
    # Check if model exists locally
    local_models_dir = os.path.join(os.path.dirname(__file__), f"models/{model_path}")
    os.makedirs(local_models_dir, exist_ok=True)
    local_model_path = os.path.join(local_models_dir, f"{model_path}.pt")
    
    model = YOLO(model_path)
    model.save(local_model_path)