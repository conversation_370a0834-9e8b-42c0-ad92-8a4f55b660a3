from collections import Counter

import cv2
import numpy as np


def get_mode_cell_index(cell_idx, cell_versions):
    averages = []
    for i, cell in enumerate(cell_versions):
        avg = int(np.round(np.mean(cell)))
        averages.append((avg, i))
    freqs = Counter(avg for avg, _ in averages)
    mode_val, _ = freqs.most_common(1)[0]
    for avg_val, img_idx in averages:
        if avg_val == mode_val:
            return img_idx


def build_modal_frame(frames, grid_rows=25, grid_cols=40):
    h, w = frames[0].shape
    y_edges = np.linspace(0, h, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, w, grid_cols + 1, dtype=int)

    # Step 1: slice each frame into cells
    all_cells = []  # List of lists of cells
    for frame in frames:
        cells = []
        for row in range(grid_rows):
            for col in range(grid_cols):
                y1, y2 = y_edges[row], y_edges[row + 1]
                x1, x2 = x_edges[col], x_edges[col + 1]
                cells.append(frame[y1:y2, x1:x2])
        all_cells.append(cells)

    # Step 2: build modal frame
    modal_frame = np.zeros((h, w), dtype=np.uint8)
    for cell_idx in range(grid_rows * grid_cols):
        # Get all versions of this cell from different frames
        cell_versions = [frame_cells[cell_idx] for frame_cells in all_cells]
        best_frame_idx = get_mode_cell_index(cell_idx, cell_versions)

        # Place the best cell in the modal frame
        row, col = divmod(cell_idx, grid_cols)
        y1, y2 = y_edges[row], y_edges[row + 1]
        x1, x2 = x_edges[col], x_edges[col + 1]
        modal_frame[y1:y2, x1:x2] = all_cells[best_frame_idx][cell_idx]

    return modal_frame


def highlight_changes(
    modal_frame, control_frame, grid_rows=25, grid_cols=40, diff_threshold=15
):
    """
    Compares modal_frame to control_frame cell-wise and highlights changed regions.

    Args:
        modal_frame (np.ndarray): Grayscale image representing background model.
        control_frame (np.ndarray): Grayscale image representing reference frame.
        grid_rows (int): Number of rows in the grid.
        grid_cols (int): Number of columns in the grid.
        diff_threshold (float): Intensity threshold to mark change.

    Returns:
        np.ndarray: Binary mask (1 where changed, 0 where stable), shape (grid_rows, grid_cols)
    """
    h, w = modal_frame.shape
    y_edges = np.linspace(0, h, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, w, grid_cols + 1, dtype=int)

    stability_mask = np.zeros((grid_rows, grid_cols), dtype=np.uint8)

    for row in range(grid_rows):
        for col in range(grid_cols):
            y1, y2 = y_edges[row], y_edges[row + 1]
            x1, x2 = x_edges[col], x_edges[col + 1]

            cell_modal = modal_frame[y1:y2, x1:x2]
            cell_control = control_frame[y1:y2, x1:x2]

            if cell_modal.size == 0 or cell_control.size == 0:
                continue

            diff = np.abs(np.mean(cell_modal) - np.mean(cell_control))
            if diff >= diff_threshold:
                stability_mask[row, col] = 1

    return stability_mask


def upscale_stability_mask_cv2(stability_mask, target_shape):
    """
    Upscale a low-res binary stability mask to full image resolution using cv2.

    Args:
        stability_mask (np.ndarray): Grid mask of shape (grid_rows, grid_cols)
        target_shape (Tuple[int, int]): Desired (H, W) size

    Returns:
        np.ndarray: Upscaled binary mask (H, W), values 0 or 1
    """
    H, W = target_shape

    # Resize using nearest neighbor to preserve binary mask
    upscaled = cv2.resize(
        stability_mask.astype(np.uint8),  # ensure uint8
        (W, H),  # cv2 uses (width, height)
        interpolation=cv2.INTER_NEAREST,
    )

    return upscaled


def compute_overlap_percentages(
    prediction_results,
    stability_mask,
    target_shape,
    overlap_threshold,
):
    upscaled_stability_mask = upscale_stability_mask_cv2(stability_mask, target_shape)

    H, W = upscaled_stability_mask.shape
    results = []

    for det in prediction_results:
        x1, y1, x2, y2 = map(int, det["original_bbox"])
        x1 = np.clip(x1, 0, W - 1)
        x2 = np.clip(x2, 0, W - 1)
        y1 = np.clip(y1, 0, H - 1)
        y2 = np.clip(y2, 0, H - 1)

        region = upscaled_stability_mask[y1:y2, x1:x2]
        total_pixels = region.size
        stable_pixels = np.sum(region == 1)

        overlap_pct = (stable_pixels / total_pixels * 100) if total_pixels > 0 else 0.0
        if overlap_pct >= overlap_threshold:
            results.append(det)

    return results


def debug_save_image(
    highlighted_image_np: np.ndarray,
    unattended_object_detection: list,
    stability_mask: np.ndarray,
    save_path: str = "output/overlapped_detections.jpg",
):
    """
    Draws detection bounding boxes and overlays a stability mask on the image.

    Args:
        highlighted_image_np (np.ndarray): Highlighted image as a NumPy array (H, W, 4).
        unattended_object_detection (list): List of dicts with key 'original_bbox' as [x1, y1, x2, y2].
        stability_mask (np.ndarray): 2D mask (grid_rows x grid_cols) where stable regions are marked.
        save_path (str): File path to save the output image.
        display (bool): Whether to display the image using matplotlib.
    """
    # Resize image to 1920x1080
    print("unattended object debug save image")
    image_resized = cv2.resize(
        highlighted_image_np, (1920, 1080), interpolation=cv2.INTER_NEAREST
    )
    image_bgr = cv2.cvtColor(image_resized, cv2.COLOR_RGBA2BGR)

    h, w = image_bgr.shape[:2]
    grid_rows, grid_cols = stability_mask.shape

    # Use np.linspace instead of floor division
    y_edges = np.linspace(0, h, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, w, grid_cols + 1, dtype=int)

    # Overlay stability mask as yellow
    overlay = image_bgr.copy()
    for row in range(grid_rows):
        for col in range(grid_cols):
            if stability_mask[row, col]:  # assume 0 = unstable, 1 or non-zero = stable
                y1, y2 = y_edges[row], y_edges[row + 1]
                x1, x2 = x_edges[col], x_edges[col + 1]
                cv2.rectangle(
                    overlay, (x1, y1), (x2, y2), color=(0, 255, 255), thickness=-1
                )  # yellow

    # Blend the overlay
    alpha = 0.3
    image_bgr = cv2.addWeighted(overlay, alpha, image_bgr, 1 - alpha, 0)

    # Draw red bounding boxes
    for detection in unattended_object_detection:
        x1, y1, x2, y2 = map(int, detection["original_bbox"])
        cv2.rectangle(image_bgr, (x1, y1), (x2, y2), color=(0, 0, 255), thickness=2)

    # Save the final image
    cv2.imwrite(save_path, image_bgr)
