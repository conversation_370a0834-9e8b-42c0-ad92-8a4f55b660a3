#!/usr/bin/env python3
"""
Transient-Only Masking for Efficient SAM/Grounded-SAM Processing

This module implements a lightweight CPU-only delta filter that:
1. Builds a long-memory background model updated slowly
2. Compares each new frame to the background to identify transient pixels
3. Filters out small islands of change below a pixel-area threshold
4. Masks out static regions (with a solid color like blue) so only dynamic pixels remain
5. Outputs masked frames, transient masks, and bounding boxes for SAM/Grounded-SAM processing
"""

import cv2
import numpy as np
from pathlib import Path
import argparse
import matplotlib.pyplot as plt
from tqdm import tqdm
import glob
from typing import List, Tuple, Dict, Any


class TransientMaskingEngine:
    def __init__(
        self,
        alpha=0.001,          # Background model update rate
        diff_threshold=100,    # L1 difference threshold to trigger movement
        downscale_factor=4,   # Downscale factor for faster diff computation
        blue_mask_color=(255, 0, 0),  # BGR color for masking static regions
        blur_size=5,          # Size of blur kernel for smoothing
        min_island_area=2000,   # Minimum pixel area for valid transient regions
        island_bleed_percent=10,  # Percentage to expand detected islands by (0-100)
        verbose=False          # Whether to print verbose output
    ):
        """
        Initialize the transient masking engine.
        """
        self.alpha = alpha
        self.diff_threshold = diff_threshold
        self.downscale_factor = downscale_factor
        self.blue_mask_color = blue_mask_color
        self.blur_size = blur_size
        self.min_island_area = min_island_area
        self.island_bleed_percent = island_bleed_percent
        self.verbose = verbose

        # Background model
        self.background_model = None
        self.is_initialized = False

        if self.verbose:
            print(f"TransientMaskingEngine initialized with parameters:")
            print(f" - Alpha: {self.alpha}")
            print(f" - Diff Threshold: {self.diff_threshold}")
            print(f" - Downscale Factor: 1/{self.downscale_factor}")
            print(f" - Mask Color (BGR): {self.blue_mask_color}")
            print(f" - Min Island Area: {self.min_island_area} pixels")
            print(f" - Island Bleed: {self.island_bleed_percent}%")

    def reset_background(self):
        """Reset the background model"""
        self.background_model = None
        self.is_initialized = False
    
    def initialize_background(self, frames):
        """
        Initialize background model from a set of frames.
        """
        if not frames:
            raise ValueError("No frames provided for background initialization")

        if self.verbose:
            print(f"Initializing background model with {len(frames)} frames...")
        self.background_model = frames[0].astype(np.float32)

        for frame in frames[1:]:
            self.update_background(frame)

        self.is_initialized = True
        if self.verbose:
            print("Background model initialized")
        return self.background_model.astype(np.uint8)

    def update_background(self, frame):
        """
        Update background model with a new frame.
        """
        if not self.is_initialized:
            self.background_model = frame.astype(np.float32)
            self.is_initialized = True
        else:
            self.background_model = (
                (1 - self.alpha) * self.background_model +
                self.alpha * frame.astype(np.float32)
            )

    def compute_transient_mask(self, frame):
        """
        Compute a binary mask of transient pixels.
        Ignores differences up to 10% of pixel values to reduce noise.
        """
        if not self.is_initialized:
            raise RuntimeError("Background model not initialized")

        h, w = frame.shape[:2]
        small_frame = cv2.resize(
            frame, (w // self.downscale_factor, h // self.downscale_factor)
        )
        small_bg = cv2.resize(
            self.background_model.astype(np.uint8),
            (w // self.downscale_factor, h // self.downscale_factor)
        )

        # Calculate absolute difference
        diff = cv2.absdiff(small_frame, small_bg)
        
        # Calculate 10% threshold for each pixel
        # For each pixel, the threshold is 10% of the maximum value between current and background
        max_values = np.maximum(small_frame, small_bg)
        percent_threshold = max_values * 0.10  # 10% threshold
        
        # Only consider differences that are both above the L1 threshold and above 10% of the pixel value
        diff_l1 = np.sum(diff, axis=2) if diff.ndim == 3 else diff
        percent_diff = np.sum(diff > percent_threshold, axis=2) if diff.ndim == 3 else (diff > percent_threshold)
        
        # Combine both conditions: must be above L1 threshold AND above 10% difference
        mask_small = ((diff_l1 > self.diff_threshold) & (percent_diff > 0)).astype(np.uint8) * 255

        kernel = np.ones((3, 3), np.uint8)
        mask_small = cv2.morphologyEx(mask_small, cv2.MORPH_OPEN, kernel)
        mask_small = cv2.morphologyEx(mask_small, cv2.MORPH_CLOSE, kernel)
        mask_small = cv2.GaussianBlur(mask_small, (self.blur_size,) * 2, 0)

        mask = cv2.resize(mask_small, (w, h), interpolation=cv2.INTER_NEAREST)
        mask = (mask > 127).astype(np.uint8) * 255
        return mask
    
    def filter_islands(self, mask):
        """
        Filter connected components (islands) by area and extract bounding boxes.
        Uses a more aggressive approach to create focused clusters of pixel changes.
        """
        h, w = mask.shape[:2]
        
        # First pass: Find connected components
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)
        
        # Second pass: Filter and merge nearby components
        final_mask = np.zeros_like(mask)
        bounding_boxes = []
        
        # Calculate average component size for reference
        valid_areas = [stats[i, cv2.CC_STAT_AREA] for i in range(1, num_labels) if stats[i, cv2.CC_STAT_AREA] >= self.min_island_area]
        if not valid_areas:
            return final_mask, bounding_boxes
            
        avg_area = np.mean(valid_areas)
        max_distance = int(np.sqrt(avg_area) * 0.5)  # Maximum distance to merge components
        
        # Group nearby components
        groups = []
        used_labels = set()
        
        for label in range(1, num_labels):
            if label in used_labels:
                continue
                
            area = stats[label, cv2.CC_STAT_AREA]
            if area < self.min_island_area:
                continue
                
            # Start a new group
            current_group = {label}
            used_labels.add(label)
            
            # Find nearby components
            center_x = stats[label, cv2.CC_STAT_LEFT] + stats[label, cv2.CC_STAT_WIDTH] // 2
            center_y = stats[label, cv2.CC_STAT_TOP] + stats[label, cv2.CC_STAT_HEIGHT] // 2
            
            for other_label in range(1, num_labels):
                if other_label in used_labels:
                    continue
                    
                other_area = stats[other_label, cv2.CC_STAT_AREA]
                if other_area < self.min_island_area:
                    continue
                
                other_center_x = stats[other_label, cv2.CC_STAT_LEFT] + stats[other_label, cv2.CC_STAT_WIDTH] // 2
                other_center_y = stats[other_label, cv2.CC_STAT_TOP] + stats[other_label, cv2.CC_STAT_HEIGHT] // 2
                
                distance = np.sqrt((center_x - other_center_x)**2 + (center_y - other_center_y)**2)
                
                if distance <= max_distance:
                    current_group.add(other_label)
                    used_labels.add(other_label)
            
            groups.append(current_group)
        
        # Process each group
        for group in groups:
            # Create a mask for this group
            group_mask = np.zeros_like(mask)
            for label in group:
                group_mask[labels == label] = 255
            
            # Find the tightest bounding box for the group
            y_indices, x_indices = np.where(group_mask > 0)
            if len(y_indices) > 0 and len(x_indices) > 0:
                x_min = np.min(x_indices)
                x_max = np.max(x_indices)
                y_min = np.min(y_indices)
                y_max = np.max(y_indices)
                
                # Apply minimal bleed for context
                if self.island_bleed_percent > 0:
                    bleed_x = int((x_max - x_min) * self.island_bleed_percent / 100)
                    bleed_y = int((y_max - y_min) * self.island_bleed_percent / 100)
                    
                    # Use smaller bleed for larger regions
                    if (x_max - x_min) * (y_max - y_min) > avg_area * 2:
                        bleed_x = int(bleed_x * 0.5)
                        bleed_y = int(bleed_y * 0.5)
                    
                    x_min = max(0, x_min - bleed_x)
                    y_min = max(0, y_min - bleed_y)
                    x_max = min(w - 1, x_max + bleed_x)
                    y_max = min(h - 1, y_max + bleed_y)
                
                # Add to final mask
                final_mask[y_min:y_max+1, x_min:x_max+1] = cv2.bitwise_or(
                    final_mask[y_min:y_max+1, x_min:x_max+1],
                    group_mask[y_min:y_max+1, x_min:x_max+1]
                )
                
                # Store the box
                bounding_boxes.append((x_min, y_min, x_max, y_max))
        
        return final_mask, bounding_boxes

    def apply_mask(self, frame, mask):
        """
        Apply the transient mask to the frame, replacing static regions with blue.
        """
        blue_bg = np.full_like(frame, self.blue_mask_color)
        masked_frame = np.where(mask[:, :, None] > 0, frame, blue_bg)
        return masked_frame

    def process_frame(self, frame, update_bg=True, padding_percent=3.0):
        """
        Process a frame: compute transient mask, filter islands, and apply it.
        Returns masked_frame, initial_mask, final_mask, bounding_boxes
        """
        initial_mask = self.compute_transient_mask(frame)
        final_mask, bounding_boxes = self.filter_islands(initial_mask)
        masked_frame = self.apply_mask(frame, final_mask)

        if update_bg:
            self.update_background(frame)

        # Crop and stitch the images with the final mask
        cropped_image, coordinate_map = crop_and_stitch_images(bounding_boxes, frame, final_mask, padding_percent)

        return masked_frame, initial_mask, final_mask, bounding_boxes, cropped_image, coordinate_map
    

def visualize_results(original, background, initial_mask, final_mask, masked_frame, boxes=None, min_island_area=500):
    """
    Visualize the results of transient masking, including total pixels and relative box coordinates.
    
    Args:
        original: Original input frame
        background: Current background model
        initial_mask: Initial binary mask before island filtering
        final_mask: Final binary mask after island filtering
        masked_frame: Masked frame with static regions replaced
        boxes: List of bounding boxes for detected transient regions
        min_island_area: Minimum area used for island filtering
    """
    # Get the dimensions of the original frame
    h, w, _ = original.shape
    total_pixels = h * w
    print(f"Original Frame Dimensions: {w}x{h}, Total Pixels: {total_pixels}")
    print("-" * 30)

    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 3, 1)
    plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB)); plt.title('Original Frame'); plt.axis('off')
    
    plt.subplot(2, 3, 2)
    plt.imshow(cv2.cvtColor(background.astype(np.uint8), cv2.COLOR_BGR2RGB)); plt.title('Background Model'); plt.axis('off')
    
    plt.subplot(2, 3, 3)
    plt.imshow(initial_mask, cmap='gray'); plt.title('Initial Transient Mask (Before Filtering)'); plt.axis('off')
    
    plt.subplot(2, 3, 4)
    plt.imshow(final_mask, cmap='gray'); plt.title(f'Final Mask (After {min_island_area}px Filtering)'); plt.axis('off')
    
    plt.subplot(2, 3, 5)
    disp = masked_frame.copy()
    if boxes:
        print(f"--- Masked Frame + {len(boxes)} Boxes ---")
        for i, (x1, y1, x2, y2) in enumerate(boxes):
            # Calculate relative coordinates
            rel_x1 = x1 / w
            rel_y1 = y1 / h
            rel_x2 = x2 / w
            rel_y2 = y2 / h
            
            # Print absolute and relative coordinates for each box
            print(f"Box {i+1}:")
            print(f"  Absolute Coords (x1, y1, x2, y2): ({x1}, {y1}, {x2}, {y2})")
            print(f"  Relative Coords (x1, y1, x2, y2): ({rel_x1:.4f}, {rel_y1:.4f}, {rel_x2:.4f}, {rel_y2:.4f})")

            # Draw the rectangle on the display image
            cv2.rectangle(disp, (x1, y1), (x2, y2), (0, 255, 0), 2)
        print("-" * 30)

    plt.imshow(cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)); plt.title(f'Masked Frame + {len(boxes)} Boxes'); plt.axis('off')
    
    plt.subplot(2, 3, 6)
    # Create visual difference between initial and final mask
    diff_mask = np.zeros_like(initial_mask, dtype=np.uint8)
    diff_mask[initial_mask > 0] = 128  # Gray for initial mask
    diff_mask[final_mask > 0] = 255    # White for final mask
    plt.imshow(diff_mask, cmap='gray'); plt.title('Removed vs. Kept Regions'); plt.axis('off')
    
    plt.tight_layout()
    plt.show()


def process_frames(bg_folder, transient_folder, output_folder=None,
                alpha=0.001, diff_threshold=30, min_island_area=2500, 
                island_bleed_percent=5, debug=False):
    """
    Process background and transient frame folders to detect transient objects.
    
    Args:
        bg_folder: Path to folder containing background frames
        transient_folder: Path to folder containing transient frames to be masked
        output_folder: Path to save output masked frames and visualizations (only used when debug=True)
        alpha: Background update rate (lower = slower adaptation)
        diff_threshold: Pixel-wise difference threshold (lower = more sensitive)
        min_island_area: Minimum area for transient regions to be considered valid
        island_bleed_percent: Percentage to expand detected regions by (0-100)
        debug: Whether to print debug information, create output files, and visualize results
    """
    # Create output folder only if in debug mode
    output_dir = None
    if debug and output_folder:
        output_dir = Path(output_folder)
        output_dir.mkdir(exist_ok=True)
    
    # Get list of background image files
    bg_files = sorted(glob.glob(str(Path(bg_folder) / "*.jpg")))
    
    # Get list of transient image files
    transient_files = sorted(glob.glob(str(Path(transient_folder) / "*.jpg")))
    
    # Check if we have files
    if not bg_files or not transient_files:
        if debug:
            print(f"Error: No images found in one of the input folders")
            print(f"Background folder '{bg_folder}': {len(bg_files)} images")
            print(f"Transient folder '{transient_folder}': {len(transient_files)} images")
        return None
    
    if debug:
        print(f"Processing frames:")
        print(f" - Found {len(bg_files)} background frames for model training")
        print(f" - Processing {len(transient_files)} transient frames")
    
    # Initialize transient masking engine
    if debug:
        print(f"Initializing TransientMaskingEngine...")
    masking_engine = TransientMaskingEngine(
        alpha=alpha, 
        diff_threshold=diff_threshold,
        min_island_area=min_island_area,
        island_bleed_percent=island_bleed_percent,
        verbose=debug
    )
    
    if debug:
        print(f"Loading background frames from {bg_folder}...")
    bg_frames = []
    for file_path in tqdm(bg_files, desc="Loading background frames", disable=not debug):
        frame = cv2.imread(file_path)
        if frame is None and debug:
            print(f"Warning: Could not read image {file_path}")
            continue
        if frame is not None:
            bg_frames.append(frame)
    
    # Initialize background model
    background = masking_engine.initialize_background(bg_frames)
    
    # Save background model if in debug mode
    if debug and output_dir:
        bg_path = output_dir / "background_model.jpg"
        cv2.imwrite(str(bg_path), background)
        if debug:
            print(f"Background model saved to {bg_path}")
    
    if debug:
        print(f"Loading transient frames from {transient_folder}...")
    
    results = []
    for i, file_path in enumerate(tqdm(transient_files, desc="Processing transient frames", disable=not debug)):
        # Read frame
        frame = cv2.imread(file_path)
        if frame is None:
            print(f"Warning: Could not read image {file_path}")
            continue
            
        # Process frame
        masked_frame, initial_mask, final_mask, boxes, cropped_image, coordinate_map = masking_engine.process_frame(frame, update_bg=False)
        
        # Save results
        filename = Path(file_path).name
        base_name = Path(filename).stem
        
        # Store the results
        result = {
            "filename": Path(file_path).name,
            "masked_frame": masked_frame,
            "initial_mask": initial_mask,
            "final_mask": final_mask,
            "boxes": boxes,
            "cropped_image": cropped_image,
            "coordinate_map": coordinate_map
        }
        results.append(result)
        
        # Save outputs only if in debug mode with output directory
        if debug and output_dir:
            # Save masked frame and both masks
            masked_frame_path = output_dir / f"{base_name}_masked.jpg"
            initial_mask_path = output_dir / f"{base_name}_initial_mask.jpg"
            final_mask_path = output_dir / f"{base_name}_final_mask.jpg"
            
            cv2.imwrite(str(masked_frame_path), masked_frame)
            cv2.imwrite(str(initial_mask_path), initial_mask)
            cv2.imwrite(str(final_mask_path), final_mask)
            
            # Create a comparison mask showing the difference
            diff_mask = np.zeros_like(initial_mask, dtype=np.uint8)
            diff_mask[initial_mask > 0] = 128  # Gray for initial mask
            diff_mask[final_mask > 0] = 255    # White for final mask
            cv2.imwrite(str(output_dir / f"{base_name}_mask_comparison.jpg"), diff_mask)
            
            # Save bounding boxes
            if boxes:
                with open(output_dir / f"{base_name}_boxes.txt", 'w') as f:
                    for box in boxes:
                        f.write(f"{box[0]},{box[1]},{box[2]},{box[3]}\n")
                        
                # Create a visualization with boxes
                disp_frame = frame.copy()
                for (x1, y1, x2, y2) in boxes:
                    cv2.rectangle(disp_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.imwrite(str(output_dir / f"{base_name}_boxes.jpg"), disp_frame)
        
        # Visualize if in debug mode
        if debug and i == 0:  # Only visualize first transient frame
            visualize_results(frame, background, initial_mask, final_mask, masked_frame, boxes, min_island_area)
    
    # Print completion message if in debug mode
    if debug and output_dir:
        print(f"Processing complete! Results saved to {output_dir}")
        
    # Return the results regardless of debug mode
    return results


def process_frame_bytes(background_image_bytes, image_bytes,
                        alpha=0.001, diff_threshold=30, min_island_area=2500, 
                        island_bleed_percent=5, padding_percent=3.0, debug=False):
    """
    Process background and transient frame folders to detect transient objects.
    
    Args:
        background_image_bytes: Bytes of background image
        background_image: Background image
        image_bytes: Bytes of image to be masked
        output_folder: Path to save output masked frames and visualizations (only used when debug=True)
        alpha: Background update rate (lower = slower adaptation)
        diff_threshold: Pixel-wise difference threshold (lower = more sensitive)
        min_island_area: Minimum area for transient regions to be considered valid
        island_bleed_percent: Percentage to expand detected regions by (0-100)
        padding_percent: Percentage of padding to add around cropped regions
        debug: Whether to print debug information, create output files, and visualize results
    """        
    # Initialize transient masking engine
    masking_engine = TransientMaskingEngine(
        alpha=alpha, 
        diff_threshold=diff_threshold,
        min_island_area=min_island_area,
        island_bleed_percent=island_bleed_percent,
        verbose=debug
    )

    bg_frames = [background_image_bytes]
    
    # Initialize background model
    _ = masking_engine.initialize_background(bg_frames)
            
    # Process frame
    masked_frame, initial_mask, final_mask, boxes, cropped_image, coordinate_map = masking_engine.process_frame(
        image_bytes, 
        update_bg=False,
        padding_percent=padding_percent
    )
        
    # Store the results
    result = {
        "masked_frame": masked_frame,
        "initial_mask": initial_mask,
        "final_mask": final_mask,
        "boxes": boxes,
        "cropped_image": cropped_image,
        "coordinate_map": coordinate_map
    }
    return result

def crop_and_stitch_images(
    bounding_boxes: List[Tuple[int, int, int, int]],
    original_image: np.ndarray,
    final_mask: np.ndarray = None,  # Add final_mask parameter
    padding_percent: float = 3.0  # Add padding percentage parameter
) -> Tuple[np.ndarray, Dict[str, Dict[str, Tuple[int, int, int, int]]]]:
    """
    Crops regions from an image based on bounding boxes, stitches them side-by-side
    on a new zero-padded canvas, and returns the new image and a coordinate map.
    Blackens areas within the cropped regions where there is no change detected.
    Adds padding around the cropped regions for better context.

    Args:
        bounding_boxes (List[Tuple[int, int, int, int]]): A list of bounding boxes,
            where each box is a tuple of (x_min, y_min, x_max, y_max).
        original_image (np.ndarray): The source image to crop from, as a NumPy array.
        final_mask (np.ndarray): Binary mask indicating areas of change. If provided,
            areas without change will be blackened in the output.
        padding_percent (float): Percentage of padding to add around each cropped region.

    Returns:
        Tuple[np.ndarray, Dict[str, Dict[str, Tuple[int, int, int, int]]]]:
            A tuple containing:
            - combined_image (np.ndarray): The new image with cropped parts stitched
              together. It is padded with zeros (black).
            - coordinate_map (Dict): A dictionary mapping a box ID to its
              original and new temporary coordinates, with all values as standard
              Python integers.
    """
    coordinate_map = {}
    cropped_images = []
    h, w = original_image.shape[:2]

    for i, (x1, y1, x2, y2) in enumerate(bounding_boxes):
        box_key = f"box_{i+1}"
        
        # Calculate padding
        box_width = x2 - x1
        box_height = y2 - y1
        pad_x = int(box_width * padding_percent / 100)
        pad_y = int(box_height * padding_percent / 100)
        
        # Apply padding with boundary checks
        x1_padded = max(0, x1 - pad_x)
        y1_padded = max(0, y1 - pad_y)
        x2_padded = min(w - 1, x2 + pad_x)
        y2_padded = min(h - 1, y2 + pad_y)
        
        coordinate_map[box_key] = {
            "original_coordinates": tuple(map(int, (x1, y1, x2, y2))),
            "padded_coordinates": tuple(map(int, (x1_padded, y1_padded, x2_padded, y2_padded)))
        }
        
        # Crop the image with padding
        crop = original_image[y1_padded:y2_padded, x1_padded:x2_padded].copy()
        
        # If we have a mask, blacken non-changing areas
        if final_mask is not None:
            # Get the corresponding mask region
            mask_crop = final_mask[y1_padded:y2_padded, x1_padded:x2_padded]
            
            # Create a binary mask for the actual changed regions
            if crop.ndim == 3:  # Color image
                # Create a 3-channel mask
                mask_3channel = np.stack([mask_crop] * 3, axis=-1)
                
                # Apply mask to all channels
                crop = np.where(mask_3channel > 0, crop, 0)
                
                # Additional noise reduction: if a pixel's neighborhood has no changes,
                # blacken it even if it's within the padded region
                kernel_size = 5
                dilated_mask = cv2.dilate(mask_crop, np.ones((kernel_size, kernel_size), np.uint8))
                dilated_mask_3channel = np.stack([dilated_mask] * 3, axis=-1)
                crop = np.where(dilated_mask_3channel > 0, crop, 0)
            else:  # Grayscale image
                crop = np.where(mask_crop > 0, crop, 0)
                # Additional noise reduction for grayscale
                kernel_size = 5
                dilated_mask = cv2.dilate(mask_crop, np.ones((kernel_size, kernel_size), np.uint8))
                crop = np.where(dilated_mask > 0, crop, 0)
        
        if crop.size == 0:
            print(f"Warning: Bounding box {box_key} { (x1, y1, x2, y2)} resulted in an empty crop. Skipping.")
            del coordinate_map[box_key]
            continue
            
        cropped_images.append(crop)

    if not cropped_images:
        return np.array([]), {}

    total_width = sum(img.shape[1] for img in cropped_images)
    max_height = max(img.shape[0] for img in cropped_images)
    num_channels = original_image.shape[2] if original_image.ndim == 3 else 1

    if num_channels > 1:
        combined_image = np.zeros((max_height, total_width, num_channels), dtype=original_image.dtype)
    else:
        combined_image = np.zeros((max_height, total_width), dtype=original_image.dtype)

    current_x = 0
    for i, box_key in enumerate(sorted(coordinate_map.keys())):
        crop = cropped_images[i]
        h, w = crop.shape[:2]

        combined_image[0:h, current_x:current_x+w] = crop

        temporary_coords = tuple(map(int, (current_x, 0, current_x + w, h)))
        coordinate_map[box_key]["temporary_coordinates"] = temporary_coords

        current_x += w

    return combined_image, coordinate_map


def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(
        description="Transient-Only Masking for Efficient SAM/Grounded-SAM Processing"
    )
    
    parser.add_argument(
        "--bg-folder", 
        default="rtsp_screenshots/original_background",
        help="Folder containing background frames"
    )
    
    parser.add_argument(
        "--transient-folder", 
        default="rtsp_screenshots/transient",
        help="Folder containing transient frames to be masked"
    )
    
    parser.add_argument(
        "--output", 
        default="transient_masks_output",
        help="Output directory for masked frames and visualizations (only used with --debug)"
    )
    
    parser.add_argument(
        "--alpha", 
        type=float, 
        default=0.001,
        help="Background model update rate (lower = slower adaptation)"
    )
    
    parser.add_argument(
        "--threshold", 
        type=int, 
        default=30,
        help="Difference threshold for transient detection (lower = more sensitive)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode with file outputs, visualization and detailed logging"
    )
    
    parser.add_argument(
        "--min-area", 
        type=int, 
        default=1500,
        help="Minimum area for transient regions to be considered valid"
    )
    
    parser.add_argument(
        "--bleed", 
        type=int, 
        default=5,
        help="Percentage to expand detected regions by (0-100)"
    )
    
    args = parser.parse_args()
    
    # Process the frames
    results = process_frames(
        args.bg_folder,
        args.transient_folder,
        args.output if args.debug else None,
        alpha=args.alpha,
        diff_threshold=args.threshold,
        min_island_area=args.min_area,
        island_bleed_percent=args.bleed,
        debug=args.debug
    )
    
    # In non-debug mode, you might want to print a summary or return results
    if not args.debug and results:
        print(f"Processed {len(results)} frames successfully.")


if __name__ == "__main__":
    main()
