from typing import Dict, Any
from redis_connection import <PERSON><PERSON><PERSON>
from datetime import datetime
import logging
import numpy as np
import cv2


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_fps(
    redis_client: CameraCache,
    frame_bytes: bytes,
    frame_id: str,
    camera_id: str,
    cardinal_requirements: dict,
    timestamp: str,
    camera_layer_config_id: str,
    frame_time: str,
    preprocessing_time: str,
    prediction_time: str,
    **kwargs,
) -> Dict[str, Any]:

    try:
        logger.info(f"Attempting to decode frame bytes of length: {len(frame_bytes)}")
        nparr = np.frombuffer(frame_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if frame is None:
            logger.error("Failed to decode frame - cv2.imdecode returned None")
            raise ValueError("Failed to decode frame")
        logger.info(f"Successfully decoded frame with shape: {frame.shape}")
    except Exception as e:
        logger.error(f"Error decoding frame: {str(e)}")
        raise

    logger.info("Encoding final frame and caching to Redis")
    # Convert the frame to bytes for sending in the alert
    _, img_encoded = cv2.imencode(".jpg", frame)
    final_frame_bytes = img_encoded.tobytes()
    logger.info("Successfully encoded frame for alert")

    final_frame_key = redis_client.build_key(camera_id, timestamp, "final_result")
    final_frame_base64 = redis_client.to_jsonable_bytes(final_frame_bytes)
    metadata = {
        "final_result": final_frame_base64,
        "has_alerts": False,
        "processed_at": datetime.now().isoformat(),
    }
    redis_client.set(final_frame_key, metadata, ttl=300)
