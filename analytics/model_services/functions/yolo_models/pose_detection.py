"""
Pose detection utilities for tripwire detection.
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO

# Define keypoint indices for YOLOv8 pose
# Format: [nose, left_eye, right_eye, left_ear, right_ear, left_shoulder, right_shoulder,
#          left_elbow, right_elbow, left_wrist, right_wrist, left_hip, right_hip,
#          left_knee, right_knee, left_ankle, right_ankle]
KEYPOINT_INDICES = {
    "nose": 0,
    "left_eye": 1,
    "right_eye": 2,
    "left_ear": 3,
    "right_ear": 4,
    "left_shoulder": 5,
    "right_shoulder": 6,
    "left_elbow": 7,
    "right_elbow": 8,
    "left_wrist": 9,
    "right_wrist": 10,
    "left_hip": 11,
    "right_hip": 12,
    "left_knee": 13,
    "right_knee": 14,
    "left_ankle": 15,
    "right_ankle": 16
}

# Define pivotal body parts for scaling detection
PIVOTAL_PARTS = ["left_wrist", "right_wrist", "left_hand", "right_hand", 
                 "left_elbow", "right_elbow", "head", "nose"]


class PoseDetector:
    """
    Pose detection using YOLOv8 for tripwire detection.
    """
    
    def __init__(self, model, device, model_path=None, confidence=0.3):
        """
        Initialize the pose detector.
        
        Args:
            model_path (str, optional): Path to YOLOv8 pose model
            confidence (float): Confidence threshold for detections
            device (str, optional): Device to run inference on ('cpu', 'cuda', etc.)
        """
        self.confidence = confidence
        
        # Load YOLOv8 pose model
        if model:
            self.model = model
        elif model_path:
            self.model = YOLO(model_path)
        else:
            # Use default YOLOv8n-pose model
            self.model = YOLO('yolov8n-pose.pt')
        
        # Set device
        if device:
            self.device = device
        else:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        print(f"Pose detector initialized with device: {self.device}")
    
    def detect(self, frame):
        """
        Detect poses in a frame.
        
        Args:
            frame (numpy.ndarray): Input frame
            
        Returns:
            list: List of detected poses with keypoints
        """
        # Run inference
        results = self.model(frame, conf=self.confidence, device=self.device)
        
        # Process results
        poses = []
        for result in results:
            # Extract keypoints
            if hasattr(result, 'keypoints') and result.keypoints is not None:
                keypoints = result.keypoints.data.cpu().numpy()
                
                for kpts in keypoints:
                    # Each keypoint is [x, y, confidence]
                    pose_data = {
                        "keypoints": {},
                        "bbox": None,
                        "confidence": 0.0
                    }
                    
                    # Extract individual keypoints
                    valid_keypoints = 0
                    total_confidence = 0.0
                    
                    for name, idx in KEYPOINT_INDICES.items():
                        if idx < len(kpts):
                            x, y, conf = kpts[idx]
                            if conf > self.confidence:
                                pose_data["keypoints"][name] = {
                                    "x": int(x),
                                    "y": int(y),
                                    "confidence": float(conf)
                                }
                                valid_keypoints += 1
                                total_confidence += float(conf)
                    
                    # Calculate average confidence
                    if valid_keypoints > 0:
                        pose_data["confidence"] = total_confidence / valid_keypoints
                    
                    # Calculate bounding box from keypoints
                    if pose_data["keypoints"]:
                        x_coords = [kp["x"] for kp in pose_data["keypoints"].values()]
                        y_coords = [kp["y"] for kp in pose_data["keypoints"].values()]
                        
                        x_min, y_min = min(x_coords), min(y_coords)
                        x_max, y_max = max(x_coords), max(y_coords)
                        
                        # Add some padding
                        padding = 10
                        x_min = max(0, x_min - padding)
                        y_min = max(0, y_min - padding)
                        x_max += padding
                        y_max += padding
                        
                        pose_data["bbox"] = [x_min, y_min, x_max - x_min, y_max - y_min]
                    
                    # Add head position (midpoint between eyes or use nose)
                    if "nose" in pose_data["keypoints"]:
                        pose_data["keypoints"]["head"] = pose_data["keypoints"]["nose"]
                    elif "left_eye" in pose_data["keypoints"] and "right_eye" in pose_data["keypoints"]:
                        left_eye = pose_data["keypoints"]["left_eye"]
                        right_eye = pose_data["keypoints"]["right_eye"]
                        
                        head_x = (left_eye["x"] + right_eye["x"]) // 2
                        head_y = (left_eye["y"] + right_eye["y"]) // 2
                        head_conf = (left_eye["confidence"] + right_eye["confidence"]) / 2
                        
                        pose_data["keypoints"]["head"] = {
                            "x": head_x,
                            "y": head_y,
                            "confidence": head_conf
                        }
                    
                    poses.append(pose_data)
        
        return poses
    
    def get_pivotal_parts(self, pose):
        """
        Extract pivotal body parts for scaling detection.
        
        Args:
            pose (dict): Pose data with keypoints
            
        Returns:
            list: List of pivotal body parts with positions
        """
        pivotal_parts = []
        
        for part_name in PIVOTAL_PARTS:
            if part_name in pose["keypoints"]:
                part = pose["keypoints"][part_name]
                pivotal_parts.append({
                    "name": part_name,
                    "x": part["x"],
                    "y": part["y"],
                    "confidence": part["confidence"]
                })
        
        return pivotal_parts
    
    def visualize_pose(self, frame, pose):
        """
        Visualize pose on a frame.
        
        Args:
            frame (numpy.ndarray): Input frame
            pose (dict): Pose data with keypoints
            
        Returns:
            numpy.ndarray: Frame with pose visualization
        """
        viz_frame = frame.copy()
        
        # Draw keypoints
        for name, kp in pose["keypoints"].items():
            x, y = int(kp["x"]), int(kp["y"])
            
            # Use different colors for pivotal parts
            if name in PIVOTAL_PARTS:
                color = (0, 0, 255)  # Red for pivotal parts
                size = 5
            else:
                color = (255, 0, 0)  # Blue for other parts
                size = 3
            
            cv2.circle(viz_frame, (x, y), size, color, -1)
            cv2.putText(
                viz_frame,
                name,
                (x + 5, y - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                color,
                1,
                cv2.LINE_AA
            )
        
        # Draw bounding box if available
        if pose["bbox"]:
            x, y, w, h = pose["bbox"]
            cv2.rectangle(viz_frame, (int(x), int(y)), (int(x + w), int(y + h)), (0, 255, 0), 2)
        
        return viz_frame


def detect_poses_for_tripwire(frame, detector=None):
    """
    Detect poses in a frame for tripwire detection.
    
    Args:
        frame (numpy.ndarray): Input frame
        detector (PoseDetector, optional): Pose detector instance
        
    Returns:
        tuple: (poses, pivotal_parts)
    """
    # Initialize detector if not provided
    if detector is None:
        detector = PoseDetector()
    
    # Detect poses
    poses = detector.detect(frame)
    
    # Extract pivotal parts
    all_pivotal_parts = []
    for pose in poses:
        pivotal_parts = detector.get_pivotal_parts(pose)
        all_pivotal_parts.extend(pivotal_parts)
    
    return poses, all_pivotal_parts
