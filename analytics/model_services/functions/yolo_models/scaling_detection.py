import json
import datetime
import pytz
import cv2
import os
import numpy as np
import uuid
import time
import logging
import pandas as pd
from typing import Dict, List, Tuple
from collections import defaultdict, deque

from redis_connection import CameraCache
from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD

try:
    from functions.yolo_models.commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
    from functions.yolo_models.pose_detection import PoseDetector, detect_poses_for_tripwire
    from functions.yolo_models.tripwire_utils import TripwireDetector, detect_objects_for_tripwire
except ModuleNotFoundError:
    from commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
    from tripwire_utils import TripwireDetector, detect_objects_for_tripwire
    from pose_detection import PoseDetector, detect_poses_for_tripwire

MODEL_NAME = "yolov8s-pose"
DEVICE = check_cuda_availability()
# POSE_DETECTION_MODEL = load_yolo_model(MODEL_NAME, DEVICE) 

logger = logging.getLogger(__name__)

CONFIG = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
ROI_CONFIG = CONFIG["POSE_DETECTION"]["ROI_CONFIG"]
POSE_TRACK_PREFIX = "pose_track"  # prefix for all pose tracking keys

BUFFER_FRAC = CONFIG["POSE_DETECTION"]["BUFFER_FRAC"]     # 10 % expansion on each side
N_TRIPWIRES = CONFIG["POSE_DETECTION"]["N_TRIPWIRES"]                    # horizontal wires
CACHE_LEN   = CONFIG["POSE_DETECTION"]["CACHE_LEN"]
CONF_PERSON = CONFIG["POSE_DETECTION"]["CONF_PERSON"]
CACHE_TTL = CONFIG["POSE_DETECTION"]["CACHE_TTL"]  # 5 minutes cache TTL
IOU_THRESHOLD = CONFIG["POSE_DETECTION"]["IOU_THRESHOLD"]        # same re-use rule
TRACK_TTL_SEC = CONFIG["POSE_DETECTION"]["TRACK_TTL_SEC"]           # drop track if its LAST frame is older
MIN_INSIDE_FRAC = CONFIG["POSE_DETECTION"]["MIN_INSIDE_FRAC"]      # was 0.80
FOOT_ZONE_FRAC = CONFIG["POSE_DETECTION"]["FOOT_ZONE_FRAC"]      # bottom half instead of bottom third
REQUIRE_BOTH_FEET = CONFIG["POSE_DETECTION"]["REQUIRE_BOTH_FEET"]     # True = both ankles, False = at least one
CRITICAL_CRITERIA = CONFIG["POSE_DETECTION"]["CRITICAL_CRITERIA"]
WARNING_CRITERIA = CONFIG["POSE_DETECTION"]["WARNING_CRITERIA"]


def expand_roi(roi: Tuple[int, int, int, int], frac: float = BUFFER_FRAC) -> Tuple[int, int, int, int]:
    """Expand an ROI by `frac` on all four sides."""
    x1, y1, x2, y2 = roi
    w, h  = x2 - x1, y2 - y1
    dx, dy = int(w * frac), int(h * frac)
    return (x1 - dx, y1 - dy, x2 + dx, y2 + dy)

ROI_EXP = {cid: expand_roi(roi) for cid, roi in ROI_CONFIG.items()}


def map_camera_name_to_background_key(camera_name):
    """
    Map camera names to background image keys.
    
    Args:
        camera_name (str): Camera name (e.g., "Cam01", "Cam02")
        
    Returns:
        str: Background image key (e.g., "cam_one", "cam_two")
    """
    # Map camera names to background image keys
    camera_mapping = {
        "cam_one": "cam_one",
        "cam_two": "cam_two", 
        "cam_three": "cam_three",
        "cam_four": "cam_four",
        "cam_five": "cam_five",
        "cam_eight": "cam_eight",
        "cam_eight": "cam_eight",
        "video_one.mp4": "cam_one",
        "video_two.mp4": "cam_two", 
        "video_three.mp4": "cam_three",
        "video_four.mp4": "cam_four",
        "video_five.mp4": "cam_five",
        "video_eight.mp4": "cam_eight",
        "video_nine.mp4": "cam_nine"
    }
    
    return camera_mapping.get(camera_name, "cam_one")

def load_camera_background_image(camera_name):
    try:
        default_bg = cv2.imread(f"functions/va_camera_backgrounds/{camera_name}/default_bg.jpg")
        if default_bg is None:
            print(f"⚠️ Could not load background image for {camera_name}")
            return None
        default_bg = cv2.cvtColor(default_bg, cv2.COLOR_BGR2RGB)
        return default_bg
    except Exception as e:
        print(f"⚠️ Error loading background image for {camera_name}: {e}")
        return None

# Initialize background images with error handling
BACKGROUND_IMAGE = {}
background_cameras = ["cam_one", "cam_eight"]

for cam in background_cameras:
    bg_image = load_camera_background_image(cam)
    if bg_image is not None:
        BACKGROUND_IMAGE[cam] = bg_image
        print(f"✅ Loaded background image for {cam}")
    else:
        print(f"⚠️ Skipping {cam} due to failed background image loading")

print(f"📸 Successfully loaded {len(BACKGROUND_IMAGE)} background images")


def predict(model, img, conf=0.5, device=DEVICE):
    # Use the specified device for prediction
    results = model.predict(img, conf=conf, device=device)
    return results


def get_pose_position(frame_bytes, model, conf=0.5, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, **kwargs):
    """Key points:
    1. Nose
    2. Left Eye
    3. Right Eye
    4. Left Ear
    5. Right Ear
    6. Left Shoulder
    7. Right Shoulder
    8. Left Elbow
    9. Right Elbow
    10. Left Wrist
    11. Right Wrist
    12. Left Hip
    13. Right Hip
    14. Left Knee
    15. Right Knee
    16. Left Ankle
    17. Right Ankle"""
        
    # Convert bytes to image
    image_obj = bytes_to_image(frame_bytes)

    preprocessing_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    
    # Predict poses in the image
    results = predict(model, image_obj, conf)

    # Convert the results to a JSON-friendly format
    results = results[0].to_json()

    # Convert the results to a list
    results = json.loads(results)

    print(f"Results: {results}")

    # Initialize the detection results list
    detections = []

    # Map keypoint indices to names
    keypoint_names = [
        'Nose', 'Left Eye', 'Right Eye', 'Left Ear', 'Right Ear',
        'Left Shoulder', 'Right Shoulder', 'Left Elbow', 'Right Elbow',
        'Left Wrist', 'Right Wrist', 'Left Hip', 'Right Hip',
        'Left Knee', 'Right Knee', 'Left Ankle', 'Right Ankle'
    ]

    for result in results:
        # Get keypoint coordinates
        x_coords = result.get('keypoints', {}).get('x', [])
        y_coords = result.get('keypoints', {}).get('y', [])
        visible = result.get('keypoints', {}).get('visible', [])
        bbox = result.get('box', {})

        # Create mapped keypoints dictionary
        mapped_keypoints = {}
        x1, y1, x2, y2 = bbox.get('x1'), bbox.get('y1'), bbox.get('x2'), bbox.get('y2')
        for i, name in enumerate(keypoint_names):
            if i < len(x_coords) and i < len(y_coords):
                x = float(x_coords[i])
                y = float(y_coords[i])
                # Only include keypoint if it's inside the bounding box
                if x1 <= x <= x2 and y1 <= y <= y2:
                    mapped_keypoints[name] = {
                        'x': x,
                        'y': y,
                        'visible': bool(visible[i]) if i < len(visible) else True
                    }
        detection = {
            'object_name': result.get('name'),
            'confidence': result.get('confidence'),
            'coordinates': {
                'x1': float(x1),
                'y1': float(y1),
                'x2': float(x2),
                'y2': float(y2)
            },
            'keypoints': {
                'landmarks': mapped_keypoints
            },
            'style': {
                'rectangle_color': rectangle_color,
                'rectangle_thickness': rectangle_thickness,
                'text_thickness': text_thickness
            }
        }

        detections.append(detection)
    
    return {"preprocessing_time": preprocessing_time, "result": detections}

def crop(img, box):
    """
    Crop an image using a bounding box.
    
    Args:
        img (numpy.ndarray): Image to crop
        box (tuple): Bounding box (x1, y1, x2, y2)
        
    Returns:
        numpy.ndarray: Cropped image
    """
    x1, y1, x2, y2 = box
    return img[y1:y2, x1:x2]


def detect_motion(control, frames, alpha=0.02, down_ratio=0.2, volatility_thresh=10, diff_thresh=30):
    """
    Detect motion in a sequence of frames compared to a control image.
    
    Args:
        control (numpy.ndarray): Control/background image
        frames (list): List of frames to analyze
        alpha (float): Weight for frame accumulation
        down_ratio (float): Downsampling ratio for processing
        volatility_thresh (int): Threshold for volatility filtering
        diff_thresh (int): Threshold for difference detection
        
    Returns:
        tuple: (list of bounding boxes, binary mask, difference map)
    """
    H, W = control.shape[:2]
    # OpenCV resize expects (width, height), so we need to swap the order
    new_size = (int(W * down_ratio), int(H * down_ratio))
    
    print(f"🔍 Control shape: {control.shape}")
    print(f"🔍 Down ratio: {down_ratio}")
    print(f"🔍 Calculated new_size: {new_size}")
    print(f"🔍 W * down_ratio = {W} * {down_ratio} = {W * down_ratio}")
    print(f"🔍 H * down_ratio = {H} * {down_ratio} = {H * down_ratio}")

    # force uint8 & contiguous in case your frame comes in another format
    control_arr = np.ascontiguousarray(control, dtype=np.uint8)
    control_small = cv2.resize(control_arr, new_size, interpolation=cv2.INTER_LINEAR).astype(np.float32)

    heatmap = np.zeros_like(control_small)
    volatility = np.zeros((new_size[1], new_size[0]), dtype=np.uint8)

    for f in frames:
        print(f"🔍 Processing frame: {f.shape}")
        print(f"🔍 Resizing frame to (w,h): {new_size}")

        f_arr = np.ascontiguousarray(f, dtype=np.uint8)
        f_small = cv2.resize(f_arr, new_size, interpolation=cv2.INTER_LINEAR).astype(np.float32)

        delta = cv2.absdiff(f_small, control_small)
        delta_gray = cv2.cvtColor(delta.astype(np.uint8), cv2.COLOR_BGR2GRAY)
        volatility += cv2.threshold(delta_gray, 15, 1, cv2.THRESH_BINARY)[1]
        heatmap += f_small * alpha

    avg_img = (heatmap / len(frames)).astype(np.uint8)
    diff = cv2.absdiff(avg_img, cv2.resize(control, new_size))
    diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)

    volatility_mask = cv2.threshold(volatility, volatility_thresh, 255, cv2.THRESH_BINARY_INV)[1]
    diff_gray = cv2.bitwise_and(diff_gray, diff_gray, mask=volatility_mask)
    mask = cv2.threshold(diff_gray, diff_thresh, 255, cv2.THRESH_BINARY)[1]

    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, np.ones((3, 3), np.uint8))
    mask = cv2.dilate(mask, np.ones((5, 5), np.uint8), iterations=2)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    bboxes = []
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        if w * h >= 100:
            x *= int(1 / down_ratio)
            y *= int(1 / down_ratio)
            w *= int(1 / down_ratio)
            h *= int(1 / down_ratio)
            bboxes.append((x, y, w, h))

    return bboxes, mask, diff_gray


def cache_last_30_messages(
    redis_client: CameraCache,
    messages: list[dict],
    camera_name: str
):
    """
    Push each message onto a Redis list and keep only the 30 most recent.
    Images are stored separately using SET/GET for better performance.
    """
    list_key = f"events:layer_one:pose_detection:{camera_name}"
    pipe = redis_client._redis.pipeline()
    
    for msg in messages:
        try:
            # Validate message and image
            if msg is None or "image" not in msg or msg["image"] is None:
                print("⚠️ Skipping invalid message in cache")
                continue
            
            # Create a copy of the message to avoid modifying the original
            msg_copy = msg.copy()
            frame_key = msg_copy["image_key"]

            # Extract out the image from the message
            image = msg_copy["image"]
            image = image.tolist()

            # Store the image in Redis
            redis_client.set(frame_key, image, ttl=3600)
            
            # Delete the image from the message
            del msg_copy["image"]
                
            payload = json.dumps(msg_copy)
            pipe.lpush(list_key, payload)
        except Exception as e:
            print(f"⚠️ Error caching message: {e}")
            continue
    
    # Now trim the list so only the 30 newest remain (indices 0–29)
    pipe.ltrim(list_key, 0, 29)
    pipe.execute()


def get_last_30_messages(
    redis_client: CameraCache,
    camera_name: str
) -> list[dict]:
    """
    Retrieve up to 30 most recent messages (in newest→oldest order).
    Images are fetched separately using their stored keys.
    """
    list_key = f"events:layer_one:pose_detection:{camera_name}"
    raw = redis_client._redis.lrange(list_key, 0, -1)
    messages = []
    
    for item in raw:
        try:
            msg = json.loads(item)
            frame_key = msg["image_key"]
            image = redis_client.get(frame_key)
            image = np.array(image)

            msg["image"] = image

            messages.append(msg)

        except (json.JSONDecodeError, ValueError, TypeError) as e:
            print(f"⚠️ Error parsing message from cache: {e}")
            continue
        except Exception as e:
            print(f"⚠️ Unexpected error processing cached message: {e}")
            continue
    
    return messages


def detect_scaling_wall_violation(redis_client, model, device, camera_name, frame_bytes: bytes, frame_key, **kwargs):
    """
    Detects scaling wall violations using tripwire detection approach.
    Triggers when pivotal body parts move upward across horizontal tripwires.

    Args:
        redis_client (CameraCache): Redis client
        model (PoseDetector): Pose detector model
        device (str): Device to run inference on ('cpu', 'cuda', etc.)
        cam_name (str): Camera label, must be one of ['cam_01', 'cam_08']
        frame_bytes (bytes): Current frame in bytes
        frame_key (str): Current frame's key

    Returns:
        tuple: (list of detections, binary mask, visualization frame)
    """
    # # Guard: only handle these two cams
    # if cam_name not in ROI_CONFIG:
    #     print(f"⚠️ Unsupported camera: {cam_name}")
    #     return [], None, None
    timestamp = frame_key.split("_")[1]

    nparr = np.frombuffer(frame_bytes, np.uint8)
    image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    new_frame = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)

    # Initialize tripwire detector if not already done
    if not hasattr(detect_scaling_wall_violation, "tripwire_detector"):
        detect_scaling_wall_violation.tripwire_detector = TripwireDetector(
            num_wires=5,  # 5 horizontal tripwires
            min_detection_size=100,  # Minimum object size in pixels
            persistence_frames=3,  # Require 3 consecutive frames for detection
            cooldown_frames=15  # Wait 15 frames before allowing another detection
        )
        # Setup tripwires for each camera
        for cam_id in ROI_CONFIG:
            roi_height = ROI_CONFIG[cam_id][3] - ROI_CONFIG[cam_id][1]
            detect_scaling_wall_violation.tripwire_detector.setup_tripwires(cam_id, roi_height)
    
    # Initialize frame cache if not already done
    # if not hasattr(detect_scaling_wall_violation, "frame_cache"):
    #     detect_scaling_wall_violation.frame_cache = {k: [] for k in ROI_CONFIG}
        
    # Initialize pose detector if not already done
    if not hasattr(detect_scaling_wall_violation, "pose_detector"):
        try:
            # TODO Load the model
            detect_scaling_wall_violation.pose_detector = PoseDetector(model=model, device=device, confidence=0.25)
            detect_scaling_wall_violation.use_pose_detection = True
            print("✅ YOLOv8 pose detector initialized successfully")
        except Exception as e:
            print(f"⚠️ Failed to initialize pose detector: {e}")
            print("⚠️ Falling back to general object detection")
            detect_scaling_wall_violation.use_pose_detection = False

    # Get ROI box and crop frames
    roi_box = ROI_CONFIG[map_camera_name_to_background_key(camera_name)]
    control_img = BACKGROUND_IMAGE[map_camera_name_to_background_key(camera_name)]
    cropped_control = crop(control_img, roi_box)
    cropped_frame = crop(new_frame, roi_box)

    # Add to cache
    message_to_cache = {
        "image": cropped_frame.copy(),
        "timestamp": timestamp,
        "image_key": f"{frame_key}_pose_detection"
    }
    
    # Debug: Check if image is valid before caching
    if message_to_cache["image"] is None:
        print(f"⚠️ cropped_frame is None before caching for camera {camera_name}")
    elif not isinstance(message_to_cache["image"], np.ndarray):
        print(f"⚠️ cropped_frame is not numpy array: {type(message_to_cache['image'])} for camera {camera_name}")
    else:
        print(f"✅ Caching frame with shape {message_to_cache['image'].shape} for camera {camera_name}")
    
    cache_last_30_messages(redis_client, [message_to_cache], camera_name)
    
    # Get the last 30 messages
    cache = get_last_30_messages(redis_client, camera_name)
    
    # Debug: Check cache contents
    print(f"📊 Retrieved {len(cache)} messages from cache for camera {camera_name}")
    for i, msg in enumerate(cache[:3]):  # Check first 3 messages
        if msg.get("image") is None:
            print(f"⚠️ Message {i} has None image in cache")
        elif isinstance(msg.get("image"), np.ndarray):
            print(f"✅ Message {i} has valid image with shape {msg['image'].shape}")
        else:
            print(f"⚠️ Message {i} has invalid image type: {type(msg.get('image'))}")

    # Not enough frames
    if len(cache) < 2:
        return [], None, None
        
    # Detect objects for tripwire tracking
    objects = []
    pivotal_parts = []
    
    # Try pose detection first if available
    if hasattr(detect_scaling_wall_violation, "use_pose_detection") and detect_scaling_wall_violation.use_pose_detection:
        try:
            # Detect poses and extract pivotal body parts
            poses, parts = detect_poses_for_tripwire(cropped_frame, detect_scaling_wall_violation.pose_detector)
            
            # Convert pivotal parts to objects for tripwire detection
            for part in parts:
                # Create a small bounding box around the body part
                x, y = part["x"], part["y"]
                w, h = 20, 20  # Small box around the keypoint
                objects.append((x - w//2, y - h//2, w, h))
                
            # Store pivotal parts for visualization
            pivotal_parts = parts
            
            # If no poses detected, fall back to general object detection
            if not objects:
                objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
        except Exception as e:
            print(f"⚠️ Error in pose detection: {e}")
            # Fall back to general object detection
            objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
    else:
        # Use general object detection
        objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
    
    # Track objects and detect tripwire crossings
    detections = detect_scaling_wall_violation.tripwire_detector.track_objects(camera_name, objects, timestamp)
    
    # Update timestamps for all detections
    for detection in detections:
        detection["timestamp"] = timestamp
    
    # Convert objects to detection format for visualization
    object_detections = []
    
    # Add pivotal body parts to visualization if available
    if pivotal_parts:
        for part in pivotal_parts:
            x, y = part["x"], part["y"]
            w, h = 20, 20  # Small box around the keypoint
            object_detections.append({
                "bbox": [x - w//2, y - h//2, w, h],
                "original_bbox": [x - w//2, y - h//2, x + w//2, y + h//2],
                "category": part["name"],
                "confidence": part["confidence"]
            })
    else:
        # Use general objects
        for obj in objects:
            x, y, w, h = obj
            object_detections.append({
                "bbox": [x, y, x+w, y+h],
                "original_bbox": [x, y, x+w, y+h],
                "category": "object",
                "confidence": 1.0
            })
    
    # Save detection visualization for debugging
    if detections:
        # Add detection method to detections
        for detection in detections:
            if pivotal_parts:
                detection["detection_method"] = "pose"
                # Add the specific body part that triggered the detection if available
                if "category" in detection and detection["category"] == "scaling_gantry":
                    detection["body_part"] = pivotal_parts[0]["name"] if pivotal_parts else "unknown"
            else:
                detection["detection_method"] = "motion"
    
    # Use motion detection as a fallback for compatibility
    if not detections and len(cache) >= 5:
        # Sample frames for motion detection with validation
        sampled = []
        for i, f in enumerate(cache):
            if i % max(1, len(cache) // 30) == 0:
                # Validate frame before adding to sampled list
                if (f is not None and 
                    "image" in f and 
                    f["image"] is not None and 
                    isinstance(f["image"], np.ndarray) and 
                    f["image"].size > 0):
                    sampled.append(f["image"])
        
        # Only proceed if we have valid frames
        if sampled:
            # Use existing motion detection as fallback
            bboxes, mask, heatmap = detect_motion(cropped_control, sampled)
            
            # Create detections from motion detection
            for x, y, w, h in bboxes:
                detections.append({
                    "event_id": str(uuid.uuid4()),
                    "timestamp": timestamp,
                    "cam_id": camera_name,
                    "category": "scaling_gantry",
                    "bbox": [x, y, x + w, y + h],
                    "original_bbox": [x, y, x + w, y + h],
                    "ground_x": 0.0,
                    "ground_y": 0.0,
                    "confidence": 0.85,  # Lower confidence for fallback method
                    "is_primary": True,
                    "detection_method": "motion"  # Mark as motion-based detection
                })
            
            return {
                "cardinal_requirements": "scaling_gantry",
                "final_results": detections
            }
    
    # Create a simple mask for visualization purposes
    if cropped_frame is not None:
        mask = np.zeros((cropped_frame.shape[0], cropped_frame.shape[1]), dtype=np.uint8)
        for obj in objects:
            x, y, w, h = obj
            cv2.rectangle(mask, (x, y), (x+w, y+h), 255, -1)
    else:
        mask = None
    
    return {
        "cardinal_requirements": "scaling_gantry",
        "final_results": detections
    }



# ----------------- This is the V3 Scaling Gantry Detection -----------------
# ------------------------------------------------------------------
# HELPERS
# ------------------------------------------------------------------
def intersection_area(a: Tuple[int, int, int, int], b: Tuple[int, int, int, int]) -> int:
    """Return intersection area of two boxes; 0 if no overlap."""
    ax1, ay1, ax2, ay2 = a
    bx1, by1, bx2, by2 = b
    iw = max(0, min(ax2, bx2) - max(ax1, bx1))
    ih = max(0, min(ay2, by2) - max(ay1, by1))
    return iw * ih

def overlap_ratio(box: Tuple[int, int, int, int], roi: Tuple[int, int, int, int]) -> float:
    """Intersection area divided by the *box* area."""
    inter = intersection_area(box, roi)
    box_area = (box[2] - box[0]) * (box[3] - box[1])
    return 0.0 if box_area == 0 else inter / box_area

def run_pose(crop_rgb, model):
    r = model(crop_rgb, imgsz=256, conf=CONF_PERSON, verbose=False)[0]
    return [{"bbox": b.cpu().numpy(), "kpts": k.cpu().numpy()}
            for k, b in zip(r.keypoints.xy, r.boxes.xyxy)]

# ------------ light IoU tracker -----------------------------
def bbox_iou(a,b):
    ax1,ay1,ax2,ay2=a; bx1,by1,bx2,by2=b
    iw,ih=max(0,min(ax2,bx2)-max(ax1,bx1)),max(0,min(ay2,by2)-max(ay1,by1))
    return 0 if iw==0 or ih==0 else (iw*ih)/((ax2-ax1)*(ay2-ay1)+(bx2-bx1)*(by2-by1)-iw*ih)

def get_track_key(camera_id: str, person_id: int) -> str:
    """Generate Redis key for storing person track data"""
    return f"{POSE_TRACK_PREFIX}:{camera_id}:{person_id}"

# ── helper ──────────────────────────────────────────────────────────────
def _rows_to_tracks(df: pd.DataFrame) -> Dict[int, deque]:
    """
    Convert the DataFrame back into {pid: deque}
    (assumes df already sorted by ts ascending)
    """
    tracks = {}
    for pid, grp in df.groupby("pid", sort=False):
        dq = deque(maxlen=CACHE_LEN)
        for _, row in grp.tail(CACHE_LEN).iterrows():
            dq.append({
                "ts"  : row.ts,
                "bbox": np.asarray(row.bbox, dtype=np.float32),
                "kpts": np.asarray(row.kpts, dtype=np.float32),
            })
        tracks[pid] = dq
    return tracks


def calculate_box_area(box):
    """Calculate the area of a bounding box."""
    width = box[2] - box[0]
    height = box[3] - box[1]
    return width * height


def calculate_intersection_area(box1, box2):
    """Calculate the intersection area between two boxes."""
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    if x2 <= x1 or y2 <= y1:
        return 0
    
    return (x2 - x1) * (y2 - y1)


def remove_nested_boxes(df):
    """Remove smaller boxes that overlap significantly with bigger boxes."""
    if df.empty:
        return df
        
    # Process each category separately
    result_dfs = []
    for category in df['category'].unique():
        category_df = df[df['category'] == category].copy()
        if len(category_df) <= 1:
            result_dfs.append(category_df)
            continue
            
        # Get all boxes and their areas
        bboxes = category_df['bbox'].tolist()
        areas = [calculate_box_area(box) for box in bboxes]
        keep = [True] * len(bboxes)
        
        # Compare each box with others
        for i in range(len(bboxes)):
            if not keep[i]:
                continue
                
            box1 = bboxes[i]
            area1 = areas[i]
            
            for j in range(len(bboxes)):
                if i == j or not keep[j]:
                    continue
                    
                box2 = bboxes[j]
                area2 = areas[j]
                
                # Only compare if box1 is smaller than box2
                if area1 < area2:
                    intersection = calculate_intersection_area(box1, box2)
                    overlap_ratio = intersection / area1  # Use smaller box area as reference
                    
                    # Debug print
                    print(f"\nComparing boxes:")
                    print(f"Smaller box {i}: {box1}, Area: {area1}")
                    print(f"Bigger box {j}: {box2}, Area: {area2}")
                    print(f"Intersection area: {intersection}")
                    print(f"Overlap ratio with smaller box: {overlap_ratio:.2f}")
                    
                    # If smaller box overlaps more than 50% with bigger box, remove it
                    if overlap_ratio > 0.3:
                        keep[i] = False
                        print(f"Removing smaller box {i} as it overlaps {overlap_ratio:.2%} with bigger box {j}")
                        break
        
        # Keep only the boxes that weren't marked for removal
        keep_indices = [idx for idx, k in zip(category_df.index, keep) if k]
        result_dfs.append(category_df.loc[keep_indices])
    
    # Combine all categories
    return pd.concat(result_dfs, ignore_index=True)


def update_cache(
    redis_client: "CameraCache",
    camera_id: str,
    dets: List[dict]
) -> Dict[int, deque]:
    print(f"Updating cache for camera: {camera_id}")

    # Create the directory if it does not exist
    output_dir = f'/frame/scaling_gantry/{camera_id}'
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'scaling_gantry_cache.json')

    try:
        if os.path.exists(output_path):
            with open(output_path, 'r') as f:
                rows = json.load(f)
        else:
            rows = []

    except Exception as e:
        print(f"Error updating cache: {e}")
        rows = []

    print(f"Cache frames: {len(rows)}")

    # active_key = f"{POSE_TRACK_PREFIX}:{camera_id}:active-new"
    
    now        = time.time()

    # ───────────────────── 1. load + JSON → DataFrame ───────────────────
    # raw = redis_client._redis.get(active_key)
    # if raw:
    #     rows = json.loads(raw)                         # list[dict]
    # else:
    #     rows = []

    if rows:
        df = pd.DataFrame(rows)
    else:
        df = pd.DataFrame(columns=["pid", "ts", "bbox", "kpts"])

    # keep only live rows
    df = df[df["ts"] >= now - TRACK_TTL_SEC].copy()

    # ensure correct dtypes
    df["pid"] = df["pid"].astype(int,   errors="ignore")
    # bbox / kpts stay as lists (converted to np later)

    print("load df", time.time() - now)
    now        = time.time()

    # ───────────────────── 2. build current tracks map ───────────────────
    existing_tracks = _rows_to_tracks(df.sort_values(["pid", "ts"]))

    # for assigning new pids
    next_pid = max(existing_tracks.keys(), default=-1) + 1

    print("build current tracks map", time.time() - now)
    now        = time.time()

    # ───────────────────── 3. IoU-based assignment ──────────────────────
    new_rows = []
    for det in dets:
        det_bbox = np.asarray(det["bbox"], dtype=np.float32)
        det_kpts = np.asarray(det["kpts"], dtype=np.float32)

        best_iou, best_pid = 0.0, None
        for pid, track in existing_tracks.items():
            if not track:
                continue
            iou = bbox_iou(det_bbox, track[-1]["bbox"])
            if iou > best_iou:
                best_iou, best_pid = iou, pid

        if best_iou < IOU_THRESHOLD:
            best_pid, next_pid = next_pid, next_pid + 1

        new_rows.append({
            "pid" : best_pid,
            "ts"  : now,
            "bbox": det_bbox.tolist(),     # JSON-serialisable
            "kpts": det_kpts.tolist(),
        })

    print("IoU-based assignment", time.time() - now)
    now = time.time()

    # ───────────────────── 4. merge + cap history ───────────────────────
    if new_rows:
        df_new = pd.DataFrame(new_rows)
        df = pd.concat([df, df_new], ignore_index=True)

    # keep only last CACHE_LEN per pid
    df.sort_values(["pid", "ts"], inplace=True)
    df["rn"] = df.groupby("pid").cumcount(ascending=False)  # 0 = newest
    df = df[df["rn"] < CACHE_LEN].drop(columns="rn")

    print("merge + cap history", time.time() - now)
    now = time.time()

    # ───────────────────── 5. write back to Redis ───────────────────────
    # redis_client._redis.set(
    #     active_key,
    #     json.dumps(df.to_dict("records")),
    #     ex=CACHE_TTL
    # )

    # Write to the /dev/shm/frame/scaling_gantry/{camera_id}/scaling_gantry_cache.json
    with open(output_path, 'w') as f:
        json.dump(df.to_dict("records"), f)

    print("write back to cache", time.time() - now)
    now = time.time()

    # ───────────────────── 6. build return structure ────────────────────
    updated_tracks = _rows_to_tracks(df)

    print("build return structure", time.time() - now)
    now = time.time()

    return updated_tracks

# ------------- ladder-rung classifier -----------------------
def rung_index(y, roi_y1, roi_y2, n=N_TRIPWIRES):
    """0 = bottom segment, n = top segment"""
    step = (roi_y2 - roi_y1) / (n + 1)
    return int(np.clip((y - roi_y1) // step, 0, n))

def status_from_rungs(track, roi_y1, roi_y2):
    if len(track) < 2: return 'green'
    hip_first = np.mean(track[0]["kpts"][[11,12],1])
    hip_last  = np.mean(track[-1]["kpts"][[11,12],1])
    r_first   = rung_index(hip_first, roi_y1, roi_y2)
    r_last    = rung_index(hip_last , roi_y1, roi_y2)
    climbed   = r_first - r_last               # wires crossed upward
    if climbed >= CRITICAL_CRITERIA: return 'red'              # Lowered from 3 to 2
    if climbed >= WARNING_CRITERIA: return 'amber'            # Lowered from 2 to 1
    return 'green'

def scaling_gantry_detection(
        redis_client,
        model, 
        camera_name, 
        frame_bytes: bytes, 
        frame_key, 
        camera_id,
        **kwargs):
    
    print("Starting scaling_gantry_detection function")
    start_preprocessing = time.time()
    
    # Need to run for detection
    pose_data = []
    # pivotal_kpts = {
    #     "head": [0],
    #     "shoulders": [5, 6],
    #     "chest": [5, 6],    # averaged later
    #     "hips": [11, 12],
    #     "knees": [13, 14],
    #     "ankles": [15, 16]
    # }
    # canv = []

    print(f"Processing frame for camera: {camera_name}")

    # Decode frame bytes to numpy array
    nparr = np.frombuffer(frame_bytes, np.uint8)
    rgb = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    # Convert BGR to RGB
    rgb = cv2.cvtColor(rgb, cv2.COLOR_BGR2RGB)
    
    # Resize the image to 1280 x 720
    target_w, target_h = 1280, 720  # (width, height) order!
    original_w, original_h = rgb.shape[1], rgb.shape[0]
    rgb = cv2.resize(rgb, (target_w, target_h),
                    interpolation=cv2.INTER_LINEAR)

    # bgr = cv2.cvtColor(rgb, cv2.COLOR_RGB2BGR)
    x1, y1, x2, y2 = ROI_EXP[map_camera_name_to_background_key(camera_name)]
    roi_h = y2 - y1

    # draw shaded bottom 33%
    y_bottom = int(y2 - roi_h / 3)

    end_preprocessing = time.time()
    end_preprocessing_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    start_prediction = time.time()

    print("Starting pose detection")
    dets_raw = run_pose(rgb[y1:y2, x1:x2], model)
    print(f"Found {len(dets_raw)} raw detections")

    end_prediction = time.time()

    if len(dets_raw) == 0:
        logger.info("No pose detection output from the model")
        print("No pose detections found, returning empty results")
        return {
            "preprocessing_time": end_preprocessing_time,
            "result": {
                "cardinal_requirements": "scaling_gantry",
                "final_results": [],
            },
        }
    
    start_scoring_1 = time.time()

    y_bottom = int(y2 - roi_h * FOOT_ZONE_FRAC)
    
    filtered_dets = []
    for d in dets_raw:
        # lift back to full-frame coords
        d["kpts"][:, 0] += x1
        d["kpts"][:, 1] += y1
        d["bbox"]       += np.array([x1, y1, x1, y1])
    
        # -------------------------------------------------------
        # 1. how much of the bbox is inside vertically?
        # -------------------------------------------------------
        bb_x1, bb_y1, bb_x2, bb_y2 = d["bbox"]
        bbox_h          = bb_y2 - bb_y1
        inside_h        = max(0, min(bb_y2, y2) - max(bb_y1, y1))
        percent_inside  = inside_h / bbox_h if bbox_h > 0 else 0
    
        # -------------------------------------------------------
        # 2. are the feet in the ground zone?
        # -------------------------------------------------------
        ankle_y         = d["kpts"][[15, 16], 1]
        feet_ok         = (ankle_y >= y_bottom).all() if REQUIRE_BOTH_FEET \
                          else (ankle_y >= y_bottom).any()
    
        # -------------------------------------------------------
        # 3. accept / reject
        # -------------------------------------------------------
        if percent_inside >= MIN_INSIDE_FRAC and feet_ok:
            filtered_dets.append(d)

    end_scoring_1 = time.time()
    print(f"Filtered down to {len(filtered_dets)} detections")

    if len(filtered_dets) == 0:
        logger.info("No detections found after filtering based on the pose in the ROI")
        print("No detections passed filtering, returning empty results")
        return {
            "preprocessing_time": end_preprocessing_time,
            "result": {
                "cardinal_requirements": "scaling_gantry",
                "final_results": [],
            },
        }

    start_cache = time.time()
    dets = filtered_dets
    print("Detections to be cached:", dets)
    
    print("Updating cache with filtered detections")
    tracks = update_cache(redis_client, camera_id, dets)
    print(f"Got {len(tracks)} tracks from cache")

    end_cache = time.time()
    start_scoring_2 = time.time()

    status = max((status_from_rungs(dq, y1, y2) for dq in tracks.values()),
                    key=('green', 'amber', 'red').index, default='green')
    print(f"Overall status determined as: {status}")

    # Rescale the bbox to the original frame size
    h_scale = original_h / target_h
    w_scale = original_w / target_w

    for pid, dq in tracks.items():
        if len(dq) < 2: continue
        # kpts = dq[-1]["kpts"]
        bbox = dq[-1]["bbox"]

        o_x1 = int(bbox[0] * w_scale)
        o_y1 = int(bbox[1] * h_scale)
        o_x2 = int(bbox[2] * w_scale)
        o_y2 = int(bbox[3] * h_scale)
        print(f"Track {pid} final bbox: [{o_x1}, {o_y1}, {o_x2}, {o_y2}]")

        # def avg_y(idxs): return np.mean(kpts[idxs, 1])
        # rungs = {part: rung_index(avg_y(idxs), y1, y2) for part, idxs in pivotal_kpts.items()}

        # kpts_flat = kpts.flatten()

        if status == "red":
            confidence = 1.0
        elif status == "amber":
            confidence = 0.5
        else:
            confidence = 0.0
        print(f"Track {pid} confidence: {confidence}")

        pose_data.append(
            {
                "event_id": str(uuid.uuid4()),
                "timestamp": frame_key.split("_")[1],
                "cam_id": camera_name,
                "category": "scaling_gantry",
                "bbox": [int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])],
                "original_bbox": [int(o_x1), int(o_y1), int(o_x2), int(o_y2)],
                "ground_x": 0.0,
                "ground_y": 0.0,
                "confidence": confidence,
                "is_primary": True,
                "group_id": -1,
                "alert_type": "L1"
            }
        )

    if len(pose_data) > 1:
    # Remove nested boxes
        df = pd.DataFrame(pose_data)
        pose_data = remove_nested_boxes(df)
        pose_data = pose_data.to_dict(orient="records")

    end_scoring_2 = time.time()

    print("\nTiming breakdown:")
    print("Processing", end_preprocessing - start_preprocessing)
    print("Prediction", end_prediction - start_prediction)
    print("Scoring 1", end_scoring_1 - start_scoring_1)
    print("Caching", end_cache - start_cache)
    print("Scoring 2", end_scoring_2 - start_scoring_2)

    logger.info(f"Returning {len(pose_data)} pose data")
    print(f"Function complete - returning {len(pose_data)} results")
    return {
        "preprocessing_time": end_preprocessing_time,
        "result": {
            "cardinal_requirements": "scaling_gantry",
            "final_results": pose_data,
        },
    }


if __name__ == "__main__":
    print("Downloading YOLO models...")
    download_models(MODEL_NAME)