# analytics_layer.py

from typing import List, Dict, <PERSON><PERSON>
import numpy as np

# Type Aliases
Point = Tuple[float, float]
KeypointsFrame = Dict[str, Point]
PoseSeries = List[KeypointsFrame]
Wire = Tuple[Point, Point]


class WireAnalyticsEngine:
    # 1. List all keypoints you care about here:
    TRACKED_POINTS = [
        "Left Knee", "Right Knee",
        "Left Ankle", "Right Ankle",
        "Left Elbow", "Right Elbow",
        "Left Wrist", "Right Wrist",
        "Nose"
    ]

    def __init__(self, tripwires: Dict[str, Wire]):
        """
        tripwires: {"wire_1": ((x1, y1), (x2, y2)), ...}
        """
        self.tripwires = tripwires

        # 2. Initialize crossed flags for every wire + every keypoint:
        self.crossed = {
            wire_id: {kp: False for kp in self.TRACKED_POINTS}
            for wire_id in tripwires
        }

    def process_pose_series(self, pose_series: PoseSeries):
        for i in range(1, len(pose_series)):
            prev = pose_series[i - 1]
            curr = pose_series[i]

            for wire_id, wire in self.tripwires.items():
                # 3. Loop over each keypoint in your TRACKED_POINTS
                for kp in self.TRACKED_POINTS:
                    if kp in prev and kp in curr:
                        if self._segments_intersect(prev[kp], curr[kp], wire[0], wire[1]):
                            self.crossed[wire_id][kp] = True

    @staticmethod
    def _segments_intersect(p1: Point, p2: Point, q1: Point, q2: Point) -> bool:
        def orientation(a, b, c):
            val = (b[1] - a[1])*(c[0] - b[0]) - (b[0] - a[0])*(c[1] - b[1])
            if np.isclose(val, 0): return 0
            return 1 if val > 0 else 2

        def on_segment(a, b, c):
            return (min(a[0], c[0]) <= b[0] <= max(a[0], c[0]) and
                    min(a[1], c[1]) <= b[1] <= max(a[1], c[1]))

        o1 = orientation(p1, p2, q1)
        o2 = orientation(p1, p2, q2)
        o3 = orientation(q1, q2, p1)
        o4 = orientation(q1, q2, p2)

        if o1 != o2 and o3 != o4:
            return True
        if o1 == 0 and on_segment(p1, q1, p2): return True
        if o2 == 0 and on_segment(p1, q2, p2): return True
        if o3 == 0 and on_segment(q1, p1, q2): return True
        if o4 == 0 and on_segment(q1, p2, q2): return True

        return False

    def get_crossings(self) -> Dict[str, Dict[str, bool]]:
        return self.crossed
