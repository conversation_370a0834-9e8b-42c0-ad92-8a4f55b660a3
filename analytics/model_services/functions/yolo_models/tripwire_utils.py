"""
Tripwire detection utilities for the scaling gantry detection pipeline.
"""

import cv2
import numpy as np
import uuid
from collections import defaultdict


class TripwireDetector:
    """
    Detector for horizontal tripwires to identify upward movement (climbing).
    """
    
    def __init__(self, num_wires=5, min_detection_size=100, persistence_frames=3, cooldown_frames=15):
        """
        Initialize the tripwire detector.
        
        Args:
            num_wires (int): Number of horizontal tripwires to create
            min_detection_size (int): Minimum size of detection in pixels
            persistence_frames (int): Number of consecutive frames needed for a valid detection
            cooldown_frames (int): Number of frames to wait before allowing another detection
        """
        self.num_wires = num_wires
        self.min_detection_size = min_detection_size
        self.persistence_frames = persistence_frames
        self.cooldown_frames = cooldown_frames
        
        # Store tripwire positions for each camera
        self.tripwires = {}
        
        # Track object positions relative to tripwires
        self.object_positions = defaultdict(dict)  # {cam_id: {object_id: position}}
        
        # Track persistence of detections
        self.detection_persistence = defaultdict(lambda: defaultdict(int))  # {cam_id: {object_id: count}}
        
        # Track cooldown periods
        self.detection_cooldown = defaultdict(int)  # {cam_id: cooldown_counter}
        
        # Store last detected objects for tracking
        self.last_objects = defaultdict(list)  # {cam_id: [objects]}
    
    def setup_tripwires(self, cam_id, roi_height):
        """
        Set up tripwires for a specific camera.
        
        Args:
            cam_id (str): Camera identifier
            roi_height (int): Height of the ROI in pixels
        """
        # Create evenly spaced tripwires
        spacing = roi_height / (self.num_wires + 1)
        tripwires = [int(spacing * (i + 1)) for i in range(self.num_wires)]
        self.tripwires[cam_id] = tripwires
        
    def get_tripwires(self, cam_id):
        """
        Get the tripwires for a specific camera.
        
        Args:
            cam_id (str): Camera identifier
            
        Returns:
            list: List of y-positions for tripwires
        """
        return self.tripwires.get(cam_id, [])
    
    def get_object_position(self, y_center, tripwires):
        """
        Get the position of an object relative to tripwires.
        
        Args:
            y_center (int): Y-coordinate of object center
            tripwires (list): List of tripwire y-positions
            
        Returns:
            int: Position index (0 = below lowest wire, n = above highest wire)
        """
        position = 0
        for wire_y in tripwires:
            if y_center > wire_y:
                position += 1
        return position
    
    def track_objects(self, cam_id, objects, timestamp):
        """
        Track objects across frames and detect upward movement.
        
        Args:
            cam_id (str): Camera ID
            objects (list): List of detected objects with bounding boxes
            
        Returns:
            list: List of objects that triggered an upward crossing alert
        """
        if cam_id not in self.tripwires:
            return []
        
        tripwires = self.tripwires[cam_id]
        alerts = []
        
        # Decrement cooldown counter if active
        if self.detection_cooldown[cam_id] > 0:
            self.detection_cooldown[cam_id] -= 1
        
        # Process each object
        current_objects = []
        for obj in objects:
            x, y, w, h = obj
            
            # Skip small objects
            if w * h < self.min_detection_size:
                continue
            
            # Calculate center point
            center_x = x + w // 2
            center_y = y + h // 2
            
            # Add to current objects list
            current_objects.append({
                "id": f"{center_x}_{center_y}_{w}_{h}",
                "bbox": (x, y, w, h),
                "center": (center_x, center_y)
            })
        
        # Match current objects with previous objects
        matched_ids = set()
        for curr_obj in current_objects:
            best_match = None
            best_distance = float('inf')
            
            for prev_obj in self.last_objects[cam_id]:
                curr_x, curr_y = curr_obj["center"]
                prev_x, prev_y = prev_obj["center"]
                
                # Calculate Euclidean distance
                distance = ((curr_x - prev_x) ** 2 + (curr_y - prev_y) ** 2) ** 0.5
                
                # Find closest match within reasonable distance
                if distance < best_distance and distance < 50:  # 50px threshold
                    best_distance = distance
                    best_match = prev_obj
            
            if best_match:
                # Use the ID from the previous frame
                curr_obj["id"] = best_match["id"]
                matched_ids.add(best_match["id"])
                
                # Get current position
                curr_position = self.get_object_position(curr_obj["center"][1], tripwires)
                
                # Get previous position
                prev_position = self.object_positions[cam_id].get(curr_obj["id"], 0)
                
                # Check for upward movement (lower position number to higher)
                if curr_position < prev_position:
                    # Increment persistence counter
                    self.detection_persistence[cam_id][curr_obj["id"]] += 1
                    
                    # Check if persistence threshold is met and cooldown is inactive
                    if (self.detection_persistence[cam_id][curr_obj["id"]] >= self.persistence_frames and
                            self.detection_cooldown[cam_id] == 0):
                        # Create alert
                        x, y, w, h = curr_obj["bbox"]
                        alerts.append({
                            "event_id": str(uuid.uuid4()),
                            "timestamp": timestamp,
                            "cam_id": cam_id,
                            "category": "scaling_gantry",
                            "bbox": [x, y, x + w, y + h],
                            "original_bbox": [x, y, x + w, y + h],
                            "ground_x": 0.0,
                            "ground_y": 0.0,
                            "confidence": 0.95,
                            "is_primary": True,
                            "wire_crossed": prev_position
                        })
                        
                        # Reset persistence counter
                        self.detection_persistence[cam_id][curr_obj["id"]] = 0
                        
                        # Set cooldown
                        self.detection_cooldown[cam_id] = self.cooldown_frames
                else:
                    # Reset persistence counter if no upward movement
                    self.detection_persistence[cam_id][curr_obj["id"]] = 0
                
                # Update position
                self.object_positions[cam_id][curr_obj["id"]] = curr_position
        
        # Clean up unmatched objects
        for prev_obj in self.last_objects[cam_id]:
            if prev_obj["id"] not in matched_ids:
                # Remove from tracking
                if prev_obj["id"] in self.object_positions[cam_id]:
                    del self.object_positions[cam_id][prev_obj["id"]]
                if prev_obj["id"] in self.detection_persistence[cam_id]:
                    del self.detection_persistence[cam_id][prev_obj["id"]]
        
        # Update last objects
        self.last_objects[cam_id] = current_objects
        
        return alerts
    
    # Visualization methods have been moved to tripwire_visualization.py


def detect_objects_for_tripwire(frame, control_frame=None):
    """
    Detect moving objects in a frame for tripwire detection.
    
    Args:
        frame (numpy.ndarray): Current frame
        control_frame (numpy.ndarray, optional): Control/background frame
        
    Returns:
        list: List of bounding boxes (x, y, w, h)
    """
    # Convert to grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    bboxes = []
    
    if control_frame is not None:
        # Use background subtraction if control frame is available
        control_gray = cv2.cvtColor(control_frame, cv2.COLOR_BGR2GRAY)
        
        # Calculate absolute difference
        diff = cv2.absdiff(control_gray, gray)
        
        # Apply Gaussian blur to reduce noise
        diff = cv2.GaussianBlur(diff, (5, 5), 0)
        
        # Threshold
        _, thresh = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)
        
        # Noise removal
        kernel = np.ones((5, 5), np.uint8)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Dilate to connect nearby regions
        thresh = cv2.dilate(thresh, kernel, iterations=1)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Extract bounding boxes from contours
        for contour in contours:
            # Get bounding box
            x, y, w, h = cv2.boundingRect(contour)
            
            # Filter by size (adjust as needed)
            if w * h > 100 and w > 10 and h > 10:
                bboxes.append((x, y, w, h))
    else:
        # If no control frame, use other detection methods
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Use adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # Noise removal
        kernel = np.ones((5, 5), np.uint8)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Extract bounding boxes from contours
        for contour in contours:
            # Get area and bounding box
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            
            # Filter by size and aspect ratio
            aspect_ratio = float(w) / h if h > 0 else 0
            if area > 200 and w > 15 and h > 15 and 0.2 < aspect_ratio < 5.0:
                bboxes.append((x, y, w, h))
    
    # Merge overlapping boxes
    if len(bboxes) > 1:
        merged_bboxes = []
        bboxes_copy = bboxes.copy()
        
        while bboxes_copy:
            current = bboxes_copy.pop(0)
            x1, y1, w1, h1 = current
            
            # Check if current box overlaps with any remaining box
            i = 0
            while i < len(bboxes_copy):
                x2, y2, w2, h2 = bboxes_copy[i]
                
                # Check for overlap
                if (x1 < x2 + w2 and x1 + w1 > x2 and 
                    y1 < y2 + h2 and y1 + h1 > y2):
                    # Merge boxes
                    x_min = min(x1, x2)
                    y_min = min(y1, y2)
                    x_max = max(x1 + w1, x2 + w2)
                    y_max = max(y1 + h1, y2 + h2)
                    
                    current = (x_min, y_min, x_max - x_min, y_max - y_min)
                    x1, y1, w1, h1 = current
                    
                    # Remove the merged box
                    bboxes_copy.pop(i)
                else:
                    i += 1
            
            merged_bboxes.append(current)
        
        bboxes = merged_bboxes
    
    return bboxes
