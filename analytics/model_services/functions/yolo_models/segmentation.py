import json
try:
    from functions.yolo_models.commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
except ModuleNotFoundError:
    from commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image

MODEL_NAME = "yolov8s-seg"
DEVICE = check_cuda_availability()
# SEGMENTATION_MODEL = load_yolo_model(MODEL_NAME, DEVICE)


def predict(model, img, classes=[], conf=0.5, device=DEVICE):
    # Use the specified device for prediction
    if classes:
        results = model.predict(img, classes=classes, conf=conf, device=device)
    else:
        results = model.predict(img, conf=conf, device=device)
    return results


def detect_object_segmentation(frame_bytes, model, classes=[], conf=0.5, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, **kwargs):
    image_obj = bytes_to_image(frame_bytes)
    # Predict segmentation masks
    results = predict(model, image_obj, classes, conf)
    # Convert the results to a JSON-friendly format
    results = results[0].to_json()
    # Convert the results to a list
    results = json.loads(results)

    # Initialize the detection results list
    detections = []

    for result in results:
        detection = {
            'object_name': result.get('name'),
            'confidence': result.get('confidence'),
            'coordinates': {
                'x1': float(result.get('box', {}).get('x1')),
                'y1': float(result.get('box', {}).get('y1')),
                'x2': float(result.get('box', {}).get('x2')),
                'y2': float(result.get('box', {}).get('y2'))
            },
            'segmentation': {
                'x': result.get('segments', {}).get('x'),
                'y': result.get('segments', {}).get('y')
            },
            'style': {
                'rectangle_color': rectangle_color,
                'rectangle_thickness': rectangle_thickness,
                'text_thickness': text_thickness
            }
        }

        detections.append(detection)
    
    return detections 


if __name__ == "__main__":
    print("Downloading YOLO models...")
    download_models(MODEL_NAME)