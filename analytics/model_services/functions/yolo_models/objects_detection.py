try:
    from functions.yolo_models.commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
except ModuleNotFoundError:
    from commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image

MODEL_NAME = "yolov10m"
DEVICE = check_cuda_availability()
# OBJECT_DETECTION_MODELS = load_yolo_model(MODEL_NAME, DEVICE)


def predict(chosen_model, img, classes=[], conf=0.5, device=DEVICE):
    # Use the specified device for prediction
    if classes:
        results = chosen_model.predict(img, classes=classes, conf=conf, device=device)
    else:
        results = chosen_model.predict(img, conf=conf, device=device)

    return results


def detect_object(frame_bytes, model, classes=[], conf=0.5, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, **kwargs):
    image_obj = bytes_to_image(frame_bytes)
    # Predict the object
    results = predict(model, image_obj, classes, conf)

    # Initialize the detection results list
    detections = []
    
    # Process each detection
    for result in results:
        for box in result.boxes:
            detection = {
                'object_name': result.names[int(box.cls[0])],
                'coordinates': {
                    'x1': float(box.xyxy[0][0]),
                    'y1': float(box.xyxy[0][1]),
                    'x2': float(box.xyxy[0][2]),
                    'y2': float(box.xyxy[0][3])
                },
                'confidence': float(box.conf[0]),
                'style': {
                    'rectangle_color': rectangle_color,
                    'rectangle_thickness': rectangle_thickness,
                    'text_thickness': text_thickness
                }
            }
            detections.append(detection)

    return detections


if __name__ == "__main__":
    print("Downloading YOLO models...")
    download_models(MODEL_NAME)
