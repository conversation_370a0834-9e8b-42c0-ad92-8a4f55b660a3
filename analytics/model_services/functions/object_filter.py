import yaml
from pathlib import Path
from typing import List, Dict, Any, Tuple
from datetime import datetime
import time
import logging

logger = logging.getLogger(__name__)

def load_filter_words(yaml_path: str = None) -> Dict[str, List[str]]:
    """
    Load filter words from the detections-prompt.yml file.
    Returns a dictionary mapping category names to their filter words.
    """
    if yaml_path is None:
        # Get the directory of the current file
        current_dir = Path(__file__).parent
        yaml_path = current_dir / "detections-prompt.yml"
    
    with open(yaml_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Extract filter words for each category
    filter_words = {}
    for category, data in config.items():
        if isinstance(data, dict) and 'filter_words' in data:
            filter_words[category] = data['filter_words']
    
    return filter_words

def filter_weapon_objects(dependency_result: List[Dict[str, Any]], filter_words_dict, **kwargs) -> List[Dict[str, Any]]:
    """
    Filter objects based on keywords from detections-prompt.yml.
    
    Args:
        dependency_result (List[Dict]): List of object detections
        filter_words_dict (Dict): Dictionary containing filter words
        
    Returns:
        List[Dict]: Filtered list of detections that match the keywords
    """    
    # If the function name doesn't exist in the filter words, return empty list:
    keywords = filter_words_dict["weapon"]["filter_words"]
    
    filtered_results = []
    
    # Loop through each detection
    for detection in dependency_result:
        # Get the class name or description from the detection
        object_class = detection.get('object_name', '').lower()
        
        # Check if any of the keywords appear in the object class
        for keyword in keywords:
            if keyword.lower() in object_class:
                filtered_results.append(detection)
    
    return filtered_results

def consolidate_alerts(alerts_data: List[Dict[str, Any]], 
                      camera_id: str, 
                      camera_layer_config_id: str,
                      frame_id: str,
                      redis_client: Any) -> Tuple[List[Dict[str, Any]], bool]:
    """
    Consolidate alerts and check throttling before sending.
    
    Args:
        alerts_data (List[Dict]): List of alert detections
        camera_id (str): ID of the camera
        camera_layer_config_id (str): ID of the camera layer config
        frame_id (str): ID of the current frame
        redis_client: Redis client instance
        
    Returns:
        Tuple[List[Dict], bool]: List of enriched alerts and whether any alerts were added
    """
    consolidated_alerts = []
    frame_has_alert = False
    
    for alert in alerts_data:
        state = alert.get("state", "normal").lower()
        if state == "normal":
            # Skip normal state alerts
            continue
            
        # Determine entity type and ID field
        if "pid" in alert:
            entity_type = "person"
            entity_id = str(alert.get("pid", "")).strip()
            id_field = "pid"
        else:
            entity_type = "object"
            entity_id = str(alert.get("oid", "")).strip()
            id_field = "oid"
            
        if not entity_id:
            logger.warning(f"No {id_field} in alert; skipping.")
            continue
            
        # Check global (cross-camera) throttling
        should_send, redis_key = _should_send_alert(
            redis_client=redis_client,
            entity_type=entity_type,
            requirement_key=alert.get("event_type", "default"),
            entity_id=entity_id,
            state=state
        )
        
        if not should_send:
            logger.debug(
                f"Skipping re-alert for {entity_type} '{entity_id}' "
                f"under '{alert.get('event_type', 'default')}' (state={state})"
            )
            continue
            
        # Build enriched payload
        enriched_alert = {
            **alert,
            "camera_id": camera_id,
            "camera_layer_config_id": camera_layer_config_id,
            "confidence": alert.get("confidence", 0.0),
            "event_type": alert.get("event_type", "default") if alert.get("event_type") != "default" else "custom",
            "event_severity": "Critical" if state == "critical" else "Warning",
            "bounding_boxes": [alert.get("bounding_box", {})],
            "timestamp": datetime.now().isoformat(),
            "frame_id": frame_id,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "redis_key": redis_key
        }
        
        consolidated_alerts.append(enriched_alert)
        frame_has_alert = True
        
    return consolidated_alerts, frame_has_alert

def _should_send_alert(redis_client: Any,
                      entity_type: str,
                      requirement_key: str,
                      entity_id: str,
                      state: str) -> Tuple[bool, str]:
    """
    Check if an alert should be sent based on throttling rules.
    
    Args:
        redis_client: Redis client instance
        entity_type (str): Type of entity (person/object)
        requirement_key (str): Type of requirement/event
        entity_id (str): ID of the entity
        state (str): Alert state
        
    Returns:
        Tuple[bool, str]: Whether alert should be sent and the Redis key used
    """
    STATE_THRESHOLDS = {
        "warning": 60,    # 1 minute
        "critical": 300,  # 5 minutes
    }
    
    if state not in STATE_THRESHOLDS:
        return True, ""
        
    redis_key = f"last_alert:{entity_type}:{requirement_key}:{entity_id}:{state}"
    
    try:
        last_ts = redis_client.get(redis_key)
        if last_ts is None:
            return True, redis_key
            
        last_ts = float(last_ts)
        now_ts = time.time()
        
        if (now_ts - last_ts) < STATE_THRESHOLDS[state]:
            return False, redis_key
            
    except Exception as e:
        logger.error(f"Error checking alert throttle in Redis: {e}")
        return True, redis_key
        
    return True, redis_key
