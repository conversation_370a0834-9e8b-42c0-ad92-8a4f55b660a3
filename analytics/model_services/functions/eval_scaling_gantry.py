# wall_monitor.py
from __future__ import annotations
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import logging
import redis
from dataclasses import dataclass
import json
from redis_connection import CameraCache

logger = logging.getLogger(__name__)

@dataclass
class ROI:
    """Region‑of‑interest description."""
    roi_type: str                       # "line" or "region"
    coordinates: List[Dict[str, float]]
    alerts_category: str               # "Warning" or "Critical"
    criteria: Dict[str, Any]           # e.g. {"requirements": "above the line", ...}

class PersonDetection:
    """Normalised output of the detector for one person."""

    def __init__(self, detection_data: dict):
        self.bbox = (
            detection_data["coordinates"]["x1"],
            detection_data["coordinates"]["y1"],
            detection_data["coordinates"]["x2"],
            detection_data["coordinates"]["y2"],
        )
        kps_raw = {
            k: (v["x"], v["y"]) for k, v in detection_data.get("keypoints", {}).get("landmarks", {}).items()
        }
        self.keypoints = self._clean_keypoints(self.bbox, kps_raw)
        self.pid = detection_data.get("pid")
        self.confidence = detection_data.get("confidence", 0.0)
        self.state = "normal"

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------
    def _clean_keypoints(self, bbox, kps_raw):
        # ❗ TODO: real cleaning – this is a passthrough placeholder
        return kps_raw

    def center(self):
        x1, y1, x2, y2 = self.bbox
        return ((x1 + x2) / 2, (y1 + y2) / 2)

class GantryMonitoring:
    def __init__(self, redis_client: CameraCache):
        self.redis_client = redis_client
        self.rois: Dict[str, List[ROI]] = {
            "Warning": [],
            "Critical": []
        }

    # ---------------------------  ROI setup  ------------------------------
    def setup_rois(self, roi_data: List[dict]):
        if isinstance(roi_data, str):
            roi_data = json.loads(roi_data)
        for roi_dict in roi_data:
            roi = ROI(
                roi_type=roi_dict["roi_type"],
                coordinates=roi_dict["coordinates"],
                alerts_category=roi_dict["alerts_category"],
                criteria=roi_dict["criteria"],
            )
            self.rois[roi.alerts_category].append(roi)

    def get_frame_detections(self, frame_id: int) -> List[PersonDetection]:
        """Get detections from Redis for given frame"""
        print(f"Getting frame detections for frame {frame_id}")
        
        frame_key = f"{frame_id}_people_tracking"
        print(f"Frame key pulling from eval_scaling_gantry: {frame_key}")
        detections_data = self.redis_client.get(frame_key)
        print(f"Detections data: {detections_data}")
        if not detections_data:
            print(f"No detections found for frame {frame_id}")
            return []
        
        try:
            # detections_data = json.loads(detections_data)
            print(f"Found {len(detections_data)} detections in frame {frame_id}")
            return [PersonDetection(d) for d in detections_data]
        except Exception as e:
            print(f"Error parsing detections for frame {frame_id}: {str(e)}")
            return []

    def get_historical_frame_detections(self, frame_ids: List[int]) -> List[List[PersonDetection]]:
        """Get historical frame detections from Redis for given frame IDs"""
        print(f"Getting historical detections for frames: {frame_ids}")
        historical_detections = []

        if frame_ids is None:
            return []
        
        for frame_id in frame_ids:
            historical_detections.append(self.get_frame_detections(frame_id))
        return historical_detections

    def get_person_history(self, target_pid: str, historical_detections: List[List[PersonDetection]]) -> List[PersonDetection]:
        """Get person detection history from Redis for a specific person"""
        print(f"Building history for person {target_pid}")
        history = []

        if len(historical_detections) == 0:
            return []
        
        for i, frame_detections in enumerate(historical_detections):
            found = False
            for detection in frame_detections:
                if detection.pid == target_pid:
                    history.append(detection)
                    found = True
                    break
            if not found:
                logger.debug(f"Person {target_pid} not found in frame {i}")
        
        print(f"Found person {target_pid} in {len(history)}/{len(historical_detections)} frames")
        return history

    def _get_available_points(self, keypoints: dict, point_list: list) -> list:
        return [keypoints[p] for p in point_list if p in keypoints and keypoints[p] is not None]

    def _is_above_line(self, pt: Tuple[float, float], line: List[Tuple[float, float]]) -> bool:
        # Two‑point line: use cross‑product sign
        (x1, y1), (x2, y2) = line
        return (x2 - x1) * (pt[1] - y1) - (y2 - y1) * (pt[0] - x1) < 0

    def _check_above_line(self, pts: List[Tuple[float, float]], coords: List[Dict[str, float]]) -> bool:
        line = [(c["x"], c["y"]) for c in coords]
        xmin, xmax = sorted(p[0] for p in line)
        for p in pts:
            if xmin <= p[0] <= xmax and self._is_above_line(p, line):
                return True
        return False

    def _point_in_poly(self, pt: Tuple[float, float], poly: np.ndarray) -> bool:
        # Ray‑casting algorithm
        x, y = pt
        inside = False
        n = len(poly)
        px, py = poly.T
        for i in range(n):
            j = (i - 1) % n
            intersect = ((py[i] > y) != (py[j] > y)) and (
                x < (px[j] - px[i]) * (y - py[i]) / (py[j] - py[i] + 1e-9) + px[i]
            )
            if intersect:
                inside = not inside
        return inside

    def _check_inside_region(
        self,
        pts: List[Tuple[float, float]],
        coords: List[Dict[str, float]],
    ) -> bool:
        """
        Return **True** as soon as *any* supplied key-point falls inside the
        polygon. (Previously required *all* key-points to be inside.)

        Args
        ----
        pts    : key-points to test, e.g. [(x, y), …]
        coords : ROI polygon – list of {"x":…, "y":…} vertices
        """
        poly = np.array([(c["x"], c["y"]) for c in coords], dtype=np.float32)
        return any(self._point_in_poly(p, poly) for p in pts)

    def _check_vertical_motion(self, history: List[PersonDetection]) -> bool:
        """
        Check for vertical motion in key body points (hips and shoulders)
        Returns True if there is significant upward motion in any of the key points
        """
        if len(history) < 5:
            logger.debug("Insufficient history for vertical motion check")
            return False
            
        # Extract key points from history
        left_hip_ys = np.array([h.keypoints.get('left_hip', [0, 0])[1] for h in history])
        right_hip_ys = np.array([h.keypoints.get('right_hip', [0, 0])[1] for h in history])
        left_shoulder_ys = np.array([h.keypoints.get('left_shoulder', [0, 0])[1] for h in history])
        right_shoulder_ys = np.array([h.keypoints.get('right_shoulder', [0, 0])[1] for h in history])
        
        # Apply smoothing to reduce noise
        window_size = 5
        left_hip_smooth = np.convolve(left_hip_ys, np.ones(window_size)/window_size, mode='valid')
        right_hip_smooth = np.convolve(right_hip_ys, np.ones(window_size)/window_size, mode='valid')
        left_shoulder_smooth = np.convolve(left_shoulder_ys, np.ones(window_size)/window_size, mode='valid')
        right_shoulder_smooth = np.convolve(right_shoulder_ys, np.ones(window_size)/window_size, mode='valid')
        
        # Calculate vertical displacement for each point
        displacements = {
            'left_hip': left_hip_smooth[0] - left_hip_smooth[-1],
            'right_hip': right_hip_smooth[0] - right_hip_smooth[-1],
            'left_shoulder': left_shoulder_smooth[0] - left_shoulder_smooth[-1],
            'right_shoulder': right_shoulder_smooth[0] - right_shoulder_smooth[-1]
        }
        
        # Log the displacements
        for point, displacement in displacements.items():
            logger.debug(f"Vertical displacement for {point}: {displacement:.2f}")
        
        # Check if any point shows significant upward motion (threshold: 40 pixels)
        significant_motion = any(displacement > 40 for displacement in displacements.values())
        
        if significant_motion:
            logger.debug("Detected significant vertical motion in key points")
        else:
            logger.debug("No significant vertical motion detected")
            
        return significant_motion
    
    # ---------------------------  Evaluation  -----------------------------
    def evaluate_person(
        self,
        person: PersonDetection,
        historical_detections: List[List[PersonDetection]]
    ) -> str:
        """
        Return "normal", "warning", or "critical".

        Rules
        -----
        1.  Warning gate  → BOTH warning ROIs must pass.
        2.  Critical gate → need ≥2 of 3 critical-ROI checks *plus* the
            vertical-motion check (treated as a 4th test).  Any 2-of-4
            passes = CRITICAL.
        """

        print(f"\n┌── Evaluating PID={person.pid} ────────────────────────────")

        # ── 1️⃣  WARNING GATE ────────────────────────────────────────────────
        above_warn_hit  = False
        inside_warn_hit = False

        for idx, roi in enumerate(self.rois["Warning"], 1):
            pts = self._get_available_points(
                person.keypoints,
                roi.criteria.get("pose_keypoints", [])
            )
            req = roi.criteria.get("requirements", "??")
            tag = f"[W{idx:02d} {roi.roi_type.upper():6s}]"

            if not pts:
                print(f"{tag} {req:<18} keypoints=0 → SKIP")
                continue

            if roi.roi_type == "line" and req == "above the line":
                passed = self._check_above_line(pts, roi.coordinates)
                above_warn_hit |= passed
            elif roi.roi_type == "region" and req == "inside the region":
                passed = self._check_inside_region(pts, roi.coordinates)
                inside_warn_hit |= passed
            else:
                passed = False

            print(f"{tag} {req:<18} → {'PASS' if passed else 'FAIL'}")

        print(f"├─ Warning summary → above_line={above_warn_hit}, inside_region={inside_warn_hit}")

        if not (above_warn_hit and inside_warn_hit):
            print("└─ Result: NORMAL (warning gate failed)\n")
            return "normal"

        # ── 2️⃣  CRITICAL GATE ───────────────────────────────────────────────
        critical_passes = 0

        # (a) three spatial critical ROIs
        for idx, roi in enumerate(self.rois["Critical"], 1):
            pts = self._get_available_points(
                person.keypoints,
                roi.criteria.get("pose_keypoints", [])
            )
            req = roi.criteria.get("requirements", "??")
            tag = f"[C{idx:02d} {roi.roi_type.upper():6s}]"

            if not pts:
                print(f"{tag} {req:<18} keypoints=0 → SKIP")
                continue

            if roi.roi_type == "line"   and req == "above the line":
                passed = self._check_above_line(pts, roi.coordinates)
            elif roi.roi_type == "region" and req == "inside the region":
                passed = self._check_inside_region(pts, roi.coordinates)
            else:
                passed = False

            if passed:
                critical_passes += 1
            print(f"{tag} {req:<18} → {'PASS' if passed else 'FAIL'}")

        # (b) vertical-motion check
        history = self.get_person_history(person.pid, historical_detections)
        if history:
            motion_hit = self._check_vertical_motion(history)
            print(f"[CM VERT] vertical motion              → {'PASS' if motion_hit else 'FAIL'}")
            if motion_hit:
                critical_passes += 1
        else:
            print("[CM VERT] vertical motion              → SKIP (no history / no line ROI)")

        print(f"├─ Critical summary → passes={critical_passes}/4")

        if critical_passes >= 2:
            print("└─ Result: CRITICAL (≥2 critical conditions)\n")
            return "critical"

        print("└─ Result: WARNING (warning gate OK, <2 critical conditions)\n")
        return "warning"

    # -----------------------  Frame processing  ---------------------------
    def process_frame(self, camera_id: str, ts: str, frame_history: List[int]):
        """Return structured alerts (state, pid, bbox …) – offline variant."""

        frame_id = f"{camera_id}_{ts}"
        detections = self.get_frame_detections(frame_id)
        history = self.get_historical_frame_detections(frame_history)  # still called, but empty

        results = []
        for person in detections:
            state = self.evaluate_person(person, history)
            x1, y1, x2, y2 = person.bbox
            alert = {
                "state": state,
                "pid": person.pid,
                "frame_id": frame_id,
                "bounding_box": {"x1": x1, "y1": y1, "x2": x2, "y2": y2},
                "reason": None,
            }
            if state == "critical":
                alert["reason"] = "Person is scaling the gantry"
            elif state == "warning":
                alert["reason"] = "Person is suspected of scaling the gantry"
            results.append(alert)
        return results
    
def eval_scaling_gantry(redis_client: CameraCache, camera_id: str, timestamp: str, frame_history_ids: List[int], dependency_result: dict, **kwargs):
    print("Running eval_scaling_gantry")

    # Get the regions of interest from the dependency result
    print(f"Dependency result: {dependency_result}")
    regions_of_interest = dependency_result.get("Trip Wire", [])
    regions_of_interest = regions_of_interest.get("result", [])
    print(f"Raw regions of interest: {regions_of_interest}")
    
    # Initialize gantry monitoring
    gantry_monitoring = GantryMonitoring(redis_client)
    print("Initialized gantry monitoring")

    # Setup ROIs
    print(f"Regions of interest: {regions_of_interest}")
    if regions_of_interest:
        gantry_monitoring.setup_rois(regions_of_interest)
        print(f"Setup {sum(len(rois) for rois in gantry_monitoring.rois.values())} ROIs")
        logger.debug(f"Warning ROIs: {len(gantry_monitoring.rois['Warning'])}")
        logger.debug(f"Critical ROIs: {len(gantry_monitoring.rois['Critical'])}")
    else:
        logger.warning("No ROIs found in database")
    
    # Process frame
    results = gantry_monitoring.process_frame(camera_id, timestamp, frame_history_ids)
    print(f"Processed frame. Found {len(results)} alerts")
    logger.debug(f"Results: {results}")

    results = {
        "cardinal_requirements": "scaling_gantry",
        "final_results": results
    }
    
    return results
