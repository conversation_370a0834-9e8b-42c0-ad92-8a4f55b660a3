"""
Alert generation and L2 promotion logic.
"""

import pandas as pd
import numpy as np

from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD

# Loading in the config
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
L2_RULES = decrypted_config["L2_RULES"]

def run_l2_promotion(df, current_time, rules=L2_RULES, window=10.0):
    """
    Run L2 promotion logic on L1 detections.
    
    Args:
        df (pandas.DataFrame): DataFrame of L1 detections
        current_time (float): Current timestamp
        rules (dict): Rules for L2 promotion
        window (float): Time window to consider
        
    Returns:
        list: L2 promoted alerts
    """
    if df.empty:
        return []
    
    # Convert current_time from float to datetime if needed
    if isinstance(current_time, (int, float)):
        current_time = pd.to_datetime(current_time, unit='s')
    print(f"Current time: {current_time}")
    # Filter to recent detections within window
    recent = df[df["timestamp"] >= current_time - pd.Timedelta(seconds=window)]
    print(f"Recent detections: {recent}")
    if recent.empty:
        return []
    
    # Group by category
    grouped = recent.groupby("category")
    
    # First, evaluate all categories against their rules
    potential_alerts = []
    category_stats = {}
    
    for category, group in grouped:
        # Skip if no rules for this category
        if category not in rules:
            continue
            
        rule = rules[category]
        
        # Count unique cameras that detected this category
        unique_cams = list(set(group["cam_id"]))
        
        # Count total detections for this category
        detection_count = len(group)
        
        # Store stats for this category
        category_stats[category] = {
            "unique_cams": unique_cams,
            "detection_count": detection_count,
            "confidence_avg": group["confidence"].mean(),
            "meets_criteria": (len(unique_cams) >= rule["min_cams"] and 
                              detection_count >= rule["min_frames"] and
                              group["confidence"].mean() >= rule["min_confidence"])
        }
        
        # Check if meets criteria
        if category_stats[category]["meets_criteria"]:
            # Create L2 alert
            potential_alerts.append({
                "category": category,
                "count": detection_count,
                "unique_cams": unique_cams,
                "confidence_avg": group["confidence"].mean(),
                "timestamp_min": group["timestamp"].min(),
                "timestamp_max": group["timestamp"].max(),
                "timestamp_range": (group["timestamp"].max() - group["timestamp"].min()).total_seconds(),
                "source_detections": group["event_id"].tolist(),
                "alert_type": "L2"  # Add alert type flag to identify L2 alerts
            })
    
    # Check for categories with overwhelming signal
    # If a category has significantly more detections than others and meets its criteria,
    # ensure it's included in the alerts
    alerts = []
    
    # First, include any category that meets its criteria
    for alert in potential_alerts:
        alerts.append(alert)
    
    # Special case for oversized_object with overwhelming signal
    if "oversized_object" in category_stats:
        oversized_stats = category_stats["oversized_object"]
        
        # Check if oversized_object has overwhelming signal but wasn't promoted
        if not oversized_stats.get("meets_criteria", False):
            # Define what constitutes 'overwhelming signal' - high detection count across multiple cameras
            has_overwhelming_signal = (
                oversized_stats["detection_count"] >= 5 and  # At least 5 detections
                len(oversized_stats["unique_cams"]) >= 2 and      # On at least 2 cameras
                oversized_stats["confidence_avg"] >= 0.25    # With reasonable confidence
            )
            
            if has_overwhelming_signal:
                # Add oversized_object to alerts even if it didn't strictly meet the criteria
                alerts.append({
                    "category": "oversized_object",
                    "count": oversized_stats["detection_count"],
                    "unique_cams": oversized_stats["unique_cams"],
                    "confidence_avg": oversized_stats["confidence_avg"],
                    "timestamp_min": recent[recent["category"] == "oversized_object"]["timestamp"].min(),
                    "timestamp_max": recent[recent["category"] == "oversized_object"]["timestamp"].max(),
                    "timestamp_range": recent[recent["category"] == "oversized_object"]["timestamp"].max() - 
                                       recent[recent["category"] == "oversized_object"]["timestamp"].min(),
                    "source_detections": recent[recent["category"] == "oversized_object"]["event_id"].tolist(),
                    "alert_type": "L2",
                    "promotion_reason": "overwhelming_signal"
                })

    print("Done with oversized_object")
    
    return alerts


def detect_persistent_objects_with_penalty(detections, time_window=10.0, min_detections=3, 
                                          min_confidence=0.3, max_cameras=None):
    """
    Detect persistent objects across multiple frames with penalty for too many cameras.
    
    Args:
        detections (list): List of detection dictionaries
        time_window (float): Time window to consider
        min_detections (int): Minimum number of detections required
        min_confidence (float): Minimum confidence threshold
        max_cameras (int): Maximum number of cameras (None for no limit)
        
    Returns:
        list: Persistent object alerts
    """
    if not detections:
        return []
    
    df = pd.DataFrame(detections)
    
    # Ensure timestamp column is datetime
    if 'timestamp' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Group by category
    grouped = df.groupby("category")
    
    alerts = []
    for category, group in grouped:
        # Skip certain categories
        if category in ["human", "unmatched"]:
            continue
            
        # Count detections and cameras
        detection_count = len(group)
        unique_cams = group["cam_id"].nunique()
        
        # Apply camera penalty if specified
        camera_penalty = 0
        if max_cameras and unique_cams > max_cameras:
            camera_penalty = (unique_cams - max_cameras) * 0.1
        
        # Calculate adjusted confidence
        adjusted_confidence = group["confidence"].mean() - camera_penalty
        
        # Check if meets criteria
        if (detection_count >= min_detections and 
            adjusted_confidence >= min_confidence):
            
            # Calculate time range in seconds
            time_range = (group["timestamp"].max() - group["timestamp"].min()).total_seconds()
            
            # Only include if within time window
            if time_range <= time_window:
                alerts.append({
                    "category": category,
                    "count": detection_count,
                    "unique_cams": unique_cams,
                    "confidence_avg": group["confidence"].mean(),
                    "adjusted_confidence": adjusted_confidence,
                    "timestamp_min": group["timestamp"].min(),
                    "timestamp_max": group["timestamp"].max(),
                    "timestamp_range": time_range,
                    "source_detections": group["event_id"].tolist()
                })
    
    return alerts
