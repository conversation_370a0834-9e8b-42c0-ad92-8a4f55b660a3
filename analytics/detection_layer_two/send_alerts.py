import json
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict

import cv2
import numpy as np
import logging
import base64
import time

from redis_connection import CameraCache
from logger_setup import setup_logging

# Import DatabaseConnection for direct database access
from db_connection import DatabaseConnection

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Create a singleton instance of DatabaseConnection
db_connection = None

def get_db_connection():
    """Get or create a singleton database connection"""
    global db_connection
    if db_connection is None:
        db_connection = DatabaseConnection()
    return db_connection


def send_alerts(alert_data: Dict[str, Any], redis_client=None) -> bool:
    """
    Save alert data to the database first, then send only the event ID to the Redis event queue.
    This approach avoids race conditions between the frontend and batch processor.
    
    NOTE: This function assumes that the bounding boxes have already been drawn on the frame
    by the overlay_box_and_alerts function, so it doesn't redraw them.
    
    Args:
        alert_data: Dictionary containing alert information including:
            - state: The alert state (critical, warning)
            - reason: The reason for the alert
            - bounding_box: The coordinates of the bounding box
            - camera_layer_config_id: The ID of the camera layer configuration
            - confidence: The confidence score (0-1)
            - event_severity: Critical or Warning
            - event_type: Type of event (requirement_key)
            - timestamp: ISO format timestamp
            - frame_id: ID of the frame
            - camera_id: ID of the camera
            - frame_bytes: Already processed frame with bounding boxes drawn
        redis_client: Optional Redis client instance. If not provided, no Redis notification will be sent.
            
    Returns:
        bool: True if alert was successfully created, False otherwise
    """
    try:
        # print(f"Starting send_alerts with alert_data: {alert_data}")
        # logger.info(f"Starting send_alerts with alert_data: {alert_data}")

        # If redis_client was included in the alert_data, use it and remove it before serializing
        if redis_client is None and 'redis_client' in alert_data:
            redis_client = alert_data.get('redis_client')
            del alert_data['redis_client']
            print("Using redis_client from alert_data")
            logger.debug("Using redis_client from alert_data")
        
        # Generate a unique ID for the event if not already present
        event_id = alert_data.get('id', str(uuid.uuid4()))
        if 'id' not in alert_data:
            alert_data['id'] = event_id
        print(f"Using event_id: {event_id}")
        logger.debug(f"Using event_id: {event_id}")
        
        # If we have image bytes, encode them as base64 string
        if 'frame_bytes' in alert_data and isinstance(alert_data['frame_bytes'], (bytes, bytearray)):
            alert_data['frame_bytes'] = CameraCache.to_jsonable_bytes(alert_data['frame_bytes'])
            print("Encoded image bytes to base64")
            logger.debug("Encoded image bytes to base64")
        
        # Extract a snippet from the already processed frame (with boxes already drawn)
        # for the notification thumbnail
        if 'frame_bytes' in alert_data and 'bounding_box' in alert_data:
            try:
                print("Starting frame crop extraction")
                logger.debug("Starting frame crop extraction")
                # Extract the box region from the frame for notification
                # Note: This frame already has the bounding boxes and text drawn on it
                # from the overlay_box_and_alerts function
                frame_bytes = alert_data['frame_bytes']
                if isinstance(frame_bytes, str):
                    # If it's a string (likely base64), convert to bytes
                    frame_bytes = base64.b64decode(frame_bytes)
                
                frame = cv2.imdecode(np.frombuffer(frame_bytes, dtype=np.uint8), cv2.IMREAD_COLOR)
                if frame is None:
                    raise ValueError("Failed to decode frame bytes")
                    
                bbox = alert_data['bounding_box']
                x1, y1 = int(bbox.get('x1', 0)), int(bbox.get('y1', 0))
                x2, y2 = int(bbox.get('x2', frame.shape[1])), int(bbox.get('y2', frame.shape[0]))

                # Add 20% of the width and height to the bounding box
                padding = 0.2
                width_padding  = frame.shape[1] * padding
                height_padding = frame.shape[0] * padding
                x1 = max(0, int(x1 - width_padding))
                y1 = max(0, int(y1 - height_padding))
                x2 = min(frame.shape[1], int(x2 + width_padding))
                y2 = min(frame.shape[0], int(y2 + height_padding))
                
                print(f"Crop coordinates: x1={x1}, y1={y1}, x2={x2}, y2={y2}")
                logger.debug(f"Crop coordinates: x1={x1}, y1={y1}, x2={x2}, y2={y2}")
                
                # Just extract the region without redrawing the box (already drawn)
                box_crop = frame[y1:y2, x1:x2]
                if box_crop.size > 0:
                    # Make sure we actually have some content in the crop region
                    # Encode as JPG and save to the alert data
                    _, img_crop_encoded = cv2.imencode('.jpg', box_crop)
                    alert_data['frame_bytes'] = CameraCache.to_jsonable_bytes(img_crop_encoded.tobytes())
                    print(f"Successfully cropped and encoded frame, crop size: {box_crop.shape}")
                    logger.debug(f"Successfully cropped and encoded frame, crop size: {box_crop.shape}")
            except Exception as e:
                print(f"Error extracting box crop from frame: {e}")
                logger.error(f"Error extracting box crop from frame: {e}")
                print(f"Frame shape: {frame.shape if 'frame' in locals() else 'unknown'}")
                logger.debug(f"Frame shape: {frame.shape if 'frame' in locals() else 'unknown'}")
        
        # Define the queue name used by the event notification service
        queue_name = "processed_events_queue_stream"
        print(f"Using queue name: {queue_name}")
        logger.debug(f"Using queue name: {queue_name}")
        
        # Save to the database first using our db_connection
        db_event_id = None
        try:
            print("Saving event to database...")
            logger.info("Saving event to database...")
            
            # Create a copy of alert_data to avoid modifying the original
            db_data = alert_data.copy()
            
            # Add bounding boxes if they exist as a single bounding box
            if 'bounding_box' in db_data:
                db_data['bounding_boxes'] = [db_data['bounding_box']]
                print("Added bounding box to bounding_boxes list")
                logger.debug("Added bounding box to bounding_boxes list")
                
            # Set the id field if provided in alert_data
            db_data['id'] = alert_data['id']
            
            # Get database connection
            db = get_db_connection()
            print("Got database connection")
            logger.debug("Got database connection")
            
            # Save to database
            success = db.create_camera_event(db_data)
            if success:
                db_event_id = str(alert_data['id'])
                print(f"Successfully saved event to database with ID: {db_event_id}")
                logger.info(f"Successfully saved event to database with ID: {db_event_id}")
            else:
                print("Failed to save event to database")
                logger.error("Failed to save event to database")
        except Exception as e:
            print(f"Error saving event to database: {e}")
            print(traceback.format_exc())
            logger.error(f"Error saving event to database: {e}")
            print(f"Database data that failed: {db_data}")
            logger.debug(f"Database data that failed: {db_data}")
            # Continue with Redis queue even if DB save fails
        
        # Only send the ID and minimal information to Redis queue
        queue_data = {
            'id': alert_data['id'],
            'event_type': alert_data.get('event_type', 'custom'),
            'event_severity': alert_data.get('event_severity', 'Warning'),
            'timestamp': datetime.now().isoformat(),
            'db_saved': str(db_event_id is not None)
        }
        print(f"Prepared queue data: {queue_data}")
        logger.debug(f"Prepared queue data: {queue_data}")
        
        # Push only the event ID to Redis (frontend will fetch details from DB)
        if redis_client:
            try:
                # First add the new entry
                entry_id = redis_client._redis.xadd(queue_name, fields=queue_data)
                print(f"Event ID pushed to queue '{queue_name}': {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
                logger.info(f"Event ID pushed to queue '{queue_name}': {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
                
                # Then trim old entries (keep last 3 seconds)
                ttl_sec = 3  # retain 3 s of history
                cutoff_ms = int((time.time() - ttl_sec) * 1000)  # ID is ms-since-epoch
                cutoff_id = f"{cutoff_ms}-0"
                redis_client._redis.xtrim(queue_name, minid=cutoff_id)
                
                return True
            except Exception as e:
                print(f"Error pushing event ID to Redis queue: {e}")
                print(traceback.format_exc())
                logger.error(f"Error pushing event ID to Redis queue: {e}")
                print(f"Failed Redis push data: {queue_data}")
                logger.debug(f"Failed Redis push data: {queue_data}")
                return False
        else:
            # If no Redis client available, log the alert
            print("No Redis client available, event ID not sent to queue")
            logger.warning("No Redis client available, event ID not sent to queue")
            print(f"Event ID would have been sent: {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
            logger.info(f"Event ID would have been sent: {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
            return False
    except Exception as e:
        traceback.print_exc()
        print(f"Unexpected error in send_alerts: {e}")
        print(traceback.format_exc())
        logger.error(f"Unexpected error in send_alerts: {e}")
        print(f"Full alert_data at time of error: {alert_data}")
        logger.debug(f"Full alert_data at time of error: {alert_data}")
        return False