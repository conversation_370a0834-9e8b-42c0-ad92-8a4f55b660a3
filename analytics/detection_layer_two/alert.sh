#!/bin/bash

# Set environment variables
export DB_NAME=squirrelsentry_db
export DB_USER=squirrelsentry_user
export DB_PASSWORD=password
export DB_HOST=localhost
export DB_PORT=5432

export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_DB=0
export REDIS_PASS=squirrel_sentinel_2025

# Install required packages
# pip install psycopg2-binary python-dotenv opencv-python python-dotenv numpy redis torch

# Run the test script with environment variables passed explicitly
env DB_NAME=$DB_NAME \
    DB_USER=$DB_USER \
    DB_PASSWORD=$DB_PASSWORD \
    DB_HOST=$DB_HOST \
    DB_PORT=$DB_PORT \
    REDIS_HOST=$REDIS_HOST \
    REDIS_PORT=$REDIS_PORT \
    REDIS_DB=$REDIS_DB \
    REDIS_PASS=$REDIS_PASS \
    python test_send_alerts.py