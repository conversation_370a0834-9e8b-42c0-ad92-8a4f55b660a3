#!/usr/bin/env python
"""
Camera-to-Floorplan Homography Calibration Tool

This tool allows calibrating camera views to a floorplan by selecting corresponding points
between the camera image and floorplan. It generates homography matrices that can be used
for accurate mapping of camera coordinates to floorplan coordinates.

How to use:
1. Configure camera and floorplan paths below
2. Run the script
3. Left-click to place calibration points on both camera and floorplan views
4. Right-click to reset views
5. Press 'q' to exit and save calibration data
"""

import os
import cv2
import numpy as np
import json
from pathlib import Path
import argparse

# Constants for window names
CAM_WINDOW = "Camera View"
FLOOR_WINDOW = "Floorplan View"

# Global variables for mouse interaction
cam_points = []
floor_points = []
cam_img = None
flr_map = None
cam_img_orig = None
flr_map_orig = None

# Output paths
BASE_DIR = Path(__file__).parent.parent  # This is now pointing to detection_layer_two
CALIB_DIR = BASE_DIR / "calibration"
CAMERA_CONFIG_DIR = CALIB_DIR / "camera_configs"
FLOORPLAN_CONFIG_DIR = CALIB_DIR / "floorplan_config"
HOMOGRAPHY_DIR = CALIB_DIR / "homography_matrices"


def ensure_dirs_exist():
    """Ensure all necessary directories exist"""
    os.makedirs(CAMERA_CONFIG_DIR, exist_ok=True)
    os.makedirs(FLOORPLAN_CONFIG_DIR, exist_ok=True)
    os.makedirs(HOMOGRAPHY_DIR, exist_ok=True)


def get_normalized_coordinates(point, image):
    """Calculate normalized coordinates (0-1 range) for a point on an image"""
    x, y = point
    h, w = image.shape[:2]
    return float(x) / w, float(y) / h


def load_config(filepath):
    """Load configuration from JSON file"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return None


def save_config(config, filepath):
    """Save configuration to JSON file"""
    with open(filepath, 'w') as f:
        json.dump(config, f, indent=2)
    print(f"Saved configuration to {filepath}")


def mouse_callback_camera(event, x, y, flags, param):
    """Handle mouse events on camera window"""
    global cam_points, cam_img, cam_img_orig

    if event == cv2.EVENT_LBUTTONDOWN:
        # Add point
        cam_points.append((x, y))
        # Draw point
        cam_img = cam_img_orig.copy()
        for idx, pt in enumerate(cam_points):
            cv2.circle(cam_img, pt, 5, (0, 0, 255), -1)
            cv2.putText(cam_img, f"{idx+1}", (pt[0]+10, pt[1]-10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.imshow(CAM_WINDOW, cam_img)

    elif event == cv2.EVENT_RBUTTONDOWN:
        # Reset
        cam_points.clear()
        cam_img = cam_img_orig.copy()
        cv2.imshow(CAM_WINDOW, cam_img)


def mouse_callback_floorplan(event, x, y, flags, param):
    """Handle mouse events on floorplan window"""
    global floor_points, flr_map, flr_map_orig

    if event == cv2.EVENT_LBUTTONDOWN:
        # Add point
        floor_points.append((x, y))
        # Draw point
        flr_map = flr_map_orig.copy()
        for idx, pt in enumerate(floor_points):
            cv2.circle(flr_map, pt, 5, (0, 255, 0), -1)
            cv2.putText(flr_map, f"{idx+1}", (pt[0]+10, pt[1]-10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.imshow(FLOOR_WINDOW, flr_map)
        
        # Print normalized coordinates for frontend use
        norm_x, norm_y = get_normalized_coordinates((x, y), flr_map)
        print(f"Floorplan normalized coordinates: ({norm_x:.4f}, {norm_y:.4f})")

    elif event == cv2.EVENT_RBUTTONDOWN:
        # Reset
        floor_points.clear()
        flr_map = flr_map_orig.copy()
        cv2.imshow(FLOOR_WINDOW, flr_map)


def compute_homography_matrices():
    """Compute homography matrices for mapping between camera and floorplan"""
    # For this implementation, we use an intermediate abstract plane with unit square coordinates
    # Camera → abstract plane → floorplan
    
    # Step 1: Define abstract plane points (0-1 range square)
    # Order must match cam_points
    abstract_plane = []
    for i, _ in enumerate(cam_points):
        u = i / (len(cam_points) - 1) if len(cam_points) > 1 else 0.5
        v = 0.5
        abstract_plane.append((u, v))
    
    abstract_plane_np = np.float32(abstract_plane)
    
    # Step 2: Camera to abstract plane
    cam_points_np = np.float32(cam_points)
    H_cam2plane, _ = cv2.findHomography(cam_points_np, abstract_plane_np)
    
    # Step 3: Abstract plane to floorplan
    floor_points_np = np.float32(floor_points)
    H_plane2floor, _ = cv2.findHomography(abstract_plane_np, floor_points_np)
    
    return H_cam2plane, H_plane2floor


def save_matrices(camera_key, H_cam2plane, H_plane2floor):
    """Save homography matrices for future use"""
    # Save as numpy files
    np.save(HOMOGRAPHY_DIR / f"{camera_key}_H_cam2plane.npy", H_cam2plane)
    np.save(HOMOGRAPHY_DIR / f"{camera_key}_H_plane2floor.npy", H_plane2floor)
    print(f"Saved homography matrices for camera {camera_key}")


def main():
    """Main calibration function"""
    parser = argparse.ArgumentParser(description='Camera to Floorplan Calibration Tool')
    parser.add_argument('--camera', '-c', required=True, help='Camera key (e.g., CAM_01)')
    parser.add_argument('--camera-image', '-ci', required=True, help='Path to camera image')
    parser.add_argument('--floorplan', '-f', required=True, help='Path to floorplan image')
    args = parser.parse_args()

    global cam_img, flr_map, cam_img_orig, flr_map_orig

    # Ensure directories exist
    ensure_dirs_exist()

    # Camera key and paths
    CAMERA_KEY = args.camera
    CAM_IMAGE = args.camera_image
    FLOORPLAN_IMG = args.floorplan

    # Check if files exist
    if not os.path.exists(CAM_IMAGE):
        print(f"Error: Camera image not found at {CAM_IMAGE}")
        return
    if not os.path.exists(FLOORPLAN_IMG):
        print(f"Error: Floorplan image not found at {FLOORPLAN_IMG}")
        return

    # Load camera image and floorplan
    cam_img = cv2.imread(CAM_IMAGE)
    flr_map = cv2.imread(FLOORPLAN_IMG)
    
    # Store originals
    cam_img_orig = cam_img.copy()
    flr_map_orig = flr_map.copy()

    # Update global floorplan dimensions
    global FLOORPLAN_WIDTH, FLOORPLAN_HEIGHT
    FLOORPLAN_HEIGHT, FLOORPLAN_WIDTH = flr_map.shape[:2]

    # Create windows and set mouse callbacks
    cv2.namedWindow(CAM_WINDOW)
    cv2.namedWindow(FLOOR_WINDOW)
    cv2.setMouseCallback(CAM_WINDOW, mouse_callback_camera)
    cv2.setMouseCallback(FLOOR_WINDOW, mouse_callback_floorplan)

    # Load previous configurations if they exist
    cam_config_path = CAMERA_CONFIG_DIR / f"{CAMERA_KEY}_camera_config.json"
    floor_config_path = FLOORPLAN_CONFIG_DIR / "floorplan_config.json"

    cam_config = load_config(cam_config_path)
    floor_config = load_config(floor_config_path)

    # Initialize points from saved configurations if available
    if cam_config and "control_points" in cam_config:
        cam_points.extend(cam_config["control_points"])
        for idx, pt in enumerate(cam_points):
            cv2.circle(cam_img, pt, 5, (0, 0, 255), -1)
            cv2.putText(cam_img, f"{idx+1}", (pt[0]+10, pt[1]-10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

    if floor_config and "control_points" in floor_config:
        floor_points.extend(floor_config["control_points"])
        for idx, pt in enumerate(floor_points):
            cv2.circle(flr_map, pt, 5, (0, 255, 0), -1)
            cv2.putText(flr_map, f"{idx+1}", (pt[0]+10, pt[1]-10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # Show initial images
    cv2.imshow(CAM_WINDOW, cam_img)
    cv2.imshow(FLOOR_WINDOW, flr_map)

    print("Mapping: left-click on camera to project to floorplan; right-click to reset; 'q' to quit.")
    
    # Main loop
    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break

    # Save control points
    if len(cam_points) > 0:
        save_config({
            "camera_key": CAMERA_KEY,
            "image_path": CAM_IMAGE,
            "control_points": cam_points
        }, cam_config_path)

    if len(floor_points) > 0:
        save_config({
            "image_path": FLOORPLAN_IMG,
            "control_points": floor_points,
            "width": FLOORPLAN_WIDTH,
            "height": FLOORPLAN_HEIGHT
        }, floor_config_path)

    # Compute and save homography matrices if enough points are available
    if len(cam_points) >= 4 and len(cam_points) == len(floor_points):
        H_cam2plane, H_plane2floor = compute_homography_matrices()
        save_matrices(CAMERA_KEY, H_cam2plane, H_plane2floor)
        print("Saved homography matrices for frontend use")
    else:
        print("Warning: Not enough corresponding points to compute homography matrices")
        print(f"  Camera points: {len(cam_points)}")
        print(f"  Floorplan points: {len(floor_points)}")

    # Clean up
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()
