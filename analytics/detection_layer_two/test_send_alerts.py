#!/usr/bin/env python
import os
import sys
import time
import argparse
import uuid
import logging
import numpy as np
import cv2
from datetime import datetime
from dotenv import load_dotenv
import random
import base64

# Add the parent directory to sys.path to import local modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import the modules we want to test
from send_alerts import send_alerts
from db_connection import DatabaseConnection
from redis_connection import CameraCache

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class AlertTester:
    """
    Test utility to simulate alerts for the send_alerts function.
    Tests the database saving and Redis queue integration.
    """

    def __init__(self):
        """Initialize the test environment"""
        # Connect to Redis
        self.redis_client = CameraCache()

        # Connect to database
        self.db_conn = DatabaseConnection()

        # Cache the first camera layer configuration ID
        self.first_layer_config_id = self._get_first_camera_layer_config_id()
        if self.first_layer_config_id:
            logger.info(f"Using camera layer config ID: {self.first_layer_config_id}")
        else:
            logger.warning("No camera layer configurations found in database")

        logger.info("Alert tester initialized")

    def _get_first_camera_layer_config_id(self):
        """Get the first available camera layer configuration ID from the database"""
        if not self.db_conn.conn:
            logger.error("No database connection available")
            return None

        try:
            # Query the CamerasLayersConfiguration table directly to get its ID (primary key)
            cur = self.db_conn.conn.cursor()
            cur.execute(
                """
                SELECT id, camera_id, layers_id, name FROM cameras_cameraslayersconfiguration
                LIMIT 1
            """
            )
            config = cur.fetchone()

            if config and len(config) >= 1:
                config_id = config[0]  # First column is the ID
                logger.info(
                    f"Found camera layer configuration: ID {config_id}, Camera ID: {config[1]}, Layer ID: {config[2]}, Name: {config[3]}"
                )
                return str(config_id)  # Convert UUID to string if needed
            else:
                logger.warning("No camera layer configurations found in database")
                return None

        except Exception as e:
            logger.error(f"Error fetching camera layer configurations: {str(e)}")
            return None

    def generate_test_image(
        self, severity="Warning", width=640, height=480, use_sample_image=True
    ):
        """
        Generate a test image for the alert using OpenCV or use a sample image

        Args:
            severity: Alert severity (Warning or Critical)
            width: Image width
            height: Image height
            use_sample_image: Use the sample suspicious_person.jpg instead of generating one

        Returns:
            bytes: JPEG image bytes
        """
        # Check if we should use the sample image
        if use_sample_image:
            try:
                # Path to the sample image
                sample_image_path = os.path.join(
                    os.path.dirname(os.path.abspath(__file__)),
                    "sample_alert_image",
                    "suspicious_person.jpg",
                )

                # Check if file exists
                if os.path.exists(sample_image_path):
                    logger.info(f"Using sample image from {sample_image_path}")

                    # Read the image file
                    with open(sample_image_path, "rb") as img_file:
                        return img_file.read()
                else:
                    logger.warning(
                        f"Sample image not found at {sample_image_path}, generating image instead"
                    )
            except Exception as e:
                logger.error(
                    f"Error loading sample image: {e}, generating image instead"
                )

        # If we get here, either use_sample_image is False or we couldn't load the sample image
        # Generate a blank black image
        img = np.zeros((height, width, 3), dtype=np.uint8)

        # Set box color based on severity (BGR format for OpenCV)
        color = (
            (0, 0, 255) if severity == "Critical" else (0, 165, 255)
        )  # Red for Critical, Orange for Warning

        # Draw a colored box in the center
        box_width, box_height = width // 3, height // 3
        x1 = (width - box_width) // 2
        y1 = (height - box_height) // 2
        cv2.rectangle(
            img, (x1, y1), (x1 + box_width, y1 + box_height), color, -1
        )  # -1 fills the rectangle

        # Add text
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(
            img,
            f"Test Alert - {severity}",
            (20, 40),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2,
        )
        cv2.putText(
            img,
            f"Time: {timestamp}",
            (20, 70),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2,
        )

        # Convert to JPEG bytes
        _, img_bytes = cv2.imencode(".jpg", img)
        return img_bytes.tobytes()

    def create_test_alert(
        self,
        event_type="suspicious person",
        severity="critical",
        confidence=0.85,
        include_image=True,
        generate_id=True,
        use_sample_image=True,
    ):
        """
        Create test alert data for the send_alerts function

        Args:
            event_type: Type of event (default: suspicious person)
            severity: Event severity (Warning or Critical)
            confidence: Detection confidence (0-1)
            include_image: Whether to include a test image
            generate_id: Whether to generate a new UUID for the event

        Returns:
            dict: Alert data dictionary
        """
        # Generate a unique ID for the event
        event_id = (
            str(uuid.uuid4())
            if generate_id
            else f"test-{int(time.time())}-{random.randint(1000, 9999)}"
        )

        # Use the first available camera layer config ID from database
        # Fall back to a default layer ID only if no configurations found
        if self.first_layer_config_id:
            layer_id = self.first_layer_config_id
        else:
            # Fallback ID in case database is not available
            logger.warning("Using fallback camera layer config ID")
            layer_id = "f799787c-259f-4e92-a866-bdb205f9f02b"

        # Current timestamp in ISO format
        current_time = datetime.now()
        timestamp = current_time.isoformat()

        # Create the alert data with all necessary fields
        alert_data = {
            "id": event_id,
            "camera_layer_config_id": layer_id,  # Using the layers_id as the config ID
            "event_type": event_type,
            "event_severity": severity,
            "confidence": confidence,
            "is_reviewed": False,
            "is_suspicious": True,
            "bounding_boxes": [
                {
                    "x1": 717,
                    "y1": 310,
                    "x2": 850,
                    "y2": 528,
                    "class": event_type,
                    "confidence": confidence,
                }
            ],
            "bounding_box": {
                "x1": 717,
                "y1": 310,
                "x2": 850,
                "y2": 528,
                "class": event_type,
                "confidence": confidence,
            },
            "timestamp": timestamp,
            "frame_time": timestamp,
            "created_at": timestamp,  # Adding created_at field
        }

        # Add image if requested
        if include_image:
            img_bytes = self.generate_test_image(
                severity, use_sample_image=use_sample_image
            )
            alert_data["frame_bytes"] = img_bytes

        return alert_data

    def test_send_alert(
        self,
        event_type="suspicious person",
        severity="Critical",
        confidence=0.85,
        include_image=True,
        use_sample_image=True,
    ):
        """
        Test the send_alerts function with generated test data

        Args:
            event_type: Type of event (default: suspicious person)
            severity: Event severity (Warning or Critical)
            confidence: Detection confidence (0-1)
            include_image: Whether to include a test image

        Returns:
            bool: True if the alert was sent successfully
        """
        # Create test alert data
        alert_data = self.create_test_alert(
            event_type=event_type,
            severity=severity,
            confidence=confidence,
            include_image=include_image,
            use_sample_image=use_sample_image,
        )

        if not alert_data:
            return False

        logger.info(f"Testing send_alerts with alert ID: {alert_data['id']}")
        logger.info(f"Alert type: {event_type}, Severity: {severity}")

        # Call the send_alerts function
        result = send_alerts(alert_data=alert_data, redis_client=self.redis_client)

        if result:
            logger.info("Alert sent successfully!")
        else:
            logger.error("Failed to send alert")

        return result


def main():
    """Main entry point for the test script"""
    parser = argparse.ArgumentParser(description="Test send_alerts function")

    # Event configuration
    parser.add_argument(
        "--type",
        type=str,
        default="suspicious person",
        choices=[
            "coveredPerson",
            "weaponDetection",
            "oversizeObject",
            "unattendedObject",
            "areaBreach",
            "custom",
        ],
        help="Type of event to publish",
    )
    parser.add_argument(
        "--severity",
        type=str,
        default="Critical",
        choices=["Warning", "Critical"],
        help="Severity level of the event",
    )
    parser.add_argument(
        "--confidence", type=float, default=0.85, help="Detection confidence (0-1)"
    )
    parser.add_argument(
        "--no-image",
        action="store_true",
        help="Don't include a test image with the alert",
    )
    parser.add_argument(
        "--no-sample-image",
        action="store_true",
        help="Don't use the sample image (generate an image instead)",
    )
    parser.add_argument(
        "--count", type=int, default=1, help="Number of test alerts to send"
    )
    parser.add_argument(
        "--delay",
        type=float,
        default=0.5,
        help="Delay between sending multiple alerts (seconds)",
    )

    args = parser.parse_args()

    # Create tester and send test alert(s)
    tester = AlertTester()

    successes = 0
    for i in range(args.count):
        if args.count > 1:
            logger.info(f"Sending test alert {i+1}/{args.count}")

        result = tester.test_send_alert(
            event_type=args.type,
            severity=args.severity,
            confidence=args.confidence,
            include_image=not args.no_image,
            use_sample_image=not args.no_sample_image,
        )

        if result:
            successes += 1

        if i < args.count - 1 and args.delay > 0:
            time.sleep(args.delay)

    # Report results
    if args.count > 1:
        logger.info(f"Test complete: {successes}/{args.count} alerts sent successfully")

    # Exit with appropriate status code
    sys.exit(0 if successes == args.count else 1)


if __name__ == "__main__":
    main()
