from dotenv import load_dotenv

load_dotenv()

import json
import logging
import time
from typing import Dict
from datetime import datetime
from itertools import product

import cv2
import numpy as np
import pandas as pd
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from homography_mapping.clustering_utils import cluster_df
import pytz
from homography_mapping.homography import project_to_floor, inside_rect
from logger_setup import setup_logging
from redis_connection import Camera<PERSON><PERSON>
from send_alerts import send_alerts
from collections import defaultdict
import os
from alerts_checking import check_image_similarity

redis_client = CameraCache()

setup_logging()
logger = logging.getLogger(__name__)

# Category mapping for alert types
CATEGORY = {
    "weapon": "CR2 - Weapon",
    "oversized_object": "CR3 - Oversized object detected",
    "scaling_gantry": "CR6 - The person is the scaling gantry",
    "unattended_object": "CR4 - Unattended object detected",
    "suspicious_person": "CR1 - Suspicious person detected",
    "unknown": "Unknown alert",
}


decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
L2_RULES = decrypted_config["L2_RULES"]
AREA_W = decrypted_config["L2_ALERT_CONFIG"]["AREA_W"]
AREA_H = decrypted_config["L2_ALERT_CONFIG"]["AREA_H"]
GRID_W = decrypted_config["L2_ALERT_CONFIG"]["GRID_W"]
GRID_H = decrypted_config["L2_ALERT_CONFIG"]["GRID_H"]
CELL_W, CELL_H = AREA_W / GRID_W, AREA_H / GRID_H
TOLERANCE = decrypted_config["L2_ALERT_CONFIG"]["TOLERANCE"]
TTL_SEC = decrypted_config["L2_ALERT_CONFIG"]["TTL_SEC"]
SCALING_GANTRY_PAUSE_SEC = decrypted_config["L2_ALERT_CONFIG"]["SCALING_GANTRY_PAUSE_SEC"]


# Initialize camera configs
CAMERA_CONFIGS = {}
config_dir = "calibration/camera_configs"
for config_file in os.listdir(config_dir):
    if config_file.endswith('_camera_config.json'):
        with open(os.path.join(config_dir, config_file)) as f:
            try:
                config_data = json.load(f)
                CAMERA_CONFIGS[config_file] = config_data
            except json.JSONDecodeError as e:
                logger.error(f"Error loading camera config {config_file}: {e}")
print(CAMERA_CONFIGS)


def bucket_xy(x: float, y: float) -> tuple[int, int]:
    bx, by = int(x // CELL_W), int(y // CELL_H)
    return max(0, min(bx, GRID_W - 1)), max(0, min(by, GRID_H - 1))


def in_tol(a: Dict, b: Dict) -> bool:
    dx, dy = a["groundx"] - b["groundx"], a["groundy"] - b["groundy"]
    return dx * dx + dy * dy <= TOLERANCE * TOLERANCE


def should_send(event: Dict, redis_client: CameraCache, event_timestamp: pd.Timestamp) -> bool:
    bx, by = bucket_xy(event["groundx"], event["groundy"])
    kpref = f"l2:sent:{event['event_type']}"
    # neighbourhood keys (3×3)
    keys = [
        f"{kpref}:{bx+dx}:{by+dy}"
        for dx, dy in product((-1, 0, 1), repeat=2)
        if 0 <= bx + dx < GRID_W and 0 <= by + dy < GRID_H
    ]

    # single round trip
    for raw in filter(None, redis_client._redis.mget(keys)):
        if in_tol(json.loads(raw), event):
            return False  # duplicate
        
    # To pause on the scaling alerts for 30 seconds
    # Get the last scaling gantry alert
    if event["event_type"] == "scaling_gantry":
        last_scaling_gantry_alert = redis_client._redis.get(f"l2:sent:scaling_gantry")
        if last_scaling_gantry_alert:
            last_scaling_gantry_alert = json.loads(last_scaling_gantry_alert)
            # Check if the last scaling gantry alert is within 30 seconds
            # Convert the timestamp to a datetime object
            last_scaling_gantry_alert_timestamp = pd.Timestamp(last_scaling_gantry_alert)
            if (event_timestamp - last_scaling_gantry_alert_timestamp).total_seconds() < SCALING_GANTRY_PAUSE_SEC:
                return False
            else:
                # Update the last scaling gantry alert
                # Convert the frame_time to str with milliseconds
                frame_time = event_timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")
                redis_client._redis.set(f"l2:sent:scaling_gantry", frame_time)
                return True

    # unique – store & allow
    if event["event_type"] == "unattended_object":
        redis_client._redis.setex(f"{kpref}:{bx}:{by}", TTL_SEC, json.dumps(event))
    else:
        redis_client._redis.setex(f"{kpref}:{bx}:{by}", 5, json.dumps(event))
    return True


# ── tiny test-rig ────────────────────────────────────────────────────────────
def make_event(eid: int, etype: str, x: float, y: float, cam_id: str) -> Dict:
    return {
        "event_id": f"E{eid}",
        "event_type": etype,
        "groundx": x,
        "groundy": y,
        "cameras": [cam_id],
        "frames": [f"frame{eid}"],
    }


def run_l2_promotion_with_fk(
    df_l1, current_time, rules=L2_RULES, window=10.0, starting_group_id=0
):
    """
    Returns df_l2 with the same schema as df_l1, tagging L2-promoted rows.
    Keeps FK link via 'event_id'.
    """

    grouped = defaultdict(list)
    promoted_l2_records = []
    group_id_counter = starting_group_id

    # Check if current_time is a pandas Timestamp and convert appropriately
    if isinstance(current_time, pd.Timestamp):
        current_time_ts = current_time
    else:
        current_time_ts = pd.Timestamp(current_time)

    for _, det in df_l1.iterrows():
        # Make sure timestamp is a pandas Timestamp for comparison
        if not isinstance(det["timestamp"], pd.Timestamp):
            timestamp = pd.Timestamp(det["timestamp"])
        else:
            timestamp = det["timestamp"]

        # Compare timestamps directly (pandas handles the timedelta comparison)
        if (current_time_ts - timestamp).total_seconds() <= window:
            grouped[det["category"]].append(det)

    # print(f"Grouped: {grouped}")

    for category, detections in grouped.items():
        print(f"Category: {category}")
        rule = rules.get(category)
        print(rule)
        if not rule:
            continue

        high_conf_dets = [
            d for d in detections if d["confidence"] >= rule["min_confidence"]
        ]
        cams = {d["cam_id"] for d in high_conf_dets}

        print(f"High conf dets: {len(high_conf_dets)}")
        print(f"Cams: {cams}")
        print(f"Rule: {rule}")
        print(f"Min cams: {rule['min_cams']}")
        print(f"Min frames: {rule['min_frames']}")
        print(f"Len cams: {len(cams)}")
        print(f"Len high conf dets: {len(high_conf_dets)}")

        if len(cams) >= rule["min_cams"] and len(high_conf_dets) >= rule["min_frames"]:
            for det in high_conf_dets:
                promoted_l2_records.append(
                    {
                        "event_id": det["event_id"],  # reuse L1 event ID
                        "timestamp": det["timestamp"],
                        "frame_time": det["frame_time"],
                        "cam_id": det["cam_id"],
                        "category": det["category"],
                        "bbox": det["bbox"],
                        "ground_x": det["ground_x"],
                        "ground_y": det["ground_y"],
                        "confidence": det["confidence"],
                        "is_primary": True,
                        "group_id": group_id_counter,
                        "alert_type": "L2",
                    }
                )
            group_id_counter += 1

    df_l2 = pd.DataFrame(promoted_l2_records)
    return df_l2


def get_last_5min(redis_client: CameraCache):
    now = datetime.now(pytz.timezone('Asia/Singapore')).timestamp()
    
    # Get all camera-zset keys
    try:
        zkeys = [k for k in redis_client._redis.scan_iter(match="events:layer_one:cam:*")]
        logger.info(f"Found {len(zkeys)} camera keys: {zkeys}")
    except Exception as e:
        logger.error(f"Error scanning Redis keys: {e}")
        return []

    # Pipeline ZRANGEBYSCORE for each key
    try:
        pipe = redis_client._redis.pipeline()
        for z in zkeys:
            pipe.zrangebyscore(z, now - 300.0, now)  # Get last 5 minutes of data initially
        results = pipe.execute()
        logger.info(f"Retrieved {len(results)} result sets from Redis pipeline")
    except Exception as e:
        logger.error(f"Error executing Redis pipeline: {e}")
        return []

    rows = []
    try:
        for i, payloads in enumerate(results):
            logger.debug(f"Processing result set {i} with {len(payloads)} payloads")
            for p in payloads:
                try:
                    parsed = json.loads(p)
                    # Filter based on category and time
                    event_time = pd.Timestamp(parsed.get('frame_time')).tz_localize('Asia/Singapore')
                    current_time = pd.Timestamp.now(tz='Asia/Singapore')
                    time_diff = (current_time - event_time).total_seconds()
                    
                    # Only keep events that are:
                    # - Within last 5 seconds for non-unattended objects
                    # - Within last 5 minutes for unattended objects
                    if (parsed.get('category') == 'unattended_object' and time_diff <= 300.0) or \
                       (parsed.get('category') != 'unattended_object' and time_diff <= 5.0):
                        rows.append(parsed)
                    else:
                        print(f"Skipping event {parsed.get('event_id')} for {parsed.get('category')} because it is not within the time window")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON payload: {e}")
                    continue
        logger.info(f"Successfully parsed {len(rows)} events")
    except Exception as e:
        logger.error(f"Error processing results: {e}")
        return []

    return rows


def compute_ground_coords(df):
    """
    Map bounding boxes from image space to ground plane coordinates.

    Args:
        df (DataFrame): DataFrame containing detections with bbox and cam_id columns

    Returns:
        DataFrame: Updated DataFrame with ground_x and ground_y columns
    """
    ground_x_list = []
    ground_y_list = []

    cam_mapping = {
        "cam_one": "cam01",
        "cam_two": "cam02",
        "cam_three": "cam03",
        "cam_four": "cam04",
        "cam_five": "cam05",
        "cam_six": "cam06",
        "cam_seven": "cam07",
        "cam_eight": "cam08",
        "cam_nine": "cam09",
        "video_one.mp4": "cam01",
        "video_two.mp4": "cam02",
        "video_three.mp4": "cam03",
        "video_four.mp4": "cam04",
        "video_five.mp4": "cam05",
        "video_eight.mp4": "cam08",
        "video_nine.mp4": "cam09",
    }

    for _, row in df.iterrows():
        cam_id = cam_mapping.get(row["cam_id"], row["cam_id"])
        # Convert cam_id format if needed (e.g., cam_01 -> cam01)
        if cam_id.startswith("cam_"):
            cam_id = f"cam{cam_id[4:]}"  # Convert cam_01 to cam01

        bbox = row["original_bbox"]
        try:
            # Need to get the center of the bbox
            # Use bottom-center point: center of x coordinates and bottom y coordinate
            bbox_center = ((bbox[0] + bbox[2]) / 2, bbox[3])  # bbox[3] is bottom y coordinate
            # Project the bbox center to the floor
            bbox_center_floor = project_to_floor(cam_id, bbox_center)
            # Get the ground x and y coordinates
            ground_x = bbox_center_floor[0]
            ground_y = bbox_center_floor[1]

            ground_x_list.append(ground_x)
            ground_y_list.append(ground_y)

        except Exception as e:
            ground_x_list.append(None)
            ground_y_list.append(None)
            print(f"⚠️ Failed to map coordinates for cam {cam_id}: {e}")

    df["ground_x"] = ground_x_list
    df["ground_y"] = ground_y_list
    return df


def get_event_payload(alerts_redis_client, redis_client, event_id):
    """
    Retrieve the full event payload from Redis for a given event ID.

    Args:
        alerts_redis_client: Alerts Redis client instance
        redis_client: Redis client instance
        event_id: The event ID to look up

    Returns:
        tuple: (event_data, frame_bytes) if found, (None, None) otherwise
    """
    try:
        # Construct the key for this event
        event_key = f"event:{event_id}"
        logger.info(f"Looking up event with key: {event_key}")

        # Get the payload from Redis
        data = alerts_redis_client.get(event_key)
        logger.info(f"Retrieved payload from Redis: {data is not None}")

        if data:
            logger.info(f"Successfully retrieved data: {data is not None}")

            if data:
                # Get the frame using frame id
                frame_id = data["frame_id"]
                logger.info(f"Getting frame bytes for frame_id: {frame_id}")
                original_frame_key = frame_id.replace("_final_result", "")
                frame_payload = redis_client.get(original_frame_key)
                logger.info(f"Retrieved frame payload: {frame_payload is not None}")

                if frame_payload and "frame" in frame_payload:
                    # Extract and decode the frame bytes from base64
                    frame_bytes = redis_client.from_jsonable_bytes(
                        frame_payload["frame"]
                    )
                    logger.info(f"Retrieved frame bytes: {frame_bytes is not None}")
                    return data, frame_bytes
                else:
                    logger.warning(
                        f"No frame data found in payload for frame_id: {frame_id}"
                    )
                    return data, None

            else:
                logger.warning(f"No data found in payload for event {event_id}")
                return None, None

        logger.warning(f"No payload found in Redis for event {event_id}")
        return None, None

    except Exception as e:
        logger.error(f"Error retrieving event payload for {event_id}: {e}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None


def draw_box_and_text(image_bytes, bbox, category):
    """
    Draw bounding box and category text on image.

    Args:
        image_bytes (bytes): Raw image bytes
        bbox (list): Bounding box coordinates [x1, y1, x2, y2]
        category (str): Category text to display

    Returns:
        bytes: Image bytes with box and text drawn
    """
    try:
        # Convert image bytes to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # Draw bounding box
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2)

        # Draw text with background
        text = category
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(
            text, font, font_scale, thickness
        )

        # Draw background rectangle for text
        cv2.rectangle(
            img,
            (x1, y1 - text_height - 10),
            (x1 + text_width + 10, y1),
            (0, 0, 255),
            -1,
        )

        # Draw text
        cv2.putText(
            img, text, (x1 + 5, y1 - 5), font, font_scale, (255, 255, 255), thickness
        )

        # Encode back to bytes
        _, img_encoded = cv2.imencode(".jpg", img)
        return img_encoded.tobytes()

    except Exception as e:
        logger.error(f"Error drawing box and text: {e}")
        return image_bytes


def get_grid_boundaries(camera_configs):
    """
    Determine grid boundaries from camera configurations.
    Returns min/max x and y coordinates to create grid.
    """
    all_points = []
    for cam_id, points in camera_configs.items():
        # Each points array is already a list of [x,y] coordinates from JSON
        all_points.extend(points)
    
    # Convert string coordinates to integers if needed
    x_coords = []
    y_coords = []
    for point in all_points:
        if isinstance(point, list) and len(point) == 2:
            x_coords.append(int(point[0]))
            y_coords.append(int(point[1]))
    
    if not x_coords or not y_coords:
        logger.error("No valid coordinates found in camera configs")
        return {'min_x': 0, 'max_x': 1000, 'min_y': 0, 'max_y': 1000}  # Default fallback
    
    return {
        'min_x': min(x_coords),
        'max_x': max(x_coords),
        'min_y': min(y_coords),
        'max_y': max(y_coords)
    }

def assign_grid_cell(x, y, boundaries, grid_size=20):
    """
    Assign a ground coordinate to a grid cell.
    Returns (grid_x, grid_y) tuple.
    """
    cell_width = (boundaries['max_x'] - boundaries['min_x']) / grid_size
    cell_height = (boundaries['max_y'] - boundaries['min_y']) / grid_size
    
    grid_x = int((x - boundaries['min_x']) / cell_width)
    grid_y = int((y - boundaries['min_y']) / cell_height)
    
    # Clamp to valid grid range
    grid_x = max(0, min(grid_x, grid_size - 1))
    grid_y = max(0, min(grid_y, grid_size - 1))
    
    return (grid_x, grid_y)

def are_adjacent_cells(cell1, cell2):
    """
    Check if two grid cells are adjacent (including diagonally).
    cell1 and cell2 are tuples of (x, y) coordinates.
    Returns True if cells share an edge or corner.
    """
    # Convert string representation to tuple if needed
    if isinstance(cell1, str):
        cell1 = eval(cell1)
    if isinstance(cell2, str):
        cell2 = eval(cell2)
        
    x1, y1 = cell1
    x2, y2 = cell2
    
    # Cells are adjacent if their x and y coordinates differ by at most 1
    # This includes:
    # - Same cell (0,0)
    # - Sharing edge: (0,1), (1,0), (0,-1), (-1,0)
    # - Sharing corner: (1,1), (1,-1), (-1,1), (-1,-1)
    return abs(x1 - x2) <= 1 and abs(y1 - y2) <= 1

def group_l2_alerts(df_l2, camera_configs):
    """
    Group L2 alerts based on grid proximity, category, and time window.
    Returns DataFrame with group assignments, keeping only latest event per group.
    """
    if df_l2.empty:
        return df_l2
        
    # Get grid boundaries from camera configs
    boundaries = get_grid_boundaries(camera_configs)
    
    # Assign initial grid cells
    df_l2['grid_cell'] = df_l2.apply(
        lambda row: assign_grid_cell(row['ground_x'], row['ground_y'], boundaries),
        axis=1
    )
    
    # Sort by timestamp to process events chronologically
    df_l2 = df_l2.sort_values('frame_time')
    
    # Initialize groups and camera tracking
    groups = []
    group_cameras = []  # Track cameras for each group
    group_start_times = []  # Track start time for each group
    processed = set()
    
    # Process each alert
    for idx, alert in df_l2.iterrows():
        if idx in processed:
            continue
            
        # Start a new group
        current_group = [idx]
        current_cameras = {alert['cam_id']}  # Track cameras as a set
        current_start_time = pd.to_datetime(alert['frame_time'])  # Initialize start time
        processed.add(idx)
        
        # Keep expanding the group until no more matches found
        group_expanded = True
        while group_expanded:
            group_expanded = False
            
            # Get all current cells in the group
            group_cells = set(df_l2.loc[current_group, 'grid_cell'])
            
            # Look for related events in same category that haven't been processed
            potential_neighbors = df_l2[
                (df_l2['category'] == alert['category']) & 
                (~df_l2.index.isin(processed))
            ]
            
            for other_idx, other_alert in potential_neighbors.iterrows():
                # Check if the new cell is adjacent to ANY cell in the current group
                is_adjacent = any(are_adjacent_cells(cell, other_alert['grid_cell']) for cell in group_cells)
                
                if is_adjacent:
                    other_time = pd.to_datetime(other_alert['frame_time'])
                    time_diff = abs((other_time - current_start_time).total_seconds())
                    
                    # Different time windows based on category
                    time_window = 300.0 if alert['category'] == 'unattended_object' else 5.0
                    
                    if time_diff <= time_window:
                        # Calculate distance between ground coordinates with the original alert
                        dx = alert['ground_x'] - other_alert['ground_x']
                        dy = alert['ground_y'] - other_alert['ground_y']
                        distance = (dx * dx + dy * dy) ** 0.5
                        
                        # Use a larger distance threshold (100 units) for grouping
                        if distance <= 100:
                            current_group.append(other_idx)
                            current_cameras.add(other_alert['cam_id'])
                            # Update start time if this event is earlier
                            if other_time < current_start_time:
                                current_start_time = other_time
                            processed.add(other_idx)
                            group_expanded = True  # Keep expanding since we found a match
        
        groups.append(current_group)
        group_cameras.append(current_cameras)
        group_start_times.append(current_start_time)
    
    # Assign group IDs starting from 1 and track cameras
    group_assignments = {}
    group_camera_dict = {}
    group_start_time_dict = {}
    for group_id, (group, cameras, start_time) in enumerate(zip(groups, group_cameras, group_start_times), start=1):
        for idx in group:
            group_assignments[idx] = group_id
        group_camera_dict[group_id] = cameras
        group_start_time_dict[group_id] = start_time
            
    df_l2['group_id'] = df_l2.index.map(group_assignments)
    
    # Add group size information before filtering
    group_sizes = df_l2.groupby('group_id').size()
    df_l2['group_size'] = df_l2['group_id'].map(group_sizes)
    
    # Add unique cameras per group
    df_l2['unique_cameras'] = df_l2['group_id'].map(lambda x: ','.join(sorted(group_camera_dict[x])))
    df_l2['num_cameras'] = df_l2['group_id'].map(lambda x: len(group_camera_dict[x]))
    
    # Add group start time
    df_l2['group_start_time'] = df_l2['group_id'].map(group_start_time_dict)

    
    print("\nGrouping Summary (before filtering):")
    print(f"Total events: {len(df_l2)}")
    print(f"Number of groups: {len(groups)}")
    print("Group sizes:", group_sizes.to_dict())
    print("\nCameras in remaining groups:")
    for group_id, cameras in group_camera_dict.items():
        grid_cells = sorted(set(df_l2[df_l2['group_id'] == group_id]['grid_cell']))
        start_time = group_start_time_dict[group_id]
        print(f"Group {group_id} (Cells {grid_cells}): {len(cameras)} cameras - {sorted(cameras)}")
        print(f"  Started at: {start_time}")
    
    # Keep only the latest event from each group
    df_l2['frame_time'] = pd.to_datetime(df_l2['frame_time'])
    latest_events = df_l2.loc[df_l2.groupby('group_id')['frame_time'].idxmax()]
    
    # Apply time-based filtering
    # Get current time in SGT
    current_time = pd.Timestamp.now(tz='Asia/Singapore')
    
    # Convert frame_time and group_start_time to SGT for comparison
    latest_events['frame_time'] = pd.to_datetime(latest_events['frame_time']).dt.tz_localize('Asia/Singapore')
    latest_events['group_start_time'] = pd.to_datetime(latest_events['group_start_time']).dt.tz_localize('Asia/Singapore')
    
    
    # Different filtering for unattended objects vs other categories
    time_mask = (
        # For unattended objects: check if group_start_time is within 3-5 minutes
        ((latest_events['category'] == 'unattended_object') & 
         ((current_time - latest_events['group_start_time']).dt.total_seconds() <= 300.0) &
         ((current_time - latest_events['group_start_time']).dt.total_seconds() >= 180.0)) |
        # For other categories: check if frame_time is within last 5 seconds
        ((latest_events['category'] != 'unattended_object') & 
         ((current_time - latest_events['frame_time']).dt.total_seconds() <= 5.0))
    )
    filtered_events = latest_events[time_mask]
    
    print("\nAfter time-based filtering:")
    print(f"Current time (SGT): {current_time}")
    print(f"Remaining events: {len(filtered_events)}")
    print(f"Groups represented: {filtered_events['group_id'].nunique()}")
    print("Categories remaining:", filtered_events['category'].value_counts().to_dict())
    print("\nCameras in remaining groups:")
    for _, event in filtered_events.iterrows():
        print(f"Group {event['group_id']} (Cell {event['grid_cell']}): {event['num_cameras']} cameras - {event['unique_cameras']}")
        print(f"  Started at: {event['group_start_time']}")
    
    return filtered_events

def main():
    print("\n=== Detection Layer 2 Process Started ===")

    redis_client = CameraCache()
    alerts_redis_client = CameraCache(host="alerts_redis")
    # streaming_redis_client = CameraCache(host="streaming_redis")
    # Keep looping through the redis stream
    while True:
        start_time = time.time()
        # Get all the current L1 detections
        rows = get_last_5min(alerts_redis_client)
        # rows = get_df()
        print(f"Number of L1 detections: {len(rows)}")

        # Convert to pandas dataframe
        df_l1 = pd.DataFrame(rows)

        if not df_l1.empty:
            print("Converting timestamps to datetime...")
            df_l1["timestamp"] = pd.to_datetime(df_l1["timestamp"]).dt.tz_localize('Asia/Singapore')
            df_l1['frame_time'] = pd.to_datetime(df_l1["frame_time"]).dt.tz_localize('Asia/Singapore')

            print("Mapping bounding boxes to ground coordinates...")
            # Map bounding boxes to ground coordinates
            df_l1 = compute_ground_coords(df_l1)
            # Drop all the rows where ground_x is None
            df_l1 = df_l1[df_l1['ground_x'].notna()]
            # Drop all the rows where the ground x or ground y is outside of the boundaries
            df_l1 = df_l1[df_l1.apply(lambda row: inside_rect(np.asarray([row['ground_x'], row['ground_y']])), axis=1)]

            print(f"L1 detections: {df_l1}")

            # Use current Unix timestamp for time comparison
            current_time = pd.Timestamp.now(tz='Asia/Singapore')
            # current_time = min(df_l1["timestamp"])
            print(f"Current time: {current_time}")

            # Filter df_l1 based on time duration
            time_mask = (
                # For unattended objects: check if timestamp is within 3-5 minutes
                ((df_l1['category'] == 'unattended_object') & 
                 ((current_time - df_l1['frame_time']).dt.total_seconds() <= 300.0)) |
                # For other categories: check if frame_time is within last 5 seconds  
                ((df_l1['category'] != 'unattended_object') &
                 ((current_time - df_l1['frame_time']).dt.total_seconds() <= 5.0))
            )
            df_l1 = df_l1[time_mask]

            # Need to do the clustering based on the category, camera id, min frame, ground x, ground y
            print("Clustering L1 detections...")
            print("DataFrame columns:", df_l1.columns.tolist())
            print("DataFrame head:")
            print(df_l1.head())
            print("DataFrame info:")
            print(df_l1.info())

            df_l1_kept, df_l1_discarded = cluster_df(df_l1)
            print(f"Number of L1 detections after clustering: {len(df_l1_kept)}")
            print(f"Number of L1 detections discarded: {len(df_l1_discarded)}")

            df_l2 = pd.DataFrame()
            for index, row in df_l1_kept.iterrows():
                df_l2 = pd.concat([df_l2, pd.DataFrame({
                    "event_id": row["event_id"],  # reuse L1 event ID
                    "timestamp": row["timestamp"],
                    "frame_time": row["frame_time"],
                    "cam_id": row["cam_id"],
                    "category": row["category"],
                    "bbox": row["bbox"],
                    "ground_x": row["ground_x"],
                    "ground_y": row["ground_y"],
                    "confidence": row["confidence"],
                    "is_primary": True,
                    "group_id": row["cluster_id"],
                    "alert_type": "L2",
                })], ignore_index=True)
            
            # Drop duplicates based on event_id, keeping the first occurrence
            df_l2 = df_l2.drop_duplicates(subset=['event_id'], keep='first')
            print(f"Number of unique L2 events after deduplication: {len(df_l2)}")

            # Process L2 alerts
            if not df_l2.empty:
                print("Processing L2 alerts...")
                # Convert DataFrame to list of dictionaries for alert processing
                promoted_l2 = df_l2.to_dict("records")

                for alert in promoted_l2:
                    print(f"Processing alert: {alert['category']}")
                    print(alert)

                    event_type = CATEGORY.get(alert["category"], "unknown")
                    event_id = alert[
                        "event_id"
                    ]  # Using the event_id from the promotion
                    cam_id = alert["cam_id"]

                    print(
                        f"Getting event payload for ID: {event_id}, {alert.get('ground_x')}, {alert.get('ground_y')}"
                    )
                    data, frame_bytes = get_event_payload(alerts_redis_client, redis_client, event_id)

                    if not data or not frame_bytes:
                        print("No event data or frame found, skipping...")
                        continue

                    base = make_event(
                        event_id,
                        event_type,
                        alert.get("ground_x"),
                        alert.get("ground_y"),
                        cam_id
                    )
                    # Check by the ground x and ground y
                    if not should_send(base, alerts_redis_client, alert["frame_time"]):
                        print(f"Event already sent for {event_id}, skipping...")
                        continue

                    # Check by the image similarity
                    bbox_dict = {
                        'x1': data.get("original_bbox")[0],
                        'y1': data.get("original_bbox")[1],
                        'x2': data.get("original_bbox")[2],
                        'y2': data.get("original_bbox")[3]
                    }
                    if not check_image_similarity(
                        cam_id,
                        frame_bytes,
                        bbox_dict,
                        alerts_redis_client
                    ):
                        print(f"Image similarity check failed for {event_id}, skipping...")
                        continue

                    print("Drawing box and text on frame...")
                    # Draw box and text on the frame
                    frame_bytes = draw_box_and_text(
                        frame_bytes, data.get("original_bbox"), event_type
                    )

                    print(f"Preparing enriched alert for event {event_id}...")
                    # Formulate the alert dictionary
                    enriched_alert = {
                        **alert,
                        "camera_id": data.get("cam_id"),
                        "camera_layer_config_id": data.get("camera_layer_config_id"),
                        "confidence": round(float(alert.get("confidence", 0.0)), 2),
                        "event_type": event_type,
                        "event_severity": "Critical",
                        "bounding_box": {
                            "x1": data.get("original_bbox")[0],
                            "y1": data.get("original_bbox")[1],
                            "x2": data.get("original_bbox")[2],
                            "y2": data.get("original_bbox")[3],
                        },
                        "timestamp": data.get("timestamp"),
                        "frame_id": data.get("frame_id"),
                        "redis_client": redis_client,
                        "alerts_redis_client": alerts_redis_client,
                        "frame_bytes": frame_bytes,
                        "frame_time": data.get("frame_time"),
                        "preprocessing_time": data.get("preprocessing_time"),
                        "prediction_time": data.get("prediction_time"),
                    }

                    print("Sending alert...")
                    send_alerts(enriched_alert, alerts_redis_client)
                    print("Alert sent successfully")
        else:
            print("No L1 detections to process")

        # Sleep for a bit before next iteration
        print("Sleeping for 1 second...")
        end_time = time.time()
        print(f"Total time taken: {end_time - start_time}")
        time.sleep(1)


if __name__ == "__main__":
    main()
