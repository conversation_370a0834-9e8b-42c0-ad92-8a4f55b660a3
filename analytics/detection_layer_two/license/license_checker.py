import sys, json, datetime, hashlib, socket, os
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding

# Default paths for Docker environment
DEFAULT_LICENSE_PATH = "./license/license.json"
DEFAULT_PUBLIC_KEY_PATH = "./license/public_key.pem"

# Allow override via environment variables for testing
LICENSE_PATH = os.environ.get("LICENSE_PATH", DEFAULT_LICENSE_PATH)
PUBLIC_KEY_PATH = os.environ.get("PUBLIC_KEY_PATH", DEFAULT_PUBLIC_KEY_PATH)

# Print configuration if in debug mode
if os.environ.get("LICENSE_DEBUG", "").lower() in ("1", "true", "yes"):
    print(f"[DEBUG] License path: {LICENSE_PATH}")
    print(f"[DEBUG] Public key path: {PUBLIC_KEY_PATH}")


def get_host_id():
    # e.g. md5 of the machine's hostname
    h = hashlib.md5(socket.gethostname().encode()).hexdigest()
    return h

def load_pubkey():
    return serialization.load_pem_public_key(open(PUBLIC_KEY_PATH,"rb").read())

def verify(data: bytes, sig: bytes, key):
    key.verify(sig, data, padding.PKCS1v15(), hashes.SHA256())

def main():
    lic = json.load(open(LICENSE_PATH))
    # Get current time as UTC with timezone info
    now = datetime.datetime.now(datetime.timezone.utc)
    # Parse expiry date with proper timezone handling
    expires = datetime.datetime.fromisoformat(lic["expires"].replace("Z","+00:00"))

    # # 1) host binding
    # if lic.get("host_id") != get_host_id():
    #     print("Host mismatch!", file=sys.stderr); sys.exit(1)

    # 2) expiry
    if now > expires:
        print("License expired:", expires, file=sys.stderr); sys.exit(1)

    # 3) signature over "expires+host_id"
    raw = (lic["expires"] + lic["host_id"]).encode()
    sig = bytes.fromhex(lic["signature"])
    try:
        verify(raw, sig, load_pubkey())
    except Exception as e:
        print("Bad signature:", e, file=sys.stderr); sys.exit(1)

    print("License verified. Expiring at:", expires)
    
if __name__=="__main__":
    main()
