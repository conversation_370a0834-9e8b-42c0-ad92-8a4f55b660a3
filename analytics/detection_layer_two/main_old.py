from dotenv import load_dotenv
load_dotenv()

import logging
import time
import json
import pandas as pd
from sklearn.cluster import DBSCAN
import numpy as np
import uuid
import cv2
from datetime import datetime

from logger_setup import setup_logging
from ground_mapping import map_bbox_to_ground
from alerts import run_l2_promotion
from license.license_checker import main as license_checker
from send_alerts import send_alerts

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

from redis_connection import Camera<PERSON>ache

CATEGORY = {
    "weapon": "CR2 - Weapon",
    "oversized_object": "CR3 - Oversized object detected",
    "scaling_gantry": "CR6 - The person is the scaling gantry",
    "unattended_object": "CR4 - Unattended object detected",
    "suspicious_person": "CR1 - Suspicious person detected",
    "unknown": "Unknown alert"
}


def get_df():
    with open("l1_event.json", "r") as f:
        data = json.load(f)
    return data

def get_last_5min(redis_client: CameraCache):
    now    = time.time()
    window = now - 300.0       # five minutes ago

    logger.info(f"Getting events from {window} to {now} (last 5 minutes)")

    # 1) grab every camera‐zset key
    try:
        zkeys = [ k for k in redis_client._redis.scan_iter(match="events:layer_one:cam:*") ]
        logger.info(f"Found {len(zkeys)} camera keys: {zkeys}")
    except Exception as e:
        logger.error(f"Error scanning Redis keys: {e}")
        return []

    # 2) pipeline a ZRANGEBYSCORE on each
    try:
        pipe = redis_client._redis.pipeline()
        for z in zkeys:
            pipe.zrangebyscore(z, window, now)
        results = pipe.execute()
        logger.info(f"Retrieved {len(results)} result sets from Redis pipeline")
    except Exception as e:
        logger.error(f"Error executing Redis pipeline: {e}")
        return []

    # 3) flatten + parse
    rows = []
    try:
        for i, payloads in enumerate(results):
            logger.debug(f"Processing result set {i} with {len(payloads)} payloads")
            for p in payloads:
                try:
                    parsed = json.loads(p)
                    rows.append(parsed)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON payload: {e}")
                    continue
        logger.info(f"Successfully parsed {len(rows)} events")
    except Exception as e:
        logger.error(f"Error processing results: {e}")
        return []

    return rows


def compute_ground_coords(df):
    """
    Map bounding boxes from image space to ground plane coordinates.
    
    Args:
        df (DataFrame): DataFrame containing detections with bbox and cam_id columns
        
    Returns:
        DataFrame: Updated DataFrame with ground_x and ground_y columns
    """
    ground_x_list = []
    ground_y_list = []

    cam_mapping = {
        'cam_one': 'cam01',
        'cam_two': 'cam02', 
        'cam_three': 'cam03',
        'cam_four': 'cam04',
        'cam_five': 'cam05',
        'cam_six': 'cam06',
        'cam_seven': 'cam07',
        'cam_eight': 'cam08',
        'cam_nine': 'cam09',
        "video_one.mp4": "cam01",
        "video_two.mp4": "cam02", 
        "video_three.mp4": "cam03",
        "video_four.mp4": "cam04",
        "video_five.mp4": "cam05",
        "video_eight.mp4": "cam08",
        "video_nine.mp4": "cam09"
    }

    for _, row in df.iterrows():
        cam_id = cam_mapping.get(row["cam_id"], row["cam_id"])
        # Convert cam_id format if needed (e.g., cam_01 -> cam01)
        if cam_id.startswith("cam_"):
            cam_id = f"cam{cam_id[4:]}"  # Convert cam_01 to cam01

        bbox = row["bbox"]
        try:
            # Create bbox dict for map_bbox_to_ground function
            bbox_dict = {"x1": bbox[0], "y1": bbox[1], "x2": bbox[2], "y2": bbox[3]}
            coords = map_bbox_to_ground(cam_id, [bbox_dict])
            ground_x_list.append(coords[0][0])
            ground_y_list.append(coords[0][1])
        except Exception as e:
            ground_x_list.append(None)
            ground_y_list.append(None)
            print(f"⚠️ Failed to map coordinates for cam {cam_id}: {e}")

    df["ground_x"] = ground_x_list
    df["ground_y"] = ground_y_list
    return df


def group_by_proximity(df, eps=1.0, min_samples=1):
    """
    Clusters events using DBSCAN based on ground_x, ground_y distance.
    
    Args:
        df (DataFrame): DataFrame with ground_x and ground_y columns
        eps (float): Distance threshold in arbitrary ground units
        min_samples (int): Minimum cluster size

    Returns:
        DataFrame: DataFrame with new group_id column
    """
    # Filter out rows with missing ground coordinates
    df_valid = df.dropna(subset=["ground_x", "ground_y"])
    
    if len(df_valid) < min_samples:
        # Not enough valid points for clustering
        df["group_id"] = -1
        return df
        
    # Extract coordinates for clustering
    coords = df_valid[["ground_x", "ground_y"]].to_numpy()
    
    # Run DBSCAN clustering
    clustering = DBSCAN(eps=eps, min_samples=min_samples, metric='euclidean').fit(coords)
    
    # Assign cluster labels to valid rows
    df_valid["group_id"] = clustering.labels_
    
    # Merge back with original dataframe
    df = df.drop(columns=["group_id"], errors="ignore")
    df = pd.merge(df, df_valid[["event_id", "group_id"]], on="event_id", how="left")
    df["group_id"] = df["group_id"].fillna(-1).astype(int)
    
    return df


def promote_grouped_events(df):
    """
    Creates promoted events by merging detections in the same group.
    
    Args:
        df (DataFrame): DataFrame with group_id column from group_by_proximity
        
    Returns:
        DataFrame: Original DataFrame with added promoted events
    """
    promoted_rows = []
    grouped = df.groupby(["cam_id", "category", "group_id"])
    
    for (cam_id, category, group_id), group in grouped:
        if group_id == -1 or len(group) < 2:
            continue  # Skip noise or non-grouped detections

        # Create a merged bounding box
        bbox_array = np.array([row.bbox for row in group.itertuples()])
        x1 = bbox_array[:, 0].min()
        y1 = bbox_array[:, 1].min()
        x2 = bbox_array[:, 2].max()
        y2 = bbox_array[:, 3].max()

        # Create a new promoted event
        new_row = {
            "event_id": str(uuid.uuid4()),
            "timestamp": group.timestamp.min(),
            "cam_id": cam_id,
            "category": category,
            "bbox": [int(x1), int(y1), int(x2), int(y2)],
            "ground_x": group.ground_x.mean(),
            "ground_y": group.ground_y.mean(),
            "confidence": group.confidence.max(),
            "is_primary": False,  # Mark as a promoted event
            "group_id": group_id,
        }
        promoted_rows.append(new_row)

    # Add promoted events to the original DataFrame
    if promoted_rows:
        df_promoted = pd.DataFrame(promoted_rows)
        df_final = pd.concat([df, df_promoted], ignore_index=True)
        return df_final
    else:
        return df
    

def get_event_payload(redis_client, event_id):
    """
    Retrieve the full event payload from Redis for a given event ID.
    
    Args:
        redis_client: Redis client instance
        event_id: The event ID to look up
        
    Returns:
        dict: The event payload if found, None otherwise
    """
    try:
        # Construct the key for this event
        event_key = f"event:{event_id}"
        logger.info(f"Looking up event with key: {event_key}")
        
        # Get the payload from Redis
        data = redis_client.get(event_key)
        logger.info(f"Retrieved payload from Redis: {data is not None}")
        
        if data:
            logger.info(f"Successfully retrieved data: {data is not None}")

            if data:
                # Get the frame using frame id
                frame_id = data["frame_id"]
                logger.info(f"Getting frame bytes for frame_id: {frame_id}")
                original_frame_key = frame_id.replace("_final_result", "")
                frame_payload = redis_client.get(original_frame_key)
                logger.info(f"Retrieved frame payload: {frame_payload is not None}")
                
                if frame_payload and "frame" in frame_payload:
                    # Extract and decode the frame bytes from base64
                    frame_bytes = redis_client.from_jsonable_bytes(frame_payload["frame"])
                    logger.info(f"Retrieved frame bytes: {frame_bytes is not None}")
                    return data, frame_bytes
                else:
                    logger.warning(f"No frame data found in payload for frame_id: {frame_id}")
                    return data, None
            
            else:
                logger.warning(f"No data found in payload for event {event_id}")
                return None, None
        
        logger.warning(f"No payload found in Redis for event {event_id}")
        return None, None
        
    except Exception as e:
        logger.error(f"Error retrieving event payload for {event_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None


def draw_box_and_text(image_bytes, bbox, category):
    """
    Draw bounding box and category text on image.
    
    Args:
        image_bytes (bytes): Raw image bytes
        bbox (list): Bounding box coordinates [x1, y1, x2, y2]
        category (str): Category text to display
        
    Returns:
        bytes: Image bytes with box and text drawn
    """
    try:
        # Convert image bytes to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Draw bounding box
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Add text with background
        text = str(category)
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1
        thickness = 2
        
        # Get text size
        (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
        
        # Draw background rectangle for text
        cv2.rectangle(img, (x1, y1-text_height-10), (x1+text_width+10, y1), (0, 0, 255), -1)
        
        # Draw text
        cv2.putText(img, text, (x1+5, y1-5), font, font_scale, (255, 255, 255), thickness)
        
        # Encode back to bytes
        _, img_encoded = cv2.imencode('.jpg', img)
        return img_encoded.tobytes()
        
    except Exception as e:
        logger.error(f"Error drawing box and text: {e}")
        return image_bytes


def main():
    redis_client = CameraCache()
    print("\n=== Detection Layer 2 Process Started ===")

    # Keep looping through the redis stream
    while True:
        # Get all the current L1 detections
        rows = get_last_5min(redis_client)
        rows = get_df()
        print(f"Number of L1 detections: {len(rows)}")
        
        # Convert to pandas dataframe
        df_l1 = pd.DataFrame(rows)
        print(f"L1 detections: {df_l1.head()}")

        # Step 2.5: Map detections to ground plane and group by proximity
        # Convert timestamp strings to datetime objects
        if not df_l1.empty:
            print("Converting timestamps to datetime...")
            df_l1['timestamp'] = pd.to_datetime(df_l1['timestamp'])

            print("Mapping bounding boxes to ground coordinates...")
            # Map bounding boxes to ground coordinates
            df_l1 = compute_ground_coords(df_l1)
            
            print("Grouping detections by proximity...")
            # Group detections by proximity on ground plane
            df_l1 = group_by_proximity(df_l1, eps=1.2, min_samples=1)
            
            print("Promoting grouped events...")
            # Create promoted events for grouped detections
            df_l1 = promote_grouped_events(df_l1)

            print(f"Available columns: {df_l1.columns}")

        # TODO:
        # Step 3: Run L2 promotion logic
        print("Step 3: Running L2 promotion")
        current_time = time.time()
        
        if df_l1.empty:
            print("No detections available for L2 promotion.")
            promoted_l2 = []
        else:
            excluded = ["human", "unmatched", "unattended_object"]
            df_l1_filtered = df_l1[~df_l1["category"].isin(excluded)]
            promoted_l2 = run_l2_promotion(df_l1_filtered, current_time)
            print(f"Number of L2 promoted events: {len(promoted_l2)}")

        for alert in promoted_l2:
            print(f"Processing alert: {alert['category']}")

            event_type = CATEGORY.get(alert["category"], "unknown")
            event_ids = alert["source_detections"]

            if not event_ids:
                print("No event IDs found, skipping...")
                continue

            print(f"Getting event payload for ID: {event_ids[0]}")
            data, frame_bytes = get_event_payload(redis_client, event_ids[0])

            print("Drawing box and text on frame...")
            # Draw box and text on the frame
            frame_bytes = draw_box_and_text(frame_bytes, data.get("original_bbox"), event_type)

            print("Preparing enriched alert...")
            # Formulate the alert dictionary
            enriched_alert = {
                **alert,
                "camera_id": data.get("cam_id"),
                "camera_layer_config_id": data.get("camera_layer_config_id"),
                "confidence": round(float(alert.get("confidence_avg", 0.0)), 2),
                "event_type": event_type,
                "event_severity": "Critical",
                "bounding_box": {
                    "x1": data.get("original_bbox")[0],
                    "y1": data.get("original_bbox")[1],
                    "x2": data.get("original_bbox")[2],
                    "y2": data.get("original_bbox")[3]
                },
                "timestamp": data.get("timestamp_min"),
                "frame_id": data.get("frame_id"),
                "redis_client": redis_client,
                "frame_bytes": frame_bytes,
                "frame_time": data.get("frame_time"),
                "preprocessing_time": data.get("preprocessing_time"),
                "prediction_time": data.get("prediction_time")
            }

            print("Sending alert...")
            send_alerts(enriched_alert, redis_client)
            print("Alert sent successfully")

        print("Sleeping for 1 second...")
        time.sleep(1)

        logger.info("Sleeping for 1 second")


if __name__ == "__main__":
    try: 
        license_checker()
        main()
    except Exception as e:
        import traceback
        logger.error(f"Error in main: {str(e)}")
        traceback.print_exc()
