"""
Utility functions for mapping between camera views and ground plane coordinates.
Supports both:
1. Legacy point-based homography (using encrypted config)
2. Enhanced homography mapping with calibration files
"""

import cv2
import numpy as np
import os
import logging
from pathlib import Path

# Import calibration data from config module
from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
IMAGE_POINTS = decrypted_config["L2_CALIBRATION"]["IMAGE_POINTS"]
GROUND_POINTS = decrypted_config["L2_CALIBRATION"]["GROUND_POINTS"]
TOPDOWN_PIXEL_POINTS = decrypted_config["L2_CALIBRATION"]["TOPDOWN_PIXEL_POINTS"]

# Setup paths for enhanced homography mapping system
BASE_DIR = Path(__file__).parent
CALIB_DIR = BASE_DIR / "calibration"
CAMERA_CONFIG_DIR = CALIB_DIR / "camera_configs"
FLOORPLAN_CONFIG_DIR = CALIB_DIR / "floorplan_config"
HOMOGRAPHY_DIR = CALIB_DIR / "homography_matrices"

# Global flag to enable/disable enhanced homography
USE_ENHANCED_HOMOGRAPHY = True  # Set to False to use only legacy system

# Cache for loaded homography matrices
HOMOGRAPHY_CACHE = {}

# Floorplan dimensions - will be updated on first load from floorplan image if available
FLOORPLAN_WIDTH = 1000  # Default value
FLOORPLAN_HEIGHT = 800  # Default value

def load_homography_matrices(camera_key):
    """
    Load homography matrices for the specified camera.
    
    Args:
        camera_key: Camera identifier (e.g., 'CAM_01' or 'cam01')
        
    Returns:
        tuple: (H_cam2plane, H_plane2floor) homography matrices or (None, None) on error
    """
    # Standardize camera key format (try both formats)
    std_camera_key = camera_key.upper() if not camera_key.upper().startswith('CAM_') else camera_key.upper()
    alt_camera_key = f"CAM_{camera_key[3:]}" if camera_key.lower().startswith('cam') else f"cam{camera_key[4:]}"
    
    # Check cache first
    if std_camera_key in HOMOGRAPHY_CACHE:
        return HOMOGRAPHY_CACHE[std_camera_key]
        
    try:
        # Try different formats of camera-specific matrix files
        camera_keys_to_try = [std_camera_key, alt_camera_key]
        
        for key in camera_keys_to_try:
            cam_h_cam2plane = HOMOGRAPHY_DIR / f"{key}_H_cam2plane.npy"
            cam_h_plane2floor = HOMOGRAPHY_DIR / f"{key}_H_plane2floor.npy"
            
            if os.path.exists(cam_h_cam2plane) and os.path.exists(cam_h_plane2floor):
                H_cam2plane = np.load(cam_h_cam2plane)
                H_plane2floor = np.load(cam_h_plane2floor)
                
                # Cache the matrices
                HOMOGRAPHY_CACHE[std_camera_key] = (H_cam2plane, H_plane2floor)
                return H_cam2plane, H_plane2floor
        
        # If no camera-specific files found, try generic ones
        generic_h_cam2plane = HOMOGRAPHY_DIR / "H_cam2plane.npy"
        generic_h_plane2floor = HOMOGRAPHY_DIR / "H_plane2floor.npy"
        
        if os.path.exists(generic_h_cam2plane) and os.path.exists(generic_h_plane2floor):
            H_cam2plane = np.load(generic_h_cam2plane)
            H_plane2floor = np.load(generic_h_plane2floor)
            
            # Cache the matrices
            HOMOGRAPHY_CACHE[std_camera_key] = (H_cam2plane, H_plane2floor)
            return H_cam2plane, H_plane2floor
        else:
            logging.warning(f"No homography matrices found for {camera_key}, using legacy approach")
            return None, None
            
    except Exception as e:
        logging.error(f"Error loading homography matrices for {camera_key}: {e}")
        return None, None


def map_camera_to_ground_enhanced(cam_id, camera_points, normalize=True):
    """
    Map points from camera view to ground plane using enhanced homography matrices.
    
    Parameters:
        cam_id (str): Camera identifier like 'CAM_01' or 'cam01'
        camera_points (list): List of (x, y) camera coordinate points
        normalize (bool): If True, normalize coordinates to 0.0-1.0 range
    
    Returns:
        list of (ground_x, ground_y): Either absolute ground coordinates or normalized (0.0-1.0)
    """
    # Load homography matrices for this camera
    H_cam2plane, H_plane2floor = load_homography_matrices(cam_id)
    if H_cam2plane is None or H_plane2floor is None:
        return None  # Fall back to legacy method
    
    result = []
    for cx, cy in camera_points:
        # Camera to abstract plane transformation
        pt = np.array([[[cx, cy]]], dtype=np.float32)
        mapped_cam2plane = cv2.perspectiveTransform(pt, H_cam2plane)[0][0]
        
        # Abstract plane to floorplan transformation
        pt2 = np.array([[[mapped_cam2plane[0], mapped_cam2plane[1]]]], dtype=np.float32)
        mapped_plane2floor = cv2.perspectiveTransform(pt2, H_plane2floor)[0][0]
        
        # Store as absolute coordinates or normalize
        if normalize:
            # Normalize coordinates to 0-1 range based on floorplan dimensions
            norm_x = float(mapped_plane2floor[0]) / FLOORPLAN_WIDTH
            norm_y = float(mapped_plane2floor[1]) / FLOORPLAN_HEIGHT
            result.append((norm_x, norm_y))
        else:
            result.append((float(mapped_plane2floor[0]), float(mapped_plane2floor[1])))
    
    return result

def map_bbox_to_ground(cam_id, bboxes, normalize=False, image_width=None, image_height=None):
    """
    Map bounding boxes from image space to ground plane coordinates.

    Parameters:
        cam_id (str): camera identifier like 'cam01' or 'cam_01'
        bboxes (list): list of dicts {"x1":..., "y1":..., "x2":..., "y2":...}
        normalize (bool): If True, normalize coordinates to 0.0-1.0 range
        image_width (int): Width of image for normalization (required if normalize=True)
        image_height (int): Height of image for normalization (required if normalize=True)

    Returns:
        list of (ground_x, ground_y): Either absolute ground coordinates or normalized coordinates (0.0-1.0)
    """
    # Try enhanced homography approach first if enabled
    if USE_ENHANCED_HOMOGRAPHY:
        try:
            # Extract bottom center points from bboxes
            camera_points = [(float(bbox["x1"] + bbox["x2"]) / 2, float(bbox["y2"])) for bbox in bboxes]
            
            # Call enhanced mapping function
            enhanced_result = map_camera_to_ground_enhanced(cam_id, camera_points, normalize)
            
            # If enhanced mapping worked, return the result
            if enhanced_result is not None:
                logging.info(f"Using enhanced homography mapping for camera {cam_id}")
                return enhanced_result
            
        except Exception as e:
            logging.warning(f"Enhanced homography mapping failed: {e}, falling back to legacy method")
            # Continue with legacy approach
    
    # Legacy approach using encrypted config points
    # Normalize cam_id format (remove underscore if present)
    if cam_id.startswith("cam_"):
        cam_id = f"cam{cam_id[4:]}"
        
    if cam_id not in IMAGE_POINTS:
        raise ValueError(f"Unknown camera ID: {cam_id}")

    # Get calibration points for this camera
    available_keys = list(IMAGE_POINTS[cam_id].keys())
    
    # We need at least 4 points for homography
    if len(available_keys) < 4:
        raise ValueError(f"Not enough calibration points for camera {cam_id}")
    
    # Extract only the available points for this camera
    img_pts = np.float32([IMAGE_POINTS[cam_id][k] for k in available_keys])
    grd_pts = np.float32([GROUND_POINTS[k] for k in available_keys])
    
    # Calculate homography matrix
    H, _ = cv2.findHomography(img_pts, grd_pts)

    # Validate normalization parameters
    if normalize and (image_width is None or image_height is None):
        raise ValueError("image_width and image_height must be provided when normalize=True")
    
    result = []
    
    # Find the min and max values from all ground points to use for normalization
    if normalize:
        all_ground_pts = np.array([GROUND_POINTS[k] for k in GROUND_POINTS.keys()])
        min_x = np.min(all_ground_pts[:, 0])
        max_x = np.max(all_ground_pts[:, 0])
        min_y = np.min(all_ground_pts[:, 1]) 
        max_y = np.max(all_ground_pts[:, 1])
        
    for bbox in bboxes:
        # Use bottom center of bounding box as reference point
        cx = (bbox["x1"] + bbox["x2"]) / 2
        cy = bbox["y2"]  # Use bottom of bbox
        
        # Apply homography transformation
        pt = np.array([[[cx, cy]]], dtype=np.float32)
        mapped = cv2.perspectiveTransform(pt, H)[0][0]
        
        if normalize:
            # Normalize using the min/max values from ground points
            norm_x = (mapped[0] - min_x) / (max_x - min_x) 
            norm_y = (mapped[1] - min_y) / (max_y - min_y)
            
            # Optionally scale by the image dimensions if needed
            if image_width is not None and image_height is not None:
                scaled_x = norm_x * image_width
                scaled_y = norm_y * image_height
                result.append((scaled_x, scaled_y))
            else:
                result.append((norm_x, norm_y))
        else:
            result.append((mapped[0], mapped[1]))

    return result


def map_ground_to_topdown(ground_coords):
    """
    Map ground plane coordinates to pixel positions on top-down image.

    Parameters:
        ground_coords (list): list of (ground_x, ground_y)

    Returns:
        list of (pixel_x, pixel_y)
    """
    # Get ground points and corresponding pixel points
    grd_pts = np.float32([GROUND_POINTS[k] for k in TOPDOWN_PIXEL_POINTS.keys()])
    px_pts = np.float32([TOPDOWN_PIXEL_POINTS[k] for k in TOPDOWN_PIXEL_POINTS.keys()])
    
    # Calculate homography matrix
    H, _ = cv2.findHomography(grd_pts, px_pts)

    result = []
    for gx, gy in ground_coords:
        # Apply homography transformation
        pt = np.array([[[gx, gy]]], dtype=np.float32)
        mapped = cv2.perspectiveTransform(pt, H)[0][0]
        result.append((int(mapped[0]), int(mapped[1])))
    return result
