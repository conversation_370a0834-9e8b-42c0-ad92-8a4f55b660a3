import ast

SAFE_NODES = {
    ast.Expression, ast.BoolOp, ast.BinOp, ast.UnaryOp,
    ast.And, ast.Or, ast.Not,
    ast.Compare, ast.Gt, ast.GtE, ast.Lt, ast.LtE, ast.Eq, ast.NotEq,
    ast.Name, ast.Load, ast.Constant,
    ast.Subscript, ast.Index,
}

def compile_condition(expr: str):
    tree = ast.parse(expr, mode="eval")
    for node in ast.walk(tree):
        if type(node) not in SAFE_NODES:
            raise ValueError(f"Disallowed node: {node!r}")
    return compile(tree, "<cond>", "eval")

def eval_condition(code, context: dict) -> bool:
    # No builtins, only your context keys
    return bool(eval(code, {"__builtins__": {}}, context))

def check_condition(condition: str, context: dict) -> bool:
    condition_str = condition.get("condition", "")
    code = compile_condition(condition_str)
    return eval_condition(code, context)
