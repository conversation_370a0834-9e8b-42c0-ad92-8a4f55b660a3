import os
import subprocess
import datetime
import pytz
import cv2
import numpy as np
import time
import select
from io import BytesIO

from typing import Generator, Tuple, List, Dict, Any, Optional


class RTSPStream:
    def __init__(self):
        self.proc = None
        self.rtsp_url = None
        self.fps = None
        self.camera_id = None
        self.frame_buffer = bytearray()
        self.width = 1920
        self.height = 1080
        self.is_running = False
        self.frame_id = 0

    def _start_ffmpeg(self, rtsp_url: str, fps: int) -> subprocess.Popen:
        try:
            print("Starting FFmpeg with RTSP URL: ", rtsp_url)
            print("FPS: ", fps)
            # Enhanced FFmpeg command with better buffering and error handling
            if "streaming" in str(rtsp_url).lower() and os.environ.get('STREAMING_START_TIME'):
                start_time = os.environ.get('STREAMING_START_TIME')
                print(f"Streaming start time: {start_time}")
                ffmpeg_cmd = [
                    "ffmpeg", "-hide_banner", "-loglevel", "error",
                    "-fflags", "nobuffer+discardcorrupt+genpts",
                    "-avoid_negative_ts", "make_zero",
                    "-flags", "low_delay",
                    "-max_delay", "500000",
                    "-rtsp_transport", "tcp", 
                    "-i", f"{rtsp_url}?starttime={start_time}",
                    "-vf", f"fps={fps}",
                    "-vcodec", "mjpeg",
                    "-q:v", "2",  # Quality setting for MJPEG
                    "-f", "image2pipe", 
                    "-"
                ]
            else:

                ffmpeg_cmd = [
                    "ffmpeg", "-hide_banner", "-loglevel", "error",
                    "-fflags", "nobuffer+discardcorrupt+genpts",
                    "-avoid_negative_ts", "make_zero",
                    "-flags", "low_delay",
                    "-flush_packets", "1",
                    "-max_delay", "500000",
                    "-rtsp_transport", "tcp",
                    "-i", rtsp_url,
                    "-pix_fmt", "bgr24",     # Raw video format
                    "-vcodec", "rawvideo",   # Raw video codec
                    "-vf", f"fps={fps}",        # Frame rate
                    "-f", "rawvideo",        # Raw video format
                    "-"
                ]
                
            print(f"FFmpeg command: {' '.join(ffmpeg_cmd)}")
            
            # Start FFmpeg process with better error handling
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0  # Unbuffered output
            )
            
            # Wait a moment to check if process started successfully
            time.sleep(0.5)
            if process.poll() is not None:
                stderr_output = process.stderr.read().decode() if process.stderr else "Unknown error"
                print(f"FFmpeg failed to start: {stderr_output}")
                return None
            
            print("Stream started successfully")
            return process
            
        except Exception as e:
            print(f"Streaming error: {e}")
            return None

    def stream(self) -> Generator[Tuple[bytes, datetime.datetime, int], None, None]:
        """
        Stream MJPEG frames from the camera.
        
        Yields:
            Tuple[bytes, datetime.datetime, int]: MJPEG frame data, timestamp, and frame ID
        """        
        # Camera dimensions for raw video
        frame_size = self.width * self.height * 3  # BGR24 format

        try:
            print("THIS IS RUNNING FROM RTSP STREAM")
            self.proc = self._start_ffmpeg(self.rtsp_url, self.fps)
            print(f"self.proc: {self.proc}")
            
            if not self.proc or self.proc.poll() is not None:
                print("Failed to start ffmpeg process")
                return None, None, None
            
            print("FFmpeg process started successfully")

            self.is_running = True
            retry_count = 0
            max_retries = 3

            frame_size = self.width * self.height * 3  # e.g. 1920*1080*3 = 6 220 800
            buf = bytearray(frame_size)
            view = memoryview(buf)
            bytes_read = 0

            while self.is_running:
                print("Polling for frame")
                try:
                    # # Read exactly one frame of raw video data
                    # raw_frame = self.proc.stdout.read(frame_size)

                    while bytes_read < frame_size:
                        n = self.proc.stdout.readinto(view[bytes_read:])
                        if not n:
                            raise RuntimeError("FFmpeg pipe closed early")
                        bytes_read += n

                    raw_frame = bytes(buf)

                    if len(raw_frame) < frame_size:
                        print(f"ffmpeg output incomplete frame, retry {retry_count}/{max_retries}, {len(raw_frame)}")
                        if retry_count < max_retries:
                            retry_count += 1
                            print(f"ffmpeg output incomplete frame, retry {retry_count}/{max_retries}")
                            time.sleep(0.5)
                            continue
                        else:
                            print("ffmpeg terminated after max retries")
                            break

                    retry_count = 0  # Reset retry count on successful read

                    # Convert raw bytes to numpy array
                    frame_array = np.frombuffer(raw_frame, np.uint8).reshape((self.height, self.width, 3))
                    
                    # Convert BGR to RGB for consistency (optional)
                    frame_rgb = cv2.cvtColor(frame_array, cv2.IMREAD_COLOR)
                    
                    # Encode to JPEG for storage/transmission if needed
                    success, jpeg_bytes = cv2.imencode('.jpg', frame_rgb, [cv2.IMWRITE_JPEG_QUALITY, 95])
                    if not success:
                        print("Failed to encode frame to JPEG")
                        continue

                    # Get frame metadata
                    self.frame_id += 1
                    timestamp = datetime.datetime.now(pytz.timezone('Asia/Singapore'))
                    
                    print(f"Stream yielding frame (raw size: {len(raw_frame)} bytes, JPEG size: {len(jpeg_bytes)})")

                    # Save frame to output folder
                    output_dir = "output_logs"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # Generate filename with timestamp
                    ts_str = timestamp.strftime("%Y-%m-%d_%H-%M-%S_%f")[:-3]
                    filename = os.path.join(output_dir, f"frame_rtsp_{ts_str}.jpg")
                    
                    # Write JPEG bytes to file
                    with open(filename, 'wb') as f:
                        f.write(jpeg_bytes.tobytes())
                    print(f"Saved frame to {filename}")
                    
                    yield jpeg_bytes.tobytes(), timestamp, self.frame_id
                    continue

                except Exception as e:
                    print(f"Error processing frame: {str(e)}")
                    time.sleep(1)

        except Exception as e:
            print(f"Fatal error in stream: {str(e)}")
            return None, None, None
        
        finally:
            self.stop()

    def stop(self):
        self.is_running = False
        if self.proc and self.proc.poll() is None:
            self.proc.terminate()
            try:
                self.proc.wait(timeout=2)
            except subprocess.TimeoutExpired:
                self.proc.kill()
        self.proc = None

    def stream_from_file(self, file_path: str) -> Generator[Tuple[bytes, datetime.datetime, int], None, None]:
        """
        Stream frames from a local video file using FFmpeg with enhanced features.
        
        Args:
            file_path: Path to the video file
            
        Yields:
            Tuple[bytes, datetime.datetime, int]: MJPEG frame data, timestamp, and frame ID
        """
        if self.is_running:
            print("Stream is already running")
            return None, None, None
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return None, None, None
        
        # Check if rtsp_url and fps are set (we'll use the same fps setting)
        if not self.fps:
            print("FPS is not set")
            return None, None, None
        
        print(f"FPS: {self.fps}")

        try:
            # Start FFmpeg process for file input
            self.proc = self._start_ffmpeg_file(file_path, self.fps)
            
            if not self.proc or self.proc.poll() is not None:
                print("Failed to start ffmpeg process for file")
                return None, None, None

            self.is_running = True
            buffer = bytearray()
            retry_count = 0
            max_retries = 3
            self.frame_id = 0

            print(f"Starting file stream from: {file_path} with FPS: {self.fps}")
            
            # Add warm-up period to allow FFmpeg to initialize properly
            print("Warming up FFmpeg process...")
            time.sleep(1.0)  # Wait 1 second for FFmpeg to initialize
            # Read and discard initial data to clear any initialization artifacts
            # initial_chunk = self.proc.stdout.read(65536)
            # if initial_chunk:
            #     print(f"Discarded {len(initial_chunk)} bytes of initialization data")


            while self.is_running:
                try:
                    chunk = self.proc.stdout.read(65536)
                    if not chunk:
                        if retry_count < max_retries:
                            retry_count += 1
                            print(f"ffmpeg output empty, retry {retry_count}/{max_retries}")
                            time.sleep(0.5)
                            continue
                        else:
                            print("ffmpeg terminated after max retries")
                            break

                    buffer.extend(chunk)
                    retry_count = 0  # Reset retry count on successful read

                    while True:
                        start = buffer.find(b"\xff\xd8")   # JPEG SOI
                        end = buffer.find(b"\xff\xd9")     # JPEG EOI
                        if start != -1 and end != -1 and end > start:
                            jpg = buffer[start:end + 2]
                            del buffer[:end + 2]

                            # Decode & validate with OpenCV
                            img = cv2.imdecode(np.frombuffer(jpg, np.uint8), cv2.IMREAD_COLOR)
                            if img is None or self._is_frame_corrupted(img):
                                print("Skipping corrupted frame from file")
                                continue

                            # Get frame metadata
                            self.frame_id += 1
                            timestamp = datetime.datetime.now(pytz.timezone('Asia/Singapore'))
                            
                            yield jpg, timestamp, self.frame_id
                            continue
                        break

                except Exception as e:
                    print(f"Error processing frame from file: {str(e)}")
                    time.sleep(0.5)

        except Exception as e:
            print(f"Fatal error in file stream: {str(e)}")
            return None, None, None
        
        finally:
            self.stop()

    def _start_ffmpeg_file(self, file_path: str, fps: int) -> subprocess.Popen:
        """
        Start FFmpeg process for reading from a local video file.
        
        Args:
            file_path: Path to the video file
            fps: Target FPS for output
            
        Returns:
            subprocess.Popen: FFmpeg process
        """
        try:
            # Enhanced FFmpeg command for file input with better initialization
            ffmpeg_cmd = [
                "ffmpeg", "-hide_banner", "-loglevel", "error",
                "-flags", "low_delay",
                "-avoid_negative_ts", "make_zero",
                "-flush_packets", "1",
                "-i", file_path,
                "-vf", f"fps={fps}",
                "-vcodec", "mjpeg",
                "-q:v", "2",  # Quality setting for MJPEG
                "-f", "image2pipe", 
                "-"
            ]
                
            print(f"FFmpeg file command: {' '.join(ffmpeg_cmd)}")
            
            # Start FFmpeg process with better error handling
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0  # Unbuffered output
            )
            
            # Wait a moment to check if process started successfully
            time.sleep(0.5)
            if process.poll() is not None:
                stderr_output = process.stderr.read().decode() if process.stderr else "Unknown error"
                print(f"FFmpeg failed to start for file: {stderr_output}")
                return None
            
            print("File stream started successfully")
            return process
            
        except Exception as e:
            print(f"File streaming error: {e}")
            return None


if __name__ == "__main__":
    stream = RTSPStream()
    rtsp_url, fps, camera_id = stream.get_camera_rtsp_url()
    
    # Example usage for both RTSP and file streaming
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--file":
        # File streaming mode
        if len(sys.argv) < 3:
            print("Usage: python rtsp_stream.py --file <video_file_path>")
            sys.exit(1)
        
        file_path = sys.argv[2]
        print(f"Starting file stream from: {file_path}")
        
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            for frame, timestamp, frame_id in stream.stream_from_file(file_path):
                now = datetime.datetime.now(pytz.timezone('Asia/Singapore'))
                print(f"Processing Frame {frame_id} at {now.strftime('%Y-%m-%d %H:%M:%S')}")
                output_path = os.path.join(output_dir, f"file_frame_{frame_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg")
                with open(output_path, 'wb') as f:
                    f.write(frame)
        except KeyboardInterrupt:
            print("File stream interrupted by user")
        finally:
            stream.stop()
            print("File stream stopped")
    
    else:
        # RTSP streaming mode (default)
        camera_name = rtsp_url.split("/")[-1]
        if "streaming" in str(rtsp_url).lower():
            camera_name = {
                "101": "cam_one",
                "201": "cam_two",
                "301": "cam_three",
                "401": "cam_four",
                "501": "cam_five",
                "801": "cam_eight",
                "901": "cam_nine",
            }.get(str(camera_name))

        output_dir = "output_logs"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"Starting enhanced FFmpeg-based stream with FPS: {fps}")
        
        try:
            for frame, timestamp, frame_id in stream.stream():
                now = datetime.datetime.now(pytz.timezone('Asia/Singapore'))
                print(f"Processing Frame {frame_id} at {now.strftime('%Y-%m-%d %H:%M:%S')}")
                output_path = os.path.join(output_dir, f"frame_{frame_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg")
                with open(output_path, 'wb') as f:
                    f.write(frame)
        except KeyboardInterrupt:
            print("Stream interrupted by user")
        finally:
            stream.stop()
            print("Stream stopped")