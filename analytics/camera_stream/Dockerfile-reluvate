# Build stage
FROM python:3.11-slim AS builder

# Creating the work directory
WORKDIR /usr/src/camera_stream

# Install build dependencies
RUN apt-get update && \
    apt-get install -y \
    gcc \
    build-essential

# Copy requirements first for better caching
COPY requirements.txt requirements.txt

# Install dependencies
RUN python -m pip install --no-cache-dir -r requirements.txt
RUN python -m pip install --no-cache-dir cryptography

# Copy the application code
COPY . .

# Make build script executable
RUN chmod +x build_exes.sh

# Run the build script to create executables
RUN ./build_exes.sh

# Final stage
FROM python:3.11-slim

# Creating the work directory
WORKDIR /usr/src/camera_stream

# create the ubuntu user
RUN adduser --system --group ubuntu

# Update and upgrade the system packages
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create necessary directories with correct permissions
RUN mkdir -p /nonexistent /usr/src/camera_stream/license && \
    chown -R ubuntu:ubuntu /nonexistent

# Copy only the executable from builder
COPY --from=builder /usr/src/camera_stream/dist/camera_stream /usr/src/camera_stream/

# Copy the license and env directories
COPY license/ /usr/src/camera_stream/license/
COPY env/ /usr/src/camera_stream/env/

# Copy the videos directory
COPY videos/ /usr/src/camera_stream/videos/

# Set permissions
RUN chmod +x /usr/src/camera_stream/camera_stream && \
    chown -R ubuntu:ubuntu /usr/src/camera_stream

RUN mkdir -p /usr/src/camera_stream/output_logs && \
    chmod 777 -R /usr/src/camera_stream/output_logs

# Ensure the backend package is in the Python path
ENV PYTHONPATH=/usr/src/camera_stream

# change to the ubuntu user
USER ubuntu

# Create a startup script that properly handles environment variables
RUN echo '#!/bin/bash\n\
# Load environment variables from ENV_FILE if specified\n\
if [ -n "$ENV_FILE" ] && [ -f "$ENV_FILE" ]; then\n\
    set -a\n\
    source "$ENV_FILE"\n\
    set +a\n\
fi\n\
\n\
# Run the main application with environment variables\n\
sleep 30 && exec ./camera_stream' > /usr/src/camera_stream/start.sh && \
    chmod +x /usr/src/camera_stream/start.sh

CMD ["./start.sh"]