from dotenv import load_dotenv
import os
import sys
import json
from license.license_checker import main as check_license

env_file = os.environ.get('ENV_FILE')
print(f"Loading environment variables from {env_file}")
load_dotenv(env_file)

import os
import requests
import time
import subprocess
import datetime
import pytz
from collections import deque
from logger_setup import setup_logging
import logging
import traceback
import numpy as np
import cv2
import threading
import queue as queue_module
from concurrent.futures import ThreadPoolExecutor, Future
from queue import SimpleQueue
from typing import Generator, Tuple, List, Dict, Any
from db_connection import DatabaseConnection
from redis_connection import CameraCache
from condition_checks import check_condition
from decimal import Decimal
from function_call import call_function

print("Starting camera stream service...")
print(f"Current environment variables:")
print(f"DB_NAME: {os.environ.get('DB_NAME', 'not set')}")
print(f"DB_USER: {os.environ.get('DB_USER', 'not set')}")
print(f"DB_PASSWORD: {'*' * len(os.environ.get('DB_PASSWORD', ''))} (hidden)")
print(f"DB_HOST: {os.environ.get('DB_HOST', 'not set')}")
print(f"DB_PORT: {os.environ.get('DB_PORT', 'not set')}")
print(f"CAMERA_STREAM_URL: {os.environ.get('camera_stream_url', 'not set')}")

# Set the timezone to SGT
os.environ['TZ'] = 'Asia/Singapore'
time.tzset()

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

class BoundedThreadPoolExecutor:
    def __init__(self, max_workers=1, max_queue_size=2):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.queue = deque(maxlen=max_queue_size)  # Use deque with maxlen for automatic dropping
        self.lock = threading.Lock()
        
    def submit(self, fn, *args, **kwargs):
        with self.lock:
            # Create the future but don't submit to executor yet
            future = Future()
            
            # Package the task
            task = (fn, args, kwargs, future)
            
            # Add to queue - if queue is full, leftmost (oldest) item is automatically dropped
            self.queue.append(task)
            
            # Submit the task to the executor
            self._submit_to_executor(task)
            
            return future
    
    def _submit_to_executor(self, task):
        fn, args, kwargs, future = task
        
        # Submit the actual task to the executor
        executor_future = self.executor.submit(fn, *args, **kwargs)
        
        # Chain the result/exception to our future
        def done_callback(f):
            try:
                result = f.result()
                future.set_result(result)
            except Exception as e:
                future.set_exception(e)
            finally:
                # Remove the task from our queue when done
                with self.lock:
                    try:
                        self.queue.remove(task)
                    except ValueError:
                        pass  # Task might have been dropped already
                
        executor_future.add_done_callback(done_callback)
    
    def shutdown(self, wait=True):
        self.executor.shutdown(wait=wait)

class RTSPStream:
    def __init__(self):
        self.proc = None
        self.is_running = False
        self.frame_id = 0
        self.rtsp_url = None
        self.fps = None
        self.camera_id = None
        self.db = DatabaseConnection()
        self.camera_name = None
        
        # Shared frame buffer for producer-consumer pattern
        self.latest_frame = None
        self.frame_lock = threading.Lock()
        self.frame_available = threading.Event()
        
        # Flag to track when FFmpeg is ready
        self.ffmpeg_ready = False
        self.ffmpeg_ready_event = threading.Event()

        # New streaming queue system
        self.STREAM_FPS = 5  # Fixed streaming rate
        self.streaming_queue = None  # Hold 1 second worth of frames
        self.streaming_queue_lock = threading.Lock()
        self.buffer_size = 0
        
        # Analytics results and processing
        self.latest_analytics_result = None
        self.analytics_result_lock = threading.Lock()
        self.analytics_frame_interval = None  # Will be calculated based on self.fps
        self.analytics_pool = BoundedThreadPoolExecutor(max_workers=4, max_queue_size=2)  # Allow 2 tasks max in queue

        # Frame for the VA to run
        self.latest_frame_for_va = None
        self.latest_frame_for_va_lock = threading.Lock()
        self.frame_available_for_va = threading.Event()

        # For unattended object detection
        self.unattended_object_detection_fps = 0.2
        self.unattended_object_detection_frame_interval = None

        # For scaling gantry detection
        self.pose_detection_fps = 5
        self.latest_pose_detection_result = None
        self.pose_detection_result_lock = threading.Lock()
        self.pose_detection_frame_interval = self.STREAM_FPS // self.pose_detection_fps
        
        # Analytics configuration
        self.redis_client = None
        self.layers_config = None
        self.required_analytics = None
        self.camera_name = None
        self.frame_history_queue = deque(maxlen=100)  # For frame history tracking

        self.streaming_redis_client = None

    def get_camera_rtsp_url(self) -> Tuple[str, int, int]:
        """Get camera information from database"""
        camera_name = os.environ.get('camera_stream_url')
        
        rtsp_url, fps, camera_id = self.db.get_camera_info(camera_name)
        if not rtsp_url or not fps or not camera_id:
            raise ValueError(f"Failed to get camera information for: {camera_name}")
        
        self.camera_name = camera_name
            
        # Store the values
        # NOTE: For the self.fps, this will determine the number of seconds lagging behind the real time. So if the fps is 0.5, the streaming will be delayed by 2s.
        self.rtsp_url = rtsp_url
        self.fps = fps
        self.camera_id = camera_id

        print(f"=================== FPS: {self.fps} ===================")

        analytics_interval = int(self.STREAM_FPS / self.fps)  # Convert to integer
        buffer_size = 0
        max_queue_size = min(self.pose_detection_frame_interval, analytics_interval + buffer_size)
        self.streaming_queue = queue_module.Queue(maxsize=max_queue_size)  # Hold frames based on FPS ratio
        self.buffer_size = buffer_size

        print(f"=================== Max queue size: {max_queue_size} ===================")
        print(f"=================== Streaming queue size: {self.streaming_queue.qsize()} ===================")
        print(f"=================== Streaming queue maxsize: {self.streaming_queue.maxsize} ===================")

        # FPS for unattended object detection
        self.unattended_start_frame = int(camera_name[-2:]) * 2
        self.unattended_object_detection_frame_interval = int(Decimal(self.fps) / Decimal(self.unattended_object_detection_fps)) + self.unattended_start_frame

        return rtsp_url, fps, camera_id

    def _start_ffmpeg(self, rtsp_url: str, fps: int) -> subprocess.Popen:
        try:
            logger.info(f"_start_ffmpeg called with rtsp_url={rtsp_url}, fps={fps}")
            
            # FFmpeg command to handle RTSP stream
            # Check if cuda is available
            # Print FFmpeg version
            try:
                version_cmd = ["ffmpeg", "-version"]
                version_output = subprocess.check_output(version_cmd, stderr=subprocess.STDOUT).decode()
                logger.info(f"FFmpeg version output: {version_output}")
                version = version_output.split('\\n')[0]
                logger.info(f"FFmpeg version: {version}")
            except subprocess.CalledProcessError as e:
                logger.warning(f"Could not get FFmpeg version: {e}")

            if "tracks" in str(rtsp_url).lower() and os.environ.get('STREAMING_START_TIME'):
                start_time = os.environ.get('STREAMING_START_TIME')
                logger.info(f"Streaming start time: {start_time}")
                ffmpeg_cmd = [
                    "ffmpeg", "-hide_banner", "-loglevel", "error",
                    "-fflags", "nobuffer+discardcorrupt+genpts",
                    "-avoid_negative_ts", "make_zero",
                    "-flags", "low_delay",
                    "-max_delay", "500000",
                    "-rtsp_transport", "tcp",
                    "-i", f"{rtsp_url}?starttime={start_time}",
                    "-vf", f"fps={fps}",
                    "-vcodec", "mjpeg",
                    "-q:v", "2",  # Quality setting for MJPEG
                    "-f", "image2pipe", "-"
                ]
            else:
                ffmpeg_cmd = [
                    "ffmpeg", "-hide_banner", "-loglevel", "error",
                    "-fflags", "nobuffer+discardcorrupt+genpts",
                    "-avoid_negative_ts", "make_zero",
                    "-flags", "low_delay",
                    "-flush_packets", "1",
                    "-max_delay", "500000",
                    "-rtsp_transport", "tcp", 
                    "-i", rtsp_url,
                    "-vf", f"fps={fps}",
                    "-vcodec", "mjpeg",
                    "-q:v", "2",  # Quality setting for MJPEG
                    "-f", "image2pipe", 
                    "-"
                ]
                
            logger.info(f"FFmpeg command: {' '.join(ffmpeg_cmd)}")
                
            # Start FFmpeg process
            logger.info("Starting FFmpeg subprocess...")
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0  # Unbuffered output
            )

            # Wait a moment to check if process started successfully
            logger.info("Waiting for FFmpeg process to initialize...")
            time.sleep(0.5)
            if process.poll() is not None:
                stderr_output = process.stderr.read().decode() if process.stderr else "Unknown error"
                logger.error(f"FFmpeg failed to start: {stderr_output}")
                return None
            
            logger.info("Stream started successfully")
            return process
            
        except Exception as e:
            logger.error(f"Streaming error in _start_ffmpeg: {e}")
            import traceback
            traceback.print_exc()
            return None

    def stream(self, start_ffmpeg=True) -> Generator[Tuple[bytes, datetime.datetime, int], None, None]:
        """
        Stream MJPEG frames from the camera using OpenCV with corruption checks.
        Args:
            start_ffmpeg: Whether to start the FFmpeg process (False for async mode)
        """
        logger.info(f"Stream method called with start_ffmpeg={start_ffmpeg}, is_running={self.is_running}")
        
        if self.is_running and start_ffmpeg:
            logger.info("Stream is already running")
            return None, None, None

        if not self.rtsp_url or not self.fps:
            logger.info("RTSP URL or FPS is not set")
            return None, None, None
        
        url = self.rtsp_url
        if "tracks" in str(url).lower() and os.environ.get('STREAMING_START_TIME'):
            url = f"{url}?starttime={os.environ.get('STREAMING_START_TIME')}"

        try:
            if start_ffmpeg:
                logger.info("Starting FFmpeg process in stream method...")
                self.proc = self._start_ffmpeg(self.rtsp_url, self.fps)
                
                if not self.proc or self.proc.poll() is not None:
                    logger.error("Failed to start ffmpeg process")
                    return None, None, None

            logger.info("Setting is_running to True in stream method...")
            self.is_running = True
            buffer = bytearray()
            retry_count = 0
            max_retries = 3

            logger.info("Starting frame reading loop...")
            while self.is_running:
                try:
                    chunk = self.proc.stdout.read(65536)
                    if not chunk:
                        if retry_count < max_retries:
                            retry_count += 1
                            logger.warning(f"ffmpeg output empty, retry {retry_count}/{max_retries}")
                            time.sleep(0.5)
                            continue
                        else:
                            logger.error("ffmpeg terminated after max retries")
                            break

                    buffer.extend(chunk)
                    retry_count = 0  # Reset retry count on successful read

                    while True:
                        start = buffer.find(b"\xff\xd8")   # JPEG SOI
                        end = buffer.find(b"\xff\xd9")     # JPEG EOI
                        if start != -1 and end != -1 and end > start:
                            jpg = buffer[start:end + 2]
                            del buffer[:end + 2]

                            # Get frame metadata
                            self.frame_id += 1
                            timestamp = datetime.datetime.now(pytz.timezone('Asia/Singapore'))
                            
                            logger.debug(f"Stream yielding frame {self.frame_id}")
                            yield jpg, timestamp, self.frame_id
                            continue
                        break

                except Exception as e:
                    logger.error(f"Error processing frame: {str(e)}")
                    time.sleep(0.5)
                
        except Exception as e:
            logger.error(f"Fatal error in stream: {str(e)}")
            return None, None, None
        
        finally:
            if start_ffmpeg:
                logger.info("Stream method calling stop() in finally block")
                self.stop()

    def stop(self):
        """Stop the streaming process"""
        logger.info(f"Stop method called. Current is_running={self.is_running}, proc={self.proc}")
        import traceback
        logger.info(f"Stop method called from: {traceback.format_stack()[-2]}")
        
        self.is_running = False
        if self.proc:
            logger.info("Terminating FFmpeg process...")
            self.proc.terminate()
            self.proc.wait()
            self.proc = None
        logger.info("Stream stopped")

        # Exit with code 1 to signal Docker to restart the container
        sys.exit(1)

    def get_camera_layers_config(self, camera_id: int) -> List[Dict[str, Any]]:
        """
        Get camera layers configuration from the database
        Args:
            camera_id: ID of the camera to query
        Returns:
            List[Dict[str, Any]]: List of layer configurations sorted by layer_type, containing:
                - layer_id: UUID of the layer
                - layer_name: Name of the layer
                - layer_type: Type of the layer (1, 2, or 3)
                - configuration: Layer-specific configuration dictionary
                - function_name: Name of the function to execute
                - enabled: Whether the layer is enabled
        """
        logger.info(f"Getting camera layers configuration for camera {camera_id}")
        return self.db.get_camera_layers_config(camera_id)
    
    def save_camera_event(self, response: list, img_bytes: bytes, camera_id: str, timestamp: str, redis_client=None):
        """
        Save camera event to Redis queue for batch processing
        Args:
            response: List of detection results
            img_bytes: Bytes of the image
            camera_id: UUID of the camera
            timestamp: Timestamp of the event
            redis_client: Redis client instance
        """
        # Create a unique key for this event
        event_key = redis_client.build_key(camera_id, timestamp, "event")
        
        # Prepare data for Redis
        event_data = {
            "response": response,
            "img_bytes": redis_client.to_jsonable_bytes(img_bytes),
            "camera_id": camera_id,
            "timestamp": timestamp,
            # "frame_id": frame_id
        }
        
        # Push to Redis queue for batch processing
        logger.info(f"Pushing event to Redis queue: {event_key}")
        redis_client.set(event_key, event_data)
        
        # Add to a processing list for batch processor
        self._redis_list_push(redis_client, "events_to_process", event_key)
    
    def _redis_list_push(self, redis_client, list_name, value):
        """
        Helper method to push a value to a Redis list
        """
        try:
            redis_client._redis.lpush(list_name, value)
        except Exception as e:
            logger.error(f"Error pushing to Redis list {list_name}: {e}")

    def _start_ffmpeg_file(self, file_path: str, fps: int) -> subprocess.Popen:
        """
        Start FFmpeg process for reading from a local video file.
        
        Args:
            file_path: Path to the video file
            fps: Target FPS for output. For fps < 1, this will select 1 frame every 1/fps seconds
            
        Returns:
            subprocess.Popen: FFmpeg process
        """
        try:
            # Always use STREAM_FPS for the actual streaming rate
            filter_str = f"fps={self.STREAM_FPS}"

            ffmpeg_cmd = [
                "ffmpeg", "-hide_banner", "-loglevel", "error",
                "-re",                       # ⬅️  read the file in real-time
                "-i", file_path,             #     (must come just *before* the input)
                "-vf", filter_str,              # Set output FPS to streaming FPS
                "-vcodec", "mjpeg",
                "-q:v", "2",                 # MJPEG quality (lower = better)
                "-flush_packets", "1",       # write each JPEG as soon as it's ready
                "-f", "image2pipe",
                "-"
            ]
                
            print(f"FFmpeg file command: {' '.join(ffmpeg_cmd)}")
            
            # Start FFmpeg process with better error handling
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0  # Unbuffered output
            )
            
            # Wait a moment to check if process started successfully
            time.sleep(0.5)
            if process.poll() is not None:
                stderr_output = process.stderr.read().decode() if process.stderr else "Unknown error"
                print(f"FFmpeg failed to start for file: {stderr_output}")
                return None
            
            print("File stream started successfully")
            return process
            
        except Exception as e:
            print(f"File streaming error: {e}")
            return None
    
    def stream_from_file(self, file_path: str) -> Generator[Tuple[bytes, datetime.datetime, int], None, None]:
        """
        Read frames from a local video file in a loop using OpenCV.
        """
        logger.info(f"Starting stream_from_file with path: {file_path}")

        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return None, None, None
        
        # Check if rtsp_url and fps are set (we'll use the same fps setting)
        if not self.fps:
            print("FPS is not set")
            return None, None, None
        
        print(f"FPS: {self.fps}")
            
        logger.info(f"File exists, size: {os.path.getsize(file_path)} bytes")
        
        try:
            # Start FFmpeg process for file input
            self.proc = self._start_ffmpeg_file(file_path, self.fps)
            
            if not self.proc or self.proc.poll() is not None:
                print("Failed to start ffmpeg process for file")
                return None, None, None

            self.is_running = True
            buffer = bytearray()
            retry_count = 0
            max_retries = 3
            self.frame_id = 0

            print(f"Starting file stream from: {file_path} with FPS: {self.fps}")
            
            # Add warm-up period to allow FFmpeg to initialize properly
            print("Warming up FFmpeg process...")
            time.sleep(1.0)  # Wait 1 second for FFmpeg to initialize

            while self.is_running:
                try:
                    chunk = self.proc.stdout.read(65536)
                    if not chunk:
                        if retry_count < max_retries:
                            retry_count += 1
                            print(f"ffmpeg output empty, retry {retry_count}/{max_retries}")
                            time.sleep(0.5)
                            continue
                        else:
                            print("ffmpeg terminated after max retries")
                            break

                    buffer.extend(chunk)
                    retry_count = 0  # Reset retry count on successful read

                    while True:
                        start = buffer.find(b"\xff\xd8")   # JPEG SOI
                        end = buffer.find(b"\xff\xd9")     # JPEG EOI
                        if start != -1 and end != -1 and end > start:
                            jpg = buffer[start:end + 2]
                            del buffer[:end + 2]

                            timestamp = datetime.datetime.now(pytz.timezone('Asia/Singapore'))

                            # Increment frame counter after successful write
                            self.frame_id += 1

                            print(f"Frame {self.frame_id} at {timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                            
                            yield jpg, timestamp, self.frame_id
                            continue
                        break

                except Exception as e:
                    print(f"Error processing frame from file: {str(e)}")
                    traceback.print_exc()
                    time.sleep(0.5)

        except Exception as e:
            print(f"Fatal error in file stream: {str(e)}")
            return None, None, None
        
        finally:
            self.stop()

    def process_frame_async(self, frame_data, redis_client, layers_config, required_analytics, camera_name, queue, for_pose_detection=False):
        """
        Process a single frame asynchronously
        Args:
            frame_data: Tuple of (frame, timestamp, frame_id)
            redis_client: Redis client instance
            layers_config: Camera layers configuration
            required_analytics: Whether analytics are required
            camera_name: Camera name
            queue: Frame history queue
            for_pose_detection: Whether pose detection is required
        """
        frame, timestamp, frame_id = frame_data
        
        try:
            # Here you can do your frame processing
            ts_str = timestamp.strftime("%Y-%m-%d %I:%M:%S.%f")
            frame_time = timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            preprocessing_time = None
            prediction_time = None
            logger.info(f"Processing frame {frame_id} at {ts_str}")

            # Save the frame to Redis
            frame_key = redis_client.build_key(self.camera_id, ts_str, None)
            logger.info(f"Frame key from camera stream: {frame_key}")

            # Convert frame to JSON-friendly bytes
            image_bytes = redis_client.to_jsonable_bytes(frame)
            payload_to_redis = {
                "frame": image_bytes,
                "timestamp": ts_str,
                "frame_id": frame_id,
                "camera_id": self.camera_id,
            }
            redis_client.set(frame_key, payload_to_redis, ttl=360)
            queue.append(frame_key)

            if required_analytics:
                final_results = None
                all_layers_results = {}
                all_layers_results_by_name = {}
                bounding_boxes_results = {}
                cardinal_requirements = {}
                latest_camera_layer_config_id = None
                
                # Add your frame processing logic here
                # For each layer in the layers_config, process the frame
                for layer in layers_config:
                    
                    # Only process pose detection layer if for_pose_detection is True
                    if for_pose_detection and layer.get("layer_name") != "Pose Detection":
                        continue

                    # Only process other analytics layers except for pose detection
                    elif not for_pose_detection and layer.get("layer_name") == "Pose Detection":
                        continue

                    # Only process unattended object detection layer if frame_id is divisible by the frame interval 
                    elif not for_pose_detection and layer.get("layer_name") == "Unattended Object Detection" and int(frame_id % self.unattended_object_detection_frame_interval) != 0:
                        continue

                    logger.info(f"Processing layer: {layer.get('layer_name')}")

                    latest_camera_layer_config_id = layer.get("layer_configuration_id")

                    # Check if there is a dependency for the layer
                    if layer.get("dependencies", []):
                        # For loop through the dependencies
                        for dependency in layer.get("dependencies"):
                            # Dependency contains a tuple of (dependency_layer_id, conditions)
                            dependency_layer_id, conditions = dependency
                            logger.info(f"Checking for dependency id: {dependency_layer_id}")
                            # Check if the dependency is in the all_layers_results
                            if conditions and all_layers_results.get(dependency_layer_id) and not check_condition(conditions, all_layers_results[dependency_layer_id]):
                                logger.info(f"Dependency {dependency_layer_id} failed")
                                break

                    frame_history = None
                    history_frame_ids = None
                    layer_configuration = {}
                    # Check if the layer configuration requires a frame history
                    if 'configuration' in layer and layer.get("configuration", {}):
                        if isinstance(layer.get("configuration"), str):
                            layer_configuration = json.loads(layer.get("configuration"))
                        else:
                            layer_configuration = layer.get("configuration")

                    if 'frame_history' in layer_configuration and str(layer_configuration['frame_history']).lower() == "true":
                        # Get the frame history
                        frame_history = queue.copy()
                        # Extract frame IDs in FIFO order (oldest to newest)
                        history_frame_ids = list(frame_history)
                        logger.info(f"Frame history IDs (oldest to newest): {history_frame_ids}")

                    dependency_result = {}
                    if 'extract_from_dependency' in layer_configuration.keys() and layer_configuration['extract_from_dependency']:
                        for dependency in layer_configuration['extract_from_dependency']:
                            dependency_result[dependency] = all_layers_results_by_name[dependency]
                        
                    # Proceed to run the layer
                    # Call the model service to detect objects
                    logger.info(f"Calling model service to detect objects for frame {frame_id}")
                    try:
                        layer_results = call_function(layer.get("function_name"), layer, frame_key, self.camera_id, ts_str, frame_id, history_frame_ids, camera_name=camera_name, dependency_result=dependency_result, fps=self.unattended_object_detection_fps)
                        results = layer_results.get("result")

                        logger.info(f"Layer name: {layer.get('layer_name')} for frame {frame_id} with layer results: {results}")

                        if isinstance(results, dict) and "preprocessing_time" in results.keys():
                            preprocessing_time = results.get("preprocessing_time")
                            results = results.get("result")

                        # Store the config layer to the all_layers_results
                        all_layers_results[layer.get("layer_configuration_id")] = results
                        all_layers_results_by_name[layer.get("layer_name")] = results

                        # Check if it is the final results
                        if results and isinstance(results, dict) and "cardinal_requirements" in results.keys() and results.get("final_results"):
                            # Need to add the cardinal requirements to the cardinal_requirements dictionary
                            cardinal_requirements[results.get("cardinal_requirements")] = results.get("final_results")
                            
                    except requests.exceptions.Timeout:
                        logger.error(f"Timeout while waiting for model service response for frame {frame_id}")
                    except requests.exceptions.ConnectionError as e:
                        logger.error(f"Connection error with model service for frame {frame_id}: {str(e)}")
                    except requests.exceptions.RequestException as e:
                        logger.error(f"Error calling model service for frame {frame_id}: {str(e)}")
                    except Exception as e:
                        logger.error(f"Unexpected error processing model service response for frame {frame_id}: {str(e)}")
                        import traceback
                        traceback.print_exc()
                    continue  # Skip to next layer

                # Check if there is any cardinal requirements
                final_frame_key = redis_client.build_key(self.camera_id, ts_str, "final_result")
                prediction_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                # If there is any cardinal requirements, we will need to overlay the frame with the final results
                if cardinal_requirements:
                    logger.info(f"Calling model service to overlay the box and alerts for frame {frame_id}")
                    # Get the model service to overlay the box and alerts
                    layer_results = call_function(
                        "overlay_box_and_alerts", 
                        {
                            "function_name": "overlay_box_and_alerts",
                        }, 
                        frame_key, 
                        self.camera_id,
                        ts_str, 
                        final_frame_key,
                        frame_history=None,
                        dependency_result=None,
                        cardinal_requirements=cardinal_requirements,
                        camera_layer_config_id=latest_camera_layer_config_id,
                        frame_time=frame_time,
                        preprocessing_time=preprocessing_time,
                        prediction_time=prediction_time)
                    
                    logger.info(f"Overlay box and alerts results: {layer_results}")

                    detection_result = layer_results.get("result", {})
                    
                    # Store the final analytics result
                    if for_pose_detection:
                        self.set_latest_pose_detection_result({"final_result": detection_result})
                    else:
                        self.set_latest_analytics_result({"final_result": detection_result})
                else:
                    # No overlays needed, store original frame as result
                    if for_pose_detection:
                        self.set_latest_pose_detection_result({"final_result": {}})
                    else:
                        self.set_latest_analytics_result({"final_result": {}})

        except Exception as e:
            logger.error(f"Error processing frame {frame_id}: {e}")
            import traceback
            traceback.print_exc()

    def set_latest_frame_for_va(self, frame_data):
        """
        Set the latest frame for the VA to run
        """
        with self.latest_frame_for_va_lock:
            self.latest_frame_for_va = frame_data
            self.frame_available_for_va.set()  # Signal that a frame is available

    def get_latest_frame_for_va(self, timeout=None):
        """
        Get the latest frame for the VA to run
        """
        if self.frame_available_for_va.wait(timeout):
            with self.latest_frame_for_va_lock:
                if self.latest_frame_for_va is not None:
                    frame_data = self.latest_frame_for_va
                    self.latest_frame_for_va = None
                    self.frame_available_for_va.clear()
                    return frame_data
        return None
    
    def get_latest_frame(self, timeout=None):
        """
        Get the latest frame from the shared buffer (consumer)
        Args:
            timeout: Timeout in seconds to wait for a frame
        Returns:
            Tuple of (frame, timestamp, frame_id) or None if timeout
        """
        if self.frame_available.wait(timeout):
            with self.frame_lock:
                if self.latest_frame is not None:
                    frame_data = self.latest_frame
                    self.latest_frame = None  # Clear the buffer
                    self.frame_available.clear()  # Reset the event
                    return frame_data
        return None
        
    def set_latest_frame(self, frame_data):
        """
        Set the latest frame in the shared buffer (producer)
        Args:
            frame_data: Tuple of (frame, timestamp, frame_id)
        """
        with self.frame_lock:
            self.latest_frame = frame_data
            self.frame_available.set()  # Signal that a frame is available

    def set_latest_pose_detection_result(self, result):
        """Store the latest pose detection result safely"""
        with self.pose_detection_result_lock:
            self.latest_pose_detection_result = result
            
    def get_latest_pose_detection_result(self):
        """Get the latest pose detection result safely"""
        with self.pose_detection_result_lock:
            return self.latest_pose_detection_result

    def start_async_frame_producer(self, redis_client=None, layers_config=None, required_analytics=None, camera_name=None, required_pose_detection=False):
        """
        Start the async frame producer that continuously reads frames from FFmpeg
        and updates the shared buffer with the latest frame.
        Also handles analytics processing for selected frames.
        """
        # Store analytics configuration
        self.redis_client = redis_client
        self.layers_config = layers_config
        self.required_analytics = required_analytics
        self.camera_name = camera_name
        
        # Calculate analytics frame interval
        if self.fps and self.required_analytics:
            self.analytics_frame_interval = self.STREAM_FPS // self.fps if self.fps < self.STREAM_FPS else 1
            logger.info(f"Analytics will process 1 frame every {self.analytics_frame_interval} frames")

        # Determine the FPS of the pose detection. (Minimally it should be 1fps. Max it should be 5fps)
        if required_pose_detection:           
            # Calculate frame interval based on stream fps and pose detection fps
            self.pose_detection_frame_interval = self.STREAM_FPS // self.pose_detection_fps
            
            # Ensure we process at least 1 frame
            self.pose_detection_frame_interval = max(1, self.pose_detection_frame_interval)
            
            logger.info(f"Pose detection will run at {self.pose_detection_fps} FPS " 
                       f"(1 frame every {self.pose_detection_frame_interval} frames)")

        def producer_loop():
            try:
                logger.info("Producer loop starting...")
                
                # Start FFmpeg process first
                if '.mp4' in self.rtsp_url:
                    logger.info("Starting FFmpeg for file input...")
                    self.proc = self._start_ffmpeg_file(f"videos/{self.rtsp_url}", self.STREAM_FPS)  # Use fixed stream FPS
                else:
                    logger.info("Starting FFmpeg for RTSP input...")
                    self.proc = self._start_ffmpeg(self.rtsp_url, self.STREAM_FPS)  # Use fixed stream FPS
                
                logger.info(f"FFmpeg process started: {self.proc}")
                
                if not self.proc or self.proc.poll() is not None:
                    logger.error("Failed to start FFmpeg process in producer")
                    self.set_ffmpeg_ready(False)  # Signal that FFmpeg failed
                    return
                
                logger.info("Setting is_running to True...")
                self.is_running = True
                
                # Set FFmpeg as ready since it started successfully
                logger.info("Setting FFmpeg as ready...")
                self.set_ffmpeg_ready(True)
                
                # Now call stream with start_ffmpeg=False since we already started it
                stream_generator = self.stream_from_file(f"videos/{self.rtsp_url}") if '.mp4' in self.rtsp_url else self.stream(start_ffmpeg=False)
                
                for frame, timestamp, frame_id in stream_generator:
                    if not self.is_running:
                        logger.info("Producer loop stopping due to is_running=False")
                        break
                        
                    # Check if this frame needs analytics
                    needs_analytics = self.required_analytics and int(frame_id % self.analytics_frame_interval) == 0
                    print(f"=================== Needs analytics: {needs_analytics} for frame {frame_id} ===================")

                    needs_pose_detection = required_pose_detection and int(frame_id % self.pose_detection_frame_interval) == 0
                    print(f"=================== Needs pose detection: {needs_pose_detection} for frame {frame_id} ===================")

                    # Set frame for VA processing
                    if needs_analytics:
                        print(f"=================== Setting frame {frame_id} for analytics ===================")
                        self.set_latest_frame_for_va((frame, timestamp, frame_id))

                    if needs_pose_detection:
                        print(f"=================== Processing frame {frame_id} for pose detection ===================")
                        # Start analytics processing in a new thread
                        self.analytics_pool.submit(
                            self.process_frame_async,
                            (frame, timestamp, frame_id),
                            self.redis_client,
                            self.layers_config,
                            True,  # required_analytics
                            self.camera_name,
                            self.frame_history_queue,
                            for_pose_detection=True
                        )
                    
                    # if needs_analytics:
                    #     print(f"=================== Processing frame {frame_id} for analytics ===================")
                    #     # Add the frame to va_latest_frame
                    #     self.set_latest_frame_for_va((frame, timestamp, frame_id))
                        
                    #     # Start analytics processing in a new thread
                    #     self.analytics_pool.submit(
                    #         self.process_frame_async,
                    #         (frame, timestamp, frame_id),
                    #         self.redis_client,
                    #         self.layers_config,
                    #         True,  # required_analytics
                    #         self.camera_name,
                    #         self.frame_history_queue
                    #     )

                    print(f"=================== Streaming queue size: {self.streaming_queue.qsize()} ===================")
                    print(f"=================== Buffer size: {self.buffer_size} ===================")

                    # If the queue is a new queue, set the buffer frame
                    if self.streaming_queue.qsize() == 0 and self.buffer_size > 0:  # Add check for positive buffer size
                        for i in range(int(self.buffer_size)):  # Ensure integer for range()
                            self.streaming_queue.put_nowait((frame, timestamp, frame_id))

                    print(f"=================== Streaming queue size: {self.streaming_queue.qsize()} ===================")
                    print(f"=================== Buffer size: {self.buffer_size} ===================")
                    
                    # Add frame to streaming queue
                    try:
                        # If queue is full, remove oldest frame
                        print(f"=================== Streaming the frame to the queue for frame {frame_id} ===================")
                        if self.streaming_queue.full():
                            self.streaming_queue.get_nowait()
                        self.streaming_queue.put_nowait((frame, timestamp, frame_id))
                    except queue_module.Full:
                        logger.warning("Streaming queue full, dropping oldest frame")
                        try:
                            self.streaming_queue.get_nowait()
                            self.streaming_queue.put_nowait((frame, timestamp, frame_id))
                        except (queue_module.Full, queue_module.Empty):
                            pass
                            
                logger.info("Producer loop finished normally")
                
            except Exception as e:
                logger.error(f"Error in frame producer: {e}")
                import traceback
                traceback.print_exc()
                self.set_ffmpeg_ready(False)  # Signal that FFmpeg failed
        
        # Start the producer thread
        producer_thread = threading.Thread(target=producer_loop, daemon=True)
        producer_thread.start()
        logger.info("Async frame producer started")


        if self.required_analytics:
            # Start the VA consumer thread
            def va_consumer_loop():
                try:
                    logger.info("VA consumer loop starting...")
                    
                    while True:  # Add continuous loop
                        try:
                            # Get the latest frame for VA if available
                            frame_data = self.get_latest_frame_for_va(timeout=1.0)
                            if frame_data is not None:
                                print(f"=================== Processing frame for analytics ===================")
                                # Process the frame with VA
                                self.analytics_pool.submit(
                                    self.process_frame_async,
                                    frame_data,
                                    self.redis_client,
                                    self.layers_config,
                                    True,  # required_analytics
                                    self.camera_name,
                                    self.frame_history_queue
                                )
                            time.sleep(0.1)  # Small sleep to prevent tight loop
                        except Exception as e:
                            logger.error(f"Error processing frame in VA consumer loop: {e}")
                            import traceback
                            traceback.print_exc()
                            # Continue the loop even if there's an error processing one frame
                        continue

                except Exception as e:
                    logger.error(f"Fatal error in VA consumer loop: {e}")
                    import traceback
                    traceback.print_exc()

            va_consumer_thread = threading.Thread(target=va_consumer_loop, daemon=True)
            va_consumer_thread.start()
            logger.info("VA consumer thread started")

        return producer_thread

    def set_ffmpeg_ready(self, ready=True):
        """
        Set the FFmpeg ready flag
        Args:
            ready: Whether FFmpeg is ready
        """
        self.ffmpeg_ready = ready
        if ready:
            self.ffmpeg_ready_event.set()
        else:
            self.ffmpeg_ready_event.clear()

    def wait_for_ffmpeg_ready(self, timeout=None):
        """
        Wait for FFmpeg to be ready
        Args:
            timeout: Timeout in seconds
        Returns:
            bool: True if FFmpeg is ready, False if timeout
        """
        return self.ffmpeg_ready_event.wait(timeout)

    def set_latest_analytics_result(self, result):
        """Store the latest analytics result safely"""
        with self.analytics_result_lock:
            self.latest_analytics_result = result
            
    def get_latest_analytics_result(self):
        """Get the latest analytics result safely"""
        with self.analytics_result_lock:
            return self.latest_analytics_result

def main_producer_consumer():
    """
    Main function using producer-consumer pattern:
    - FFmpeg runs asynchronously and continuously updates streaming queue at 10fps
    - Analytics run at configured fps on selected frames
    - Main processing loop consumes frames from queue and applies latest analytics results
    """
    # First check the license
    try:
        check_license()
    except SystemExit as e:
        if e.code != 0:
            logger.error("License check failed. Exiting...")
            raise
        logger.info("License check passed.")
    except Exception as e:
        logger.error(f"License check error: {e}")
        raise

    try:
        stream = RTSPStream()

        # Get camera configuration
        rtsp_url, fps, camera_id = stream.get_camera_rtsp_url()
        camera_name = rtsp_url.split("/")[-1]
        camera_name = {
            "101": "cam_one",
            "201": "cam_two",
            "301": "cam_three",
            "401": "cam_four",
            "501": "cam_five",
            "801": "cam_eight",
            "901": "cam_nine",
            "video_one.mp4": "cam_one",
            "video_two.mp4": "cam_two", 
            "video_three.mp4": "cam_three",
            "video_four.mp4": "cam_four",
            "video_five.mp4": "cam_five",
            "video_eight.mp4": "cam_eight",
            "video_nine.mp4": "cam_nine",
        }.get(str(camera_name), str(camera_name))

        # Start Redis connection
        redis_client = CameraCache(camera_id=camera_id)
        streaming_redis_client = CameraCache(camera_id=camera_id, host="streaming_redis")

        # Get the camera layers configuration
        layers_config, required_analytics, required_pose_detection = stream.get_camera_layers_config(camera_id)
        logger.info(f"Camera layers configuration: {layers_config}")
        logger.info(f"rtsp_url: {rtsp_url}")
        
        # Start the async frame producer
        producer_thread = stream.start_async_frame_producer(redis_client, layers_config, required_analytics, camera_name, required_pose_detection)
        
        logger.info("Starting consumer loop - waiting for frames...")

        # Wait for FFmpeg to be ready (can take 3-10 seconds for RTSP connection)
        logger.info("Waiting for FFmpeg to be ready...")
        if not stream.wait_for_ffmpeg_ready(timeout=15.0):  # Wait up to 15 seconds
            logger.error("FFmpeg failed to become ready within timeout")
            stream.stop()
            return
        
        logger.info("FFmpeg is ready, starting consumer loop...")
        
        # Consumer loop - process frames as they become available
        while stream.is_running:
            try:
                # Wait for streaming queue to fill up
                while stream.is_running and stream.streaming_queue.qsize() < stream.streaming_queue.maxsize:
                    time.sleep(0.1)
                logger.info("Streaming queue is full, starting frame processing...")

                # Get frame from streaming queue
                try:
                    frame_data = stream.streaming_queue.get(timeout=5.0)
                    print(f"Got frame data from queue with length: {len(frame_data)}")
                except queue_module.Empty:
                    logger.debug("Consumer loop: timeout occurred, no frame available")
                    print("Queue timeout - no frame available")
                    if not stream.is_running:
                        logger.info("Consumer loop: stream is no longer running, breaking")
                        print("Stream no longer running, breaking consumer loop")
                        break
                
                frame, timestamp, frame_id = frame_data
                ts_str = timestamp.strftime("%Y-%m-%d %I:%M:%S.%f")
                print(f"Processing frame {frame_id} from timestamp {ts_str}")
                print(f"=================== Reading the frame from the queue for frame {frame_id} ===================")
                
                # Get latest analytics result if available
                latest_result = []
                with stream.analytics_result_lock:
                    latest_result = stream.latest_analytics_result
                    logger.info(f"Latest result: {latest_result}")
                    print(f"Got latest analytics result: {latest_result}")
                    if latest_result:
                        latest_result = latest_result.get("final_result", {}).get("result", [])
                        print(f"Extracted result data: {latest_result}")

                # Get latest pose detection result if available
                latest_pose_detection_result = []
                with stream.pose_detection_result_lock:
                    latest_pose_detection_result = stream.latest_pose_detection_result
                    logger.info(f"Latest pose detection result: {latest_pose_detection_result}")
                    print(f"Got latest pose detection result: {latest_pose_detection_result}")
                    if latest_pose_detection_result:
                        latest_pose_detection_result = latest_pose_detection_result.get("final_result", {}).get("result", [])
                        print(f"Extracted pose detection result data: {latest_pose_detection_result}")

                # Combine the latest analytics result and pose detection result
                if latest_pose_detection_result:
                    latest_result.extend(latest_pose_detection_result)
                
                # Save frame to Redis
                # frame_key = redis_client.build_key(camera_id, ts_str, None)
                # image_bytes = redis_client.to_jsonable_bytes(frame)

                # Overlay the box and alerts
                logger.info(f"Attempting to decode frame bytes of length: {len(frame)}")
                print(f"Decoding frame bytes of length: {len(frame)}")
                nparr = np.frombuffer(frame, np.uint8)
                frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                print(f"Decoded frame shape: {frame.shape}")

                color_map = {
                    "normal": (0, 255, 0),    # Green
                    "warning": (0, 165, 255), # Amber
                    "critical": (0, 0, 255), # Red
                }
                
                if latest_result:
                    print(f"Processing {len(latest_result)} detections")
                    for detection in latest_result:
                        category = detection.get("category", "unknown")
                        confidence = detection.get("confidence", 0.0)
                        bbox = detection.get("original_bbox", [])

                        logger.info(f"Processing alert - State: {category}, Confidence: {confidence}")
                        print(f"Drawing detection - Category: {category}, Confidence: {confidence:.2f}")
                        x1, y1 = int(bbox[0]), int(bbox[1])
                        x2, y2 = int(bbox[2]), int(bbox[3])
                        logger.info(f"Drawing box at coordinates: ({x1}, {y1}) to ({x2}, {y2})")
                        print(f"Box coordinates: ({x1}, {y1}) to ({x2}, {y2})")

                        # No alert here as it will be from L2
                        if category == "scaling_gantry":
                            if confidence > 0.5:
                                color = color_map.get("critical", color_map["critical"])
                            elif confidence > 0.0:
                                color = color_map.get("warning", color_map["warning"])
                            else:
                                color = color_map.get("normal", color_map["normal"])
                        else:
                            color = color_map.get("normal", color_map["normal"])
                            
                        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                        text_y = y1 - 10 if (y1 - 10) > 10 else y1 + 20
                        cv2.putText(frame, category, (x1, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)

                # Save the frame to the redis cache
                logger.info("Encoding final frame and caching to Redis")
                print("Encoding processed frame for Redis")
                # Convert the frame to bytes for sending in the alert
                _, img_encoded = cv2.imencode(".jpg", frame)
                final_frame_bytes = img_encoded.tobytes()
                logger.info("Successfully encoded frame for alert")
                print(f"Encoded frame size: {len(final_frame_bytes)} bytes")



                # # Save frame to output logs folder for debugging/monitoring
                # try:
                #     output_dir = 'output_logs'
                #     os.makedirs(output_dir, exist_ok=True)
                    
                #     # Generate filename with frame's original timestamp
                #     output_path = os.path.join(output_dir, f'frame_{ts_str.replace(" ", "_").replace(":", "-").replace(".", "_")}.jpg')
                    
                #     # Write the encoded frame bytes directly to file
                #     with open(output_path, 'wb') as f:
                #         f.write(final_frame_bytes)
                    
                #     logger.info(f"Saved debug frame to {output_path}")
                #     print(f"Saved frame to output logs: {output_path}")
                # except Exception as e:
                #     logger.error(f"Failed to save debug frame: {e}")
                #     print(f"Error saving frame to output logs: {str(e)}")



                final_frame_base64 = streaming_redis_client.to_jsonable_bytes(final_frame_bytes)
                final_frame_key = streaming_redis_client.build_key(camera_id, ts_str, "final_result")
                print(f"Generated Redis key: {final_frame_key}")

                metadata = {
                    "final_result": final_frame_base64,
                    "has_alerts": False,
                    "processed_at": datetime.datetime.now().isoformat()
                }
                print(f"Saving metadata to Redis with key {final_frame_key}")

                # Save metadata to JSON file
                output_dir = f'/frame/streaming/{camera_id}'
                os.makedirs(output_dir, exist_ok=True)
                temp_output_path = os.path.join(output_dir, 'streaming_final_result_temp.json')
                output_path = os.path.join(output_dir, 'streaming_final_result.json')
                
                with open(temp_output_path, 'w') as f:
                    json.dump(metadata, f, indent=4)
                print(f"Saved metadata to {output_path}")

                # Rename the file as the process is atomic
                os.rename(temp_output_path, output_path)
                
                # # Apply latest analytics result if available
                # streaming_redis_client.set(final_frame_key, metadata, ttl=300)
                # print("Successfully saved to Redis")
                
                # Publish frame
                streaming_redis_client.publish_latest_frame(final_frame_key, camera_id)
                print(f"Published frame {frame_id} to Redis channel at {camera_id}")
                print(f"=================== Published frame {frame_id} to Redis channel at {camera_id} ===================")
                
            except Exception as e:
                logger.error(f"Error in consumer loop: {e}")
                print(f"ERROR in consumer loop: {str(e)}")
                import traceback
                traceback.print_exc()
                print("Full traceback printed above")
                continue
                    
    except KeyboardInterrupt:
        logger.error("\nStopping stream...")

    except Exception as e:
        logger.error(f"Exception in main_producer_consumer: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
    finally:
        logger.info("main_producer_consumer finally block - calling stream.stop()")
        stream.stop()


def main():
    """
    Original main function with synchronous processing (may cause timing drift)
    """
    # First check the license
    try:
        check_license()
    except SystemExit as e:
        if e.code != 0:
            logger.error("License check failed. Exiting...")
            raise
        logger.info("License check passed.")
    except Exception as e:
        logger.error(f"License check error: {e}")
        raise

    try:
        stream = RTSPStream()

        rtsp_url, fps, camera_id = stream.get_camera_rtsp_url()
        camera_name = rtsp_url.split("/")[-1]
        if "tracks" in str(rtsp_url).lower():
            camera_name = {
                "101": "cam_one",
                "201": "cam_two",
                "301": "cam_three",
                "401": "cam_four",
                "501": "cam_five",
                "801": "cam_eight",
                "901": "cam_nine",
            }.get(str(camera_name))

        # Start Redis connection
        redis_client = CameraCache(camera_id=camera_id)

        # Get the camera layers configuration
        layers_config, required_analytics = stream.get_camera_layers_config(camera_id)
        logger.info(f"Camera layers configuration: {layers_config}")
        logger.info(f"rtsp_url: {rtsp_url}")

        # Process frames from the stream
        queue = deque(maxlen=100) # To create a queue of past 100 frames
        if '.mp4' in rtsp_url:
            for frame, timestamp, frame_id in stream.stream_from_file(f"videos/{rtsp_url}"):
                try:
                    # Here you can do your frame processing
                    ts_str = timestamp.strftime("%Y-%m-%d %I:%M:%S.%f")
                    frame_time = timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    preprocessing_time = None
                    prediction_time = None
                    logger.info(f"Processing frame {frame_id} at {ts_str}")

                    # Save the frame to Redis
                    frame_key = redis_client.build_key(camera_id, ts_str, None)
                    logger.info(f"Frame key from camera stream: {frame_key}")

                    # Convert frame to JSON-friendly bytes
                    image_bytes = redis_client.to_jsonable_bytes(frame)
                    payload_to_redis = {
                            "frame": image_bytes,
                            "timestamp": ts_str,
                            "frame_id": frame_id,
                            "camera_id": camera_id,
                        }
                    redis_client.set(frame_key, payload_to_redis, ttl=360)
                    queue.append(frame_key)

                    if required_analytics:
                        final_results = None
                        all_layers_results = {}
                        all_layers_results_by_name = {}
                        bounding_boxes_results = {}
                        cardinal_requirements = {}
                        latest_camera_layer_config_id = None
                        
                        # Add your frame processing logic here
                        # For each layer in the layers_config, process the frame
                        for layer in layers_config:
                            logger.info(f"Processing layer: {layer.get('layer_name')}")

                            latest_camera_layer_config_id = layer.get("layer_configuration_id")

                            # Check if there is a dependency for the layer
                            if layer.get("dependencies", []):
                                # For loop through the dependencies
                                for dependency in layer.get("dependencies"):
                                    # Dependency contains a tuple of (dependency_layer_id, conditions)
                                    dependency_layer_id, conditions = dependency
                                    logger.info(f"Checking for dependency id: {dependency_layer_id}")
                                    # Check if the dependency is in the all_layers_results
                                    if conditions and all_layers_results.get(dependency_layer_id) and not check_condition(conditions, all_layers_results[dependency_layer_id]):
                                        logger.info(f"Dependency {dependency_layer_id} failed")
                                        break
                            
                            frame_history = None
                            history_frame_ids = None
                            layer_configuration = {}
                            # Check if the layer configuration requires a frame history
                            if 'configuration' in layer and layer.get("configuration", {}):
                                if isinstance(layer.get("configuration"), str):
                                    layer_configuration = json.loads(layer.get("configuration"))
                                else:
                                    layer_configuration = layer.get("configuration")

                            if 'frame_history' in layer_configuration and str(layer_configuration['frame_history']).lower() == "true":

                                # Get the frame history
                                frame_history = queue.copy()
                                # Extract frame IDs in FIFO order (oldest to newest)
                                history_frame_ids = list(frame_history)
                                logger.info(f"Frame history IDs (oldest to newest): {history_frame_ids}")

                            dependency_result = {}
                            if 'extract_from_dependency' in layer_configuration.keys() and layer_configuration['extract_from_dependency']:
                                for dependency in layer_configuration['extract_from_dependency']:
                                    dependency_result[dependency] = all_layers_results_by_name[dependency]

                            logger.info(f"Calling model service to detect objects for frame {frame_id}")
                            try:
                                layer_results = call_function(layer.get("function_name"), layer, frame_key, camera_id, ts_str, frame_id, history_frame_ids, dependency_result=dependency_result, camera_name=camera_name)
                                results = layer_results.get("result")

                                logger.info(f"Layer name: {layer.get('layer_name')} for frame {frame_id} with layer results: {results}")

                                if isinstance(results, dict) and "preprocessing_time" in results.keys():
                                    preprocessing_time = results.get("preprocessing_time")
                                    results = results.get("result")

                                # Store the config layer to the all_layers_results
                                all_layers_results[layer.get("layer_configuration_id")] = results
                                all_layers_results_by_name[layer.get("layer_name")] = results

                               
                                if results and isinstance(results, dict) and "cardinal_requirements" in results.keys() and results.get("final_results"):
                                    # Need to add the cardinal requirements to the cardinal_requirements dictionary
                                    cardinal_requirements[results.get("cardinal_requirements")] = results.get("final_results")
                                    
                            except requests.exceptions.Timeout:
                                logger.error(f"Timeout while waiting for model service response for frame {frame_id}")
                            except requests.exceptions.ConnectionError as e:
                                logger.error(f"Connection error with model service for frame {frame_id}: {str(e)}")
                            except requests.exceptions.RequestException as e:
                                logger.error(f"Error calling model service for frame {frame_id}: {str(e)}")
                            except Exception as e:
                                logger.error(f"Unexpected error processing model service response for frame {frame_id}: {str(e)}")
                                import traceback
                                traceback.print_exc()
                            continue  # Skip to next layer

                        logger.info(f"Cardinal requirements: {cardinal_requirements}")
                        
                        # Check if there is any cardinal requirements
                        final_frame_key = redis_client.build_key(camera_id, ts_str, "final_result")
                        prediction_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        # If there is any cardinal requirements, we will need to overlay the frame with the final results
                        if cardinal_requirements:
                            logger.info(f"Calling model service to overlay the box and alerts for frame {frame_id}")
                            # Get the model service to overlay the box and alerts and save the result to the final_frame_key
                            layer_results = call_function(
                                "overlay_box_and_alerts", 
                                {
                                    "function_name": "overlay_box_and_alerts",
                                }, 
                                frame_key, 
                                camera_id,
                                ts_str, 
                                final_frame_key,
                                frame_history=None,
                                dependency_result=None,
                                cardinal_requirements=cardinal_requirements,
                                camera_layer_config_id=latest_camera_layer_config_id,
                                frame_time=frame_time,
                                preprocessing_time=preprocessing_time,
                                prediction_time=prediction_time)

                            logger.info(f"Overlay box and alerts results: {layer_results}")

                        # We will publish the latest frame to the backend
                        else:
                            redis_client.set(final_frame_key, {
                                "final_result": image_bytes,
                            }, ttl=300)
                        redis_client.publish_latest_frame(final_frame_key)

                    # No analytics required
                    else:
                        # Check if there is any cardinal requirements
                        final_frame_key = redis_client.build_key(camera_id, ts_str, "final_result")
                        redis_client.set(final_frame_key, {
                            "final_result": image_bytes,
                        }, ttl=300)
                        redis_client.publish_latest_frame(final_frame_key)

                except Exception as e:
                    logger.error(f"Error processing frame {frame_id}: {e}")
                    import traceback
                    traceback.print_exc()
                
                continue

        else:
            for frame, timestamp, frame_id in stream.stream():
                try:
                    # Here you can do your frame processing
                    ts_str = timestamp.strftime("%Y-%m-%d %I:%M:%S.%f")
                    frame_time = timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    preprocessing_time = None
                    prediction_time = None
                    logger.info(f"Processing frame {frame_id} at {ts_str}")

                    # Save the frame to Redis
                    frame_key = redis_client.build_key(camera_id, ts_str, None)
                    logger.info(f"Frame key from camera stream: {frame_key}")

                    # Convert frame to JSON-friendly bytes
                    image_bytes = redis_client.to_jsonable_bytes(frame)
                    payload_to_redis = {
                        "frame": image_bytes,
                        "timestamp": ts_str,
                        "frame_id": frame_id,
                        "camera_id": camera_id,
                    }
                    redis_client.set(frame_key, payload_to_redis, ttl=360)
                    queue.append(frame_key)

                    if required_analytics:
                        final_results = None
                        all_layers_results = {}
                        all_layers_results_by_name = {}
                        bounding_boxes_results = {}
                        cardinal_requirements = {}
                        latest_camera_layer_config_id = None
                        
                        # Add your frame processing logic here
                        # For each layer in the layers_config, process the frame
                        for layer in layers_config:
                            logger.info(f"Processing layer: {layer.get('layer_name')}")

                            latest_camera_layer_config_id = layer.get("layer_configuration_id")

                            # Check if there is a dependency for the layer
                            if layer.get("dependencies", []):
                                # For loop through the dependencies
                                for dependency in layer.get("dependencies"):
                                    # Dependency contains a tuple of (dependency_layer_id, conditions)
                                    dependency_layer_id, conditions = dependency
                                    logger.info(f"Checking for dependency id: {dependency_layer_id}")
                                    # Check if the dependency is in the all_layers_results
                                    if conditions and all_layers_results.get(dependency_layer_id) and not check_condition(conditions, all_layers_results[dependency_layer_id]):
                                        logger.info(f"Dependency {dependency_layer_id} failed")
                                        break

                        frame_history = None
                        history_frame_ids = None
                        layer_configuration = {}
                        # Check if the layer configuration requires a frame history
                        if 'configuration' in layer and layer.get("configuration", {}):
                            if isinstance(layer.get("configuration"), str):
                                layer_configuration = json.loads(layer.get("configuration"))
                            else:
                                layer_configuration = layer.get("configuration")

                        if 'frame_history' in layer_configuration and str(layer_configuration['frame_history']).lower() == "true":
                            # Get the frame history
                            frame_history = queue.copy()
                            # Extract frame IDs in FIFO order (oldest to newest)
                            history_frame_ids = list(frame_history)
                            logger.info(f"Frame history IDs (oldest to newest): {history_frame_ids}")

                        dependency_result = {}
                        if 'extract_from_dependency' in layer_configuration.keys() and layer_configuration['extract_from_dependency']:
                            for dependency in layer_configuration['extract_from_dependency']:
                                dependency_result[dependency] = all_layers_results_by_name[dependency]
                                
                        # Proceed to run the layer
                        # Call the model service to detect objects
                        logger.info(f"Calling model service to detect objects for frame {frame_id}")
                        try:
                            layer_results = call_function(layer.get("function_name"), layer, frame_key, camera_id, ts_str, frame_id, history_frame_ids, camera_name=camera_name, dependency_result=dependency_result)
                            results = layer_results.get("result")

                            logger.info(f"Layer name: {layer.get('layer_name')} for frame {frame_id} with layer results: {results}")

                            if isinstance(results, dict) and "preprocessing_time" in results.keys():
                                preprocessing_time = results.get("preprocessing_time")
                                results = results.get("result")

                            # Store the config layer to the all_layers_results
                            all_layers_results[layer.get("layer_configuration_id")] = results
                            all_layers_results_by_name[layer.get("layer_name")] = results

                            # Check if it is the final results
                            if results and isinstance(results, dict) and "cardinal_requirements" in results.keys() and results.get("final_results"):
                                # Need to add the cardinal requirements to the cardinal_requirements dictionary
                                cardinal_requirements[results.get("cardinal_requirements")] = results.get("final_results")
                                
                        except requests.exceptions.Timeout:
                            logger.error(f"Timeout while waiting for model service response for frame {frame_id}")
                        except requests.exceptions.ConnectionError as e:
                            logger.error(f"Connection error with model service for frame {frame_id}: {str(e)}")
                        except requests.exceptions.RequestException as e:
                            logger.error(f"Error calling model service for frame {frame_id}: {str(e)}")
                        except Exception as e:
                            logger.error(f"Unexpected error processing model service response for frame {frame_id}: {str(e)}")
                            import traceback
                            traceback.print_exc()

                        # Check if there is any cardinal requirements
                        final_frame_key = redis_client.build_key(camera_id, ts_str, "final_result")
                        prediction_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        # If there is any cardinal requirements, we will need to overlay the frame with the final results
                        if cardinal_requirements:
                            logger.info(f"Calling model service to overlay the box and alerts for frame {frame_id}")
                            # Get the model service to overlay the box and alerts and save the result to the final_frame_key
                            layer_results = call_function(
                                "overlay_box_and_alerts", 
                                {
                                    "function_name": "overlay_box_and_alerts",
                                }, 
                                frame_key, 
                                camera_id,
                                ts_str, 
                                final_frame_key,
                                frame_history=None,
                                dependency_result=None,
                                cardinal_requirements=cardinal_requirements,
                                camera_layer_config_id=latest_camera_layer_config_id,
                                frame_time=frame_time,
                                preprocessing_time=preprocessing_time,
                                prediction_time=prediction_time)

                            logger.info(f"Overlay box and alerts results: {layer_results}")

                        # We will publish the latest frame to the backend
                        else:
                            redis_client.set(final_frame_key, {
                                "final_result": image_bytes,
                            }, ttl=300)
                        redis_client.publish_latest_frame(final_frame_key)

                    else:
                        print(f"No analytics required for frame {frame_id}.")
                        # Check if there is any cardinal requirements
                        final_frame_key = redis_client.build_key(camera_id, ts_str, "final_result")
                        redis_client.set(final_frame_key, {
                            "final_result": image_bytes,
                        }, ttl=300)
                        redis_client.publish_latest_frame(final_frame_key)

                except Exception as e:
                    logger.error(f"Error processing frame {frame_id}: {e}")
                    import traceback
                    traceback.print_exc()

                continue
            
    except KeyboardInterrupt:
        logger.error("\nStopping stream...")

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"Error: {e}")
        
    finally:
        stream.stop()


if __name__ == "__main__":
    # Check if async processing is enabled
    use_async = os.environ.get('USE_ASYNC_PROCESSING', 'false').lower() == 'true'
    use_async = True
    if use_async:
        logger.info("Using producer-consumer pattern to prevent timing drift")
        main_producer_consumer()
    else:
        logger.info("Using synchronous frame processing (may cause timing drift if processing time > frame interval)")
        main() # Outdated code
