# Build stage
FROM ubuntu:24.04 AS builder

# Creating the work directory
WORKDIR /usr/src/camera_stream

# Update and install necessary packages
RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev \
    # python3-distutils \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create symlink from python3 to python
RUN ln -sf /usr/bin/python3 /usr/bin/python


# Install build dependencies
RUN apt-get update && \
    apt-get install -y \
    gcc \
    build-essential

# Copy requirements first for better caching
COPY requirements.txt requirements.txt

# Install dependencies
RUN python -m pip install --no-cache-dir --break-system-packages -r requirements.txt
RUN python -m pip install --no-cache-dir --break-system-packages cryptography

# Copy the application code
COPY . .

# Make build script executable
RUN chmod +x build_exes.sh

# Run the build script to create executables
RUN ./build_exes.sh

# Final stage
FROM ubuntu:24.04 

# Creating the work directory
WORKDIR /usr/src/camera_stream

# Update and install necessary packages
RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev \
    # python3-distutils \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create symlink from python3 to python
RUN ln -sf /usr/bin/python3 /usr/bin/python

# create the app user
RUN adduser --system --group appuser

# Update and upgrade the system packages
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        libsm6 libxext6 libgl1 libglib2.0-0 \
        ffmpeg \
        ca-certificates tzdata && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create necessary directories with correct permissions
RUN mkdir -p /nonexistent /usr/src/camera_stream/license && \
    chown -R appuser:appuser /nonexistent

# Copy only the executable from builder
COPY --from=builder /usr/src/camera_stream/dist/camera_stream /usr/src/camera_stream/

# Copy the license and env directories
COPY license/ /usr/src/camera_stream/license/
COPY env/ /usr/src/camera_stream/env/

# Copy the videos directory
# COPY videos/ /usr/src/camera_stream/videos/

# Set permissions
RUN chmod +x /usr/src/camera_stream/camera_stream && \
    chown -R appuser:appuser /usr/src/camera_stream

RUN mkdir -p /usr/src/camera_stream/output_logs && \
    chmod 777 -R /usr/src/camera_stream/output_logs && \
    chown -R appuser:appuser /usr/src/camera_stream/output_logs

# Ensure the backend package is in the Python path
ENV PYTHONPATH=/usr/src/camera_stream

# change to the app user
USER appuser

# Create a startup script that properly handles environment variables
RUN echo '#!/bin/bash\n\
# Load environment variables from ENV_FILE if specified\n\
if [ -n "$ENV_FILE" ] && [ -f "$ENV_FILE" ]; then\n\
    set -a\n\
    source "$ENV_FILE"\n\
    set +a\n\
fi\n\
\n\
# Run the main application with environment variables\n\
sleep 5 && exec ./camera_stream' > /usr/src/camera_stream/start.sh && \
    chmod +x /usr/src/camera_stream/start.sh

CMD ["./start.sh"]