import os
import time
import uuid
import psycopg2
from psycopg2.extras import DictCursor
import logging
from typing import Tuple, Optional, List, Dict, Any
import json

logger = logging.getLogger(__name__)

class DatabaseConnection:
    def __init__(self, max_retries: int = 30, retry_interval: int = 2):
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        print("Initializing database connection with parameters:")
        self.db_params = {
            'dbname': os.environ.get('DB_NAME'),
            'user': os.environ.get('DB_USER'),
            'password': os.environ.get('DB_PASSWORD'),
            'host': os.environ.get('DB_HOST'),
            'port': os.environ.get('DB_PORT')
        }
        print(f"Host: {self.db_params['host']}")
        print(f"Port: {self.db_params['port']}")
        print(f"Database: {self.db_params['dbname']}")
        print(f"User: {self.db_params['user']}")
        self.conn = self.connect_with_retry()

    def connect_with_retry(self) -> Optional[psycopg2.extensions.connection]:
        """
        Attempt to connect to the database with retry mechanism
        Returns:
            Optional[psycopg2.extensions.connection]: Database connection if successful, None otherwise
        """
        print(f"Attempting to connect to database at {self.db_params['host']}:{self.db_params['port']}")
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                conn = psycopg2.connect(**self.db_params)
                print(f"Successfully connected to PostgreSQL at {self.db_params['host']}:{self.db_params['port']}")
                return conn
            except psycopg2.OperationalError as e:
                retry_count += 1
                if retry_count < self.max_retries:
                    print(f"Database connection attempt {retry_count}/{self.max_retries} failed. Retrying in {self.retry_interval} seconds...")
                    print(f"Error: {str(e)}")
                    time.sleep(self.retry_interval)
                else:
                    print(f"Max retries ({self.max_retries}) reached. Could not connect to PostgreSQL.")
                    return None
            except Exception as e:
                print(f"Unexpected error while connecting to database: {str(e)}")
                return None

    def get_camera_info(self, camera_name: str) -> Tuple[Optional[str], Optional[int], Optional[int]]:
        """
        Get camera information from the database
        Args:
            camera_name: Name of the camera to query
        Returns:
)            Tuple[Optional[str], Optional[int], Optional[int]]: (rtsp_url, stream_fps, camera_id) or (None, None, None) if not found
        """
        print(f"Fetching camera information for: {camera_name}")
        if not self.conn:
            print("Failed to establish database connection")
            return None, None, None

        try:
            cur = self.conn.cursor(cursor_factory=DictCursor)
            cur.execute("SELECT rtsp_url, stream_fps, id FROM cameras_camera WHERE name = %s", (camera_name,))
            result = cur.fetchone()

            if not result:
                print(f"No camera found with name: {camera_name}")
                return None, None, None

            print(f"Found camera: ID={result['id']}, FPS={result['stream_fps']}")
            return result['rtsp_url'], result['stream_fps'], result['id']

        except Exception as e:
            print(f"Error querying camera information: {e}")
            return None, None, None

    def get_camera_layers_config(self, camera_id: str) -> Tuple[List[Dict[str, Any]], bool]:
        """
        Get camera layers configuration from the database, sorted by layer_type
        Args:
            camera_id: UUID of the camera to query
        Returns:
            List[Dict[str, Any]]: List of layer configurations sorted by layer_type, containing:
                - layer_id: UUID of the layer
                - layer_name: Name of the layer
                - layer_type: Type of the layer (1, 2, or 3)
                - configuration: Layer-specific configuration dictionary
                - function_name: Name of the function to execute
                - cv_model: CV model information if available
                - enabled: Whether the layer is enabled
                - dependencies: List of dependencies
                Returns empty list if no configurations found
        """
        try:
            # Get all layer configurations for this camera, ordered by layer_type
            print(f"Getting camera layers configuration for camera with connection{camera_id}")
            if not self.conn:
                print("Failed to establish database connection")
                return [], False
            
            cur = self.conn.cursor(cursor_factory=DictCursor)
            # Join cameras_cameralayersconfiguration with cameras_layer and order by layer_type
            cur.execute("""
                SELECT
                    l.id   AS layer_id,
                    l.name AS layer_name,
                    clc.id AS layer_configuration_id,
                    l.layer_type,
                    clc.configuration,
                    l.function_name,
                    clc.enabled,
                    cam.required_analytics as required_analytics,

                    -- dependencies as jsonb array
                    COALESCE(
                        jsonb_agg(DISTINCT jsonb_build_object(
                        'dependency_layer_id', dep.dependency_layer_id,
                        'conditions',          dep.conditions
                        )) FILTER (WHERE dep.dependency_layer_id IS NOT NULL),
                        '[]'::jsonb
                    ) AS dependencies,

                    -- regions as jsonb array
                    COALESCE(
                        jsonb_agg(DISTINCT jsonb_build_object(
                        'region_id',    roi.id,
                        'name',         roi.name,
                        'description',  roi.description,
                        'roi_type',     roi.roi_type,
                        'coordinates',  roi.coordinates,
                        'alerts_category', roi.alerts_category,
                        'criteria',      roi.criteria
                        )) FILTER (WHERE roi.id IS NOT NULL),
                        '[]'::jsonb
                    ) AS regions

                FROM cameras_cameraslayersconfiguration clc

                -- join to camera table to get required_analytics
                LEFT JOIN cameras_camera cam
                ON clc.camera_id = cam.id

                LEFT JOIN cameras_layer l
                ON clc.layers_id = l.id

                LEFT JOIN cameras_cameraslayersconfigurationdependency dep
                ON dep.current_layer_id = clc.id

                LEFT JOIN cameras_cameraslayersconfiguration_regions reg
                ON reg.cameraslayersconfiguration_id = clc.id
                LEFT JOIN cameras_regionofinterest roi
                ON roi.id = reg.regionofinterest_id

                WHERE clc.camera_id = %s

                GROUP BY
                    l.id,
                    l.name,
                    l.layer_type,
                    clc.configuration,
                    l.function_name,
                    clc.enabled,
                    clc.id,
                    cam.required_analytics

                ORDER BY l.layer_type;
            """, (camera_id,))
            layer_configs = cur.fetchall()

            result = []
            required_analytics = False
            required_pose_detection = False
            for config in layer_configs:
                print(f"Layer config: {config['required_analytics']}")
                if not required_analytics and config['required_analytics']:
                    required_analytics = True

                if not required_pose_detection and config['function_name'] == 'get_pose_position':
                    required_pose_detection = True

                layer_data = {
                    'layer_id': str(config['layer_id']),
                    'layer_name': config['layer_name'],
                    'layer_type': config['layer_type'],
                    'configuration': config['configuration'],
                    'function_name': config['function_name'],
                    'enabled': config['enabled'],
                    'dependencies': config['dependencies'] if config['dependencies'] is not None else [],
                    'layer_configuration_id': str(config['layer_configuration_id']),
                    'regions': config['regions'] if config['regions'] is not None else []
                }

                result.append(layer_data)

            print(f"Required analytics: {required_analytics}")
            
            return result, required_analytics, required_pose_detection

        except Exception as e:
            print(f"Error fetching camera layers configuration for camera {camera_id}: {str(e)}")
            return [], False
        
    def push_results_and_frame_to_redis(self, response: list, img_bytes: bytes, frame_id: str, camera_id: str, timestamp: str, redis_client=None):
        """
        Push camera event data directly to a Redis processing queue
        
        Args:
            response: List of detection results from model service
            img_bytes: Raw image bytes of the frame
            frame_id: Unique identifier for the frame
            camera_id: UUID of the camera
            timestamp: Timestamp string of when the frame was captured
            redis_client: Redis client instance
        """
        try:
            if not redis_client:
                logger.error("Redis client not provided for event push")
                return
            
            # Prepare data for Redis
            event_data = {
                "response": response,  # Detection results
                "img_bytes": redis_client.to_jsonable_bytes(img_bytes),  # Encoded frame bytes
                "camera_id": camera_id,  # Camera identifier
                "timestamp": timestamp,  # Exact timestamp
                "frame_id": frame_id  # Frame identifier
            }
            
            # Serialize the data to JSON string
            event_json = json.dumps(event_data)
            
            # Push directly to the processing queue
            queue_name = "processed_events_queue"
            redis_client.rpush(queue_name, event_json)
            logger.info(f"Added event for camera {camera_id}, frame {frame_id} directly to {queue_name}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error pushing camera event to Redis: {str(e)}")
        