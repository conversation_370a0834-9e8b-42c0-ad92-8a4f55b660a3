# logger_setup.py
import logging
import logging.config
import os

env_file = os.environ.get('ENV_FILE')
env_file = env_file.split('/')[-1]
# import yaml    # uncomment if loading from YAML

LOGGING_CONFIG = {
    "version": 1,
    "formatters": {
        "simple": {
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s - %(levelname)s - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s"
        },
        "error": {
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s - %(levelname)s - <PID %(process)d:%(processName)s> - %(filename)s-%(module)s-%(funcName)s-%(lineno)d: %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "simple",
            "stream": "ext://sys.stdout"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "simple",
            "filename": f"output_logs/{env_file}_info.log",
            "maxBytes": 10*1024*1024,
            "backupCount": 3,
            "encoding": "utf8"
        }
    },
    "root": {
        "level": "DEBUG",
        "handlers": ["console", "file"],
        "propagate": False
    }
}

def setup_logging():
    """Call this once at application startup."""
    logging.config.dictConfig(LOGGING_CONFIG)

# If you prefer loading from a YAML file instead:
# def setup_logging():
#     with open("logging_config.yaml") as f:
#         cfg = yaml.safe_load(f)
#     logging.config.dictConfig(cfg)
