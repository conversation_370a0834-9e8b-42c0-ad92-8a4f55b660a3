#!/bin/bash

# Exit on any error
set -e

echo "Installing OpenCV’s native dependencies so PyInstaller can bundle them…"
apt-get update \
  && apt-get install -y --no-install-recommends \
       libgl1 libglib2.0-0 libsm6 libxext6 \
       libjpeg-dev zlib1g-dev python3-distutils \
  && rm -rf /var/lib/apt/lists/*

echo "Installing PyInstaller..."
pip install --break-system-packages pyinstaller

echo "Compiling main.py..."
pyinstaller --onedir \
    --name camera_stream \
    --add-data "license:license" \
    --hidden-import=psycopg2-binary \
    --hidden-import=redis \
    --hidden-import=requests \
    --hidden-import=pytz \
    --hidden-import=dotenv \
    --hidden-import=cryptography \
    --hidden-import=pythonjsonlogger.jsonlogger \
    --hidden-import=pythonjsonlogger \
    main.py

echo "Build complete! Executable is in the dist/ directory" 