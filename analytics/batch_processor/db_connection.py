import os
import time
import uuid
import json
import psycopg2
from psycopg2.extras import DictCursor
import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class DatabaseConnection:
    def __init__(self, max_retries: int = 3, retry_interval: int = 2):
        print("Initializing database connection")
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        self.db_params = {
            'dbname': os.environ.get('DB_NAME'),
            'user': os.environ.get('DB_USER'),
            'password': os.environ.get('DB_PASSWORD'),
            'host': os.environ.get('DB_HOST'),
            'port': os.environ.get('DB_PORT')
        }
        self.conn = self.connect_with_retry()
        print("Database connection initialized")

    def connect_with_retry(self) -> Optional[psycopg2.extensions.connection]:
        """
        Attempt to connect to the database with retry mechanism
        Returns:
            Optional[psycopg2.extensions.connection]: Database connection if successful, None otherwise
        """
        logger.info(f"Attempting to connect to database at {self.db_params['host']}:{self.db_params['port']}")
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                conn = psycopg2.connect(**self.db_params)
                logger.info(f"Successfully connected to PostgreSQL at {self.db_params['host']}:{self.db_params['port']}")
                return conn
            except psycopg2.OperationalError as e:
                retry_count += 1
                if retry_count < self.max_retries:
                    logger.warning(f"Database connection attempt {retry_count}/{self.max_retries} failed. Retrying in {self.retry_interval} seconds...")
                    logger.error(f"Error: {str(e)}")
                    time.sleep(self.retry_interval)
                else:
                    logger.error(f"Max retries ({self.max_retries}) reached. Could not connect to PostgreSQL.")
                    return None
            except Exception as e:
                logger.error(f"Unexpected error while connecting to database: {str(e)}")
                return None

   
    def save_frame(self, img_bytes: bytes, camera_id: str, timestamp: str) -> str:
        """
        Save frame to the database if it doesn't exist
        
        Args:
            img_bytes: Bytes of the image
            camera_id: UUID of the camera
            timestamp: Timestamp of the frame
            
        Returns:
            str: The UUID of the frame (either existing or newly created)
        """
        try:
            if not self.conn:
                logger.error("Failed to establish database connection")
                return None
            
            # First check if the frame already exists
            cur = self.conn.cursor(cursor_factory=DictCursor)
            cur.execute("""
                SELECT id, frame_bytes FROM cameras_frame 
                WHERE camera_id = %s AND timestamp = %s
                LIMIT 1
                """, (camera_id, timestamp))
            
            existing_frame = cur.fetchone()
            
            if existing_frame:
                # Frame already exists
                frame_id = existing_frame['id']
                logger.info(f"Frame already in database: id={frame_id}, camera_id={camera_id}, timestamp={timestamp}")
                
                # Check if we need to update the frame with image bytes
                if img_bytes and not existing_frame['frame_bytes']:
                    logger.info(f"Updating existing frame with image bytes: id={frame_id}")
                    cur.execute("""
                        UPDATE cameras_frame SET frame_bytes = %s WHERE id = %s
                        """, (img_bytes, frame_id))
                    self.conn.commit()
                
                return frame_id
            
            # If no existing frame, create a new one
            frame_id = str(uuid.uuid4())
            created_at = timestamp  # Use the same timestamp for created_at

            # Extract image dimensions if possible
            width = None
            height = None
            format = 'JPEG'  # Default assumption
            
            try:
                # Try to extract image dimensions if possible
                if img_bytes:
                    import io
                    from PIL import Image
                    img = Image.open(io.BytesIO(img_bytes))
                    width, height = img.size
                    format = img.format
            except Exception as dim_err:
                logger.warning(f"Could not extract image dimensions: {str(dim_err)}")

            # Insert with all fields from the Frame model
            cur.execute("""
                INSERT INTO cameras_frame (
                    id, camera_id, timestamp, frame_bytes, 
                    width, height, format, created_at
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    frame_id, 
                    camera_id, 
                    timestamp, 
                    img_bytes,
                    width,
                    height,
                    format,
                    created_at
                ))
            self.conn.commit()
            logger.info(f"New frame saved successfully: id={frame_id}, camera_id={camera_id}, timestamp={timestamp}")
            return frame_id
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error saving frame for frame {timestamp}: {str(e)}")
            self.conn.rollback()
            raise e

    def save_camera_event(self, response: list, img_bytes: bytes, camera_id: str, timestamp: str):
        """
        Save camera event to the database
        Args:
            response: List of detection results
            img_bytes: Bytes of the image
            camera_id: UUID of the camera
            timestamp: Timestamp of the event
        """
        try:
            if not self.conn:
                logger.error("Failed to establish database connection")
                return
            
            # First save or get the frame, which returns the frame ID
            frame_id = self.save_frame(img_bytes, camera_id, timestamp)
            
            if not frame_id:
                logger.error(f"Could not save or find frame for camera_id={camera_id}, timestamp={timestamp}")
                return
                
            cur = self.conn.cursor(cursor_factory=DictCursor)
            
            # Analyze response to determine event type and confidence
            event_type = 'custom'  # Default event type
            confidence = 0.0
            region_id = None
            camera_layer_config_id = None
            
            # Look for region_id and layer_id in the response
            if response and isinstance(response, list):
                for detection in response:
                    if isinstance(detection, dict):
                        # Extract confidence
                        if 'confidence' in detection:
                            det_confidence = float(detection.get('confidence', 0))
                            if det_confidence > confidence:
                                confidence = det_confidence
                                
                                # Try to determine event type from class name
                                class_name = detection.get('class_name', '').lower()
                                if 'person' in class_name and ('covered' in class_name or 'masked' in class_name):
                                    event_type = 'coveredPerson'
                                elif 'weapon' in class_name or 'gun' in class_name or 'knife' in class_name:
                                    event_type = 'weaponDetection'
                                elif 'large' in class_name or 'oversize' in class_name:
                                    event_type = 'oversizeObject'
                                elif 'unattended' in class_name:
                                    event_type = 'unattendedObject'
                                elif 'fence' in class_name or 'breach' in class_name or 'climb' in class_name:
                                    event_type = 'areaBreach'
                        
                        # Extract region_id if present
                        if 'region_id' in detection and not region_id:
                            region_id = detection.get('region_id')
                            
                        # Extract layer_id if present
                        if 'layer_id' in detection and not camera_layer_config_id:
                            camera_layer_config_id = detection.get('layer_id')
            
            # Get UUID for the event
            event_uuid = str(uuid.uuid4())

            # Convert response to JSON string for storage
            details_json = json.dumps(response)
            
            # Generate thumbnail path if needed
            thumbnail_path = None
            if confidence > 0.5:  # Only generate thumbnails for high-confidence events
                try:
                    # Create a path following a consistent pattern
                    thumbnail_path = f"/media/events/{event_uuid}/thumbnail.jpg"
                    # Note: The actual thumbnail generation would happen elsewhere
                except Exception as thumb_err:
                    logger.warning(f"Error generating thumbnail path: {str(thumb_err)}")

            # Create the event with reference to the frame and all required fields
            cur.execute("""
                INSERT INTO cameras_cameraevent (
                    id, camera_id, region_id, camera_layer_config_id,
                    event_type, timestamp, frame_id, 
                    confidence, details, thumbnail_path,
                    is_reviewed, is_suspicious, in_training_queue
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    event_uuid, 
                    camera_id,
                    region_id,
                    camera_layer_config_id, 
                    event_type,
                    timestamp, 
                    frame_id,
                    confidence,
                    details_json,  # Store full response as JSON
                    thumbnail_path,
                    False,  # is_reviewed
                    False,  # is_suspicious
                    False   # in_training_queue
                ))
            self.conn.commit()
            logger.info(f"Camera event saved successfully: id={event_uuid}, type={event_type}, confidence={confidence:.2f}")
            return event_uuid
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error saving camera event for frame {timestamp}: {str(e)}")
            self.conn.rollback()
            raise e