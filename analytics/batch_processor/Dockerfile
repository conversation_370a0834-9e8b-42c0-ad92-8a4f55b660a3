FROM python:3.11-slim

WORKDIR /app

# Install required packages
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq-dev \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy batch processor files first (for better layer caching)
COPY ./analytics/batch_processor/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY ./analytics /app/analytics
COPY .env /app/.env

# Set the working directory to the batch processor
WORKDIR /app/analytics/batch_processor

# Make the start script executable
COPY ./analytics/batch_processor/aws_start.sh /app/analytics/batch_processor/
RUN chmod +x /app/analytics/batch_processor/aws_start.sh

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Run the batch processor using the start script
CMD ["/app/analytics/batch_processor/aws_start.sh"]
