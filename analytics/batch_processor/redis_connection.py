import json
import os
import redis
import base64
import logging
from typing import Any, Optional, List

logger = logging.getLogger(__name__)


class CameraCache:
    """
    Key–value cache for per-camera, per-time-frame data in Redis.

    Key format
    ----------
        <camera_id>_<timeframe>            e.g. "camA_20250526T011513"
        <camera_id>_<timeframe>_<layer_id> e.g. "camA_20250526T011513_L1"

    Examples
    --------
    >>> cache = CameraCache(ttl_default=600)
    >>> cache.set("camA", "20250526T011500", {"boxes": []})
    >>> cache.set("camA", "20250526T011500", {"mask": []}, layer_id="seg")
    >>> cache.get("camA", "20250526T011500")
    {'boxes': []}
    >>> cache.get("camA", "20250526T011500", layer_id="seg")
    {'mask': []}
    """

    # ------------------------------------------------------------------ #
    #  Construction / connection
    # ------------------------------------------------------------------ #

    def __init__(
        self,
        host: str | None = None,
        port: int | None = None,
        db: int | None = None,
        password: str | None = None,
        ttl_default: int = 6000,
    ) -> None:
        print("Connecting to Redis...")
        self._redis = redis.Redis(
            host=host or os.getenv("REDIS_HOST"),
            port=port or int(os.getenv("REDIS_PORT")),
            db=db or int(os.getenv("REDIS_DB")),
            password=password or os.getenv("REDIS_PASS"),
            decode_responses=True,  # str in / str out so json loads easily
        )
        self._ttl_default = ttl_default
        print("Connected to Redis")

    # ------------------------------------------------------------------ #
    #  Public API
    # ------------------------------------------------------------------ #

    def set(
        self,
        key: str,
        value: Any,
        *,
        ttl: Optional[int] = None,
    ) -> None:
        """
        Cache *value* under the standardised key for (*camera_id*, *timeframe*[, *layer_id*]).
        The key expires automatically after *ttl* seconds (default = self._ttl_default).
        """
        payload = json.dumps(value)
        self._redis.set(name=key, value=payload, ex=ttl or self._ttl_default)

    def get(
        self,
        key: str,
        *,
        default: Any = None,
    ) -> Any:
        """
        Retrieve the cached value or return *default* if the key is missing / expired.
        """
        raw = self._redis.get(key)
        return default if raw is None else json.loads(raw)
        
    def get_batch_from_queue(self, queue_name: str, batch_size: int = 10) -> list:
        """
        Get a batch of items from a Redis queue (list)
        
        Args:
            queue_name: Name of the Redis list
            batch_size: Maximum number of items to retrieve
            
        Returns:
            List of items retrieved from the queue
        """
        items = []
        for _ in range(batch_size):
            item = self._redis.lpop(queue_name)
            if item:
                items.append(item)
            else:
                break
        logger.info(f"Retrieved {len(items)} items from queue {queue_name}")
        return items
        
    def get_processed_events(self, batch_size: int = 10) -> list:
        """
        Get a batch of processed events directly from the processed_events_queue
        
        Args:
            batch_size: Maximum number of events to retrieve
            
        Returns:
            List of deserialized event data dictionaries
        """
        queue_name = "processed_events_queue"
        raw_items = self.get_batch_from_queue(queue_name, batch_size)
        
        # Deserialize JSON items
        processed_events = []
        for item in raw_items:
            try:
                # Parse the JSON string into a dictionary
                event_data = json.loads(item)
                
                # Convert the base64 encoded image back to bytes if present
                if "img_bytes" in event_data and event_data["img_bytes"]:
                    event_data["img_bytes"] = self.from_jsonable_bytes(event_data["img_bytes"])
                    
                processed_events.append(event_data)
            except Exception as e:
                logger.error(f"Error processing event from queue: {str(e)}")
                continue
                
        logger.info(f"Processed {len(processed_events)} events from {queue_name}")
        return processed_events

    def delete_key(self, key: str) -> None:
        """
        Delete a key from Redis
        """
        self._redis.delete(key)
        logger.info(f"Deleted key {key} from Redis")

    # ------------------------------------------------------------------ #
    #  Internals
    # ------------------------------------------------------------------ #

    @staticmethod
    def build_key(camera_id: str, timeframe: str, layer_id: str | None) -> str:
        """
        Assemble the canonical Redis key string.
        """
        if layer_id:
            return f"{camera_id}_{timeframe}_{layer_id}"
        return f"{camera_id}_{timeframe}"
    
    # ---------- 1. binary → JSON-friendly ---------------------------------
    @staticmethod
    def to_jsonable_bytes(data: bytes | bytearray) -> str:
        """Encode arbitrary binary as a base-64 ASCII string."""
        return base64.b64encode(data).decode("ascii")

    # ---------- 2. JSON-friendly → binary ---------------------------------
    @staticmethod
    def from_jsonable_bytes(encoded: str) -> bytes:
        """Decode the ASCII base-64 string back to the original bytes."""
        return base64.b64decode(encoded.encode("ascii"))
