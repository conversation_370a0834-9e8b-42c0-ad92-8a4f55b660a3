import os
import time
import logging
import argparse
from dotenv import load_dotenv
load_dotenv()

# Import local modules
from redis_connection import Camera<PERSON>ache
from db_connection import DatabaseConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

class BatchProcessor:
    """
    Batch processor for camera events.
    Pulls events from Redis queue and processes them in batches.
    """
    
    def __init__(self, batch_size: int = 20, sleep_interval: int = 1):
        """
        Initialize the batch processor
        
        Args:
            batch_size: Number of events to process in a batch
            sleep_interval: Time to sleep between batches (seconds)
        """
        self.batch_size = batch_size
        self.sleep_interval = sleep_interval
        self.redis_client = CameraCache()
        self.db = DatabaseConnection()
        logger.info(f"Batch processor initialized with batch size: {batch_size}, sleep interval: {sleep_interval}s")
    
    def run(self):
        """
        Run the batch processor indefinitely
        """
        logger.info("Starting batch processor...")
        
        while True:
            try:
                # Process a batch of events directly from the queue
                processed_count = self.process_batch()
                
                if processed_count == 0:
                    # If no events were processed, wait a bit before trying again
                    time.sleep(self.sleep_interval)
                else:
                    # If we processed events, continue immediately to check for more
                    logger.info(f"Processed {processed_count} events, checking for more...")
                    # Add a small delay to prevent overwhelming the system
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Batch processing error: {str(e)}")
                # Wait before trying again after an error
                time.sleep(self.sleep_interval)
                    
    def process_batch(self):
        """
        Process a batch of events directly from the processed_events_queue
        """
        # Initialize connections
        redis_client = CameraCache()
        db_connection = DatabaseConnection()
        
        # Get processed events directly from the queue
        events = redis_client.get_processed_events(batch_size=self.batch_size)
        
        if not events:
            logger.info("No events to process, waiting...")
            return 0
        
        logger.info(f"Retrieved {len(events)} events to process")
        processed_count = 0
        
        # Process each event
        for event_data in events:
            try:
                # Extract data
                response = event_data.get('response')
                img_bytes = event_data.get('img_bytes')  # Already decoded by get_processed_events
                camera_id = event_data.get('camera_id')
                timestamp = event_data.get('timestamp')
                frame_id = event_data.get('frame_id')
                
                if not all([response, camera_id, timestamp]):
                    logger.warning(f"Missing required data in event: {event_data.keys()}")
                    continue
                    
                logger.info(f"Processing event: camera={camera_id}, timestamp={timestamp}, frame_id={frame_id}")
                
                # Save to database
                db_connection.save_camera_event(
                    response=response,
                    img_bytes=img_bytes,
                    camera_id=camera_id,
                    timestamp=timestamp
                )
                
                processed_count += 1
                
            except Exception as e:
                import traceback
                traceback.print_exc()
                logger.error(f"Error processing event: {str(e)}")
        
        logger.info(f"Successfully processed {processed_count} events")
        return processed_count


def main():
    """
    Main entry point for the batch processor
    """
    parser = argparse.ArgumentParser(description="Batch Processor for Camera Events")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of events to process in each batch")
    parser.add_argument("--sleep-interval", type=int, default=5, help="Sleep interval between batches in seconds")
    
    args = parser.parse_args()
    
    batch_processor = BatchProcessor(batch_size=args.batch_size, sleep_interval=args.sleep_interval)
    
    try:
        # Start the batch processor
        logger.info(f"Starting batch processor with batch size {args.batch_size} and sleep interval {args.sleep_interval}s")
        batch_processor.run()
    except KeyboardInterrupt:
        logger.info("Batch processor stopped by user")
    except Exception as e:
        logger.error(f"Fatal error in batch processor: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
