import time
from collections import Counter
from dotenv import load_dotenv
from typing import List, Dict

load_dotenv()

import cv2
import numpy as np
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from db_connection import DatabaseConnection
from license.license_checker import main as check_license
from redis_connection import CameraCache


CONFIG = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
THRESHOLD = CONFIG["TEMPORAL_CONTROL"]["THRESHOLD"]
DEBUG = CONFIG["TEMPORAL_CONTROL"].get("DEBUG", False)
SLEEP_TIME = CONFIG["TEMPORAL_CONTROL"].get("SLEEP_TIME", 300)

CAMERA_MAPPING = {
    "Cam01": "cam_one",
    "Cam02": "cam_two",
    "Cam03": "cam_three",
    "Cam04": "cam_four",
    "Cam05": "cam_five",
    "Cam06": "cam_six",
    "Cam07": "cam_seven",
    "Cam08": "cam_eight",
    "Cam09": "cam_nine",
}


def fast_deserialize(frame_bytes):
    """Deserialize grayscale image from binary payload"""
    nparr = np.frombuffer(frame_bytes, np.uint8)
    return cv2.imdecode(nparr, cv2.IMREAD_COLOR)


def load_camera_background_image(camera_name):
    try:
        default_bg = cv2.imread(f"va_camera_backgrounds/{camera_name}/default_bg.jpg")
        if default_bg is None:
            print(
                f"Unattended Objects: ⚠️ Could not load background image for {camera_name}"
            )
            return None
        return default_bg
    except Exception as e:
        print(
            f"Unattended Objects: ⚠️ Error loading background image for {camera_name}: {e}"
        )
        return None


# Initialize background images with error handling
BACKGROUND_IMAGE = {}
background_cameras = [
    "cam_one",
    "cam_two",
    "cam_three",
    "cam_four",
    "cam_five",
    "cam_eight",
    "cam_nine",
]

for cam in background_cameras:
    bg_image = load_camera_background_image(cam)
    if bg_image is not None:
        BACKGROUND_IMAGE[cam] = bg_image
        print(f"Unattended Objects: ✅ Loaded background image for {cam}")
    else:
        print(
            f"Unattended Objects: ⚠️ Skipping {cam} due to failed background image loading"
        )

print(
    f"Unattended Objects: 📸 Successfully loaded {len(BACKGROUND_IMAGE)} background images"
)


def encode_image_to_bytes(image: np.ndarray, ext: str = ".jpg") -> bytes:
    """
    Encode OpenCV image to bytes.

    Parameters:
        image (np.ndarray): BGR image
        ext (str): Extension format (e.g. ".jpg", ".png")

    Returns:
        bytes: Encoded image in byte format
    """
    success, encoded_image = cv2.imencode(ext, image)
    if not success:
        raise ValueError("Image encoding failed.")
    return encoded_image.tobytes()


def get_mode_cell_index(cell_idx, all_cells):
    averages = []
    for i, frame in enumerate(all_cells):
        cell = frame[cell_idx]
        avg = int(np.round(np.mean(cell)))
        averages.append((avg, i))
    freqs = Counter(avg for avg, _ in averages)
    mode_val, _ = freqs.most_common(1)[0]
    for avg_val, img_idx in averages:
        if avg_val == mode_val:
            return img_idx, mode_val


def save_modal_overlay_with_cv(
    modal_frame,
    mode_values,
    grid_rows,
    grid_cols,
    output_path,
):

    overlay = modal_frame.copy()
    h, w, _ = modal_frame.shape
    y_edges = np.linspace(0, h, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, w, grid_cols + 1, dtype=int)

    # Normalize mode_values for color scaling (0-255)
    vmin, vmax = mode_values.min(), mode_values.max()
    norm_vals = ((mode_values - vmin) / (vmax - vmin + 1e-5) * 255).astype(np.uint8)

    # Use OpenCV colormap (e.g., COLORMAP_VIRIDIS)
    colormap = cv2.COLORMAP_VIRIDIS

    for row in range(grid_rows):
        for col in range(grid_cols):
            val = norm_vals[row, col]
            y1, y2 = y_edges[row], y_edges[row + 1]
            x1, x2 = x_edges[col], x_edges[col + 1]

            # Get colormap color and blend
            color = cv2.applyColorMap(np.uint8([[val]]), colormap)[0][0].tolist()
            color_overlay = np.full((y2 - y1, x2 - x1, 3), color, dtype=np.uint8)

            # Blend with original image
            overlay[y1:y2, x1:x2] = cv2.addWeighted(
                overlay[y1:y2, x1:x2], 0.6, color_overlay, 0.4, 0
            )

            # Draw text
            text = str(mode_values[row, col])
            cv2.putText(
                overlay,
                text,
                (x1 + (x2 - x1) // 3, y1 + (y2 - y1) // 2),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (255, 255, 255),
                1,
                cv2.LINE_AA,
            )

    cv2.imwrite(output_path, overlay)


def build_temporal_modal_frame(
    frames,
    control_frame: np.ndarray,
    threshold: int,
    detections: List[Dict],
    grid_rows=25,
    grid_cols=40,
    debug=False,
    camera_name=None,
):
    h, w, _ = frames[0].shape
    y_edges = np.linspace(0, h, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, w, grid_cols + 1, dtype=int)

    # Step 1: Break all frames into cells
    all_cells = []
    for frame in frames:
        cells = []
        for row in range(grid_rows):
            for col in range(grid_cols):
                y1, y2 = y_edges[row], y_edges[row + 1]
                x1, x2 = x_edges[col], x_edges[col + 1]
                cells.append(frame[y1:y2, x1:x2, :])  # keep color
        all_cells.append(cells)

    # Step 2: Build modal frame from mode values
    modal_frame = np.zeros((h, w, 3), dtype=np.uint8)
    mode_values = np.zeros((grid_rows, grid_cols), dtype=int)

    for cell_idx in range(grid_rows * grid_cols):
        best_frame_idx, mode_val = get_mode_cell_index(cell_idx, all_cells)
        row, col = divmod(cell_idx, grid_cols)
        y1, y2 = y_edges[row], y_edges[row + 1]
        x1, x2 = x_edges[col], x_edges[col + 1]

        if mode_val < threshold:
            modal_frame[y1:y2, x1:x2, :] = control_frame[y1:y2, x1:x2, :]
        else:
            modal_frame[y1:y2, x1:x2, :] = all_cells[best_frame_idx, cell_idx]

        mode_values[row, col] = mode_val

    # Step 3: Overwrite detected regions with control frame
    for det in detections:
        if "original_bbox" in det:
            x1, y1, x2, y2 = map(int, det["original_bbox"])
            modal_frame[y1:y2, x1:x2, :] = control_frame[y1:y2, x1:x2, :]

    # Step 4: Optional Debug Output
    if debug:
        time_str = time.strftime("%Y%m%d-%H%M%S")
        safe_camera_name = camera_name or "unknown_cam"
        save_modal_overlay_with_cv(
            modal_frame,
            mode_values,
            grid_rows,
            grid_cols,
            f"debug/{safe_camera_name}_{time_str}_overlay.jpg",
        )

    return modal_frame


def main():
    check_license()

    redis_client = CameraCache()

    while True:
        camera_data = DatabaseConnection().get_camera_info()
        for camera in camera_data:
            camera_id = camera["id"]
            camera_name = camera["name"]
            print(f"Processing camera: {camera_name} (ID: {camera_id})")
            frames = redis_client.get_all_frames_by_camera(camera_id)
            total = len(frames)
            if not frames or len(frames) < 300:
                print("Not enough frames for temporal control")
                time.sleep(SLEEP_TIME)
                continue

            indices = np.linspace(0, total - 1, 120, dtype=int)
            cv_frames = []
            for i in indices:
                try:
                    img = fast_deserialize(frames[i])
                    if img is not None:
                        cv_frames.append(img)
                except Exception as e:
                    print(
                        f"Warning: Could not decode frame {i} from {camera_name}: {e}"
                    )
            cam_folder_name = CAMERA_MAPPING[camera_name]

            # First try to get the background image from Redis
            redis_bg_key = f"{cam_folder_name}:background_image"
            redis_bg = redis_client.get(redis_bg_key)

            if redis_bg:
                try:
                    # Deserialize the background image from Redis
                    bg_bytes = redis_client.from_jsonable_bytes(redis_bg)
                    control_frame = fast_deserialize(bg_bytes)
                    print(f"Using background image from Redis for {camera_name}")
                except Exception as e:
                    print(f"Error loading Redis background for {camera_name}: {e}")
                    # Fall back to disk-based background
                    control_frame = BACKGROUND_IMAGE.get(cam_folder_name)
                    if control_frame is None:
                        print(
                            f"No background image available for {camera_name}, skipping"
                        )
                        continue
            else:
                # Use disk-based background image
                control_frame = BACKGROUND_IMAGE.get(cam_folder_name)
                if control_frame is None:
                    print(f"No background image available for {camera_name}, skipping")
                    continue
                print(f"Using disk-based background image for {camera_name}")

            if frames is None:
                continue

            detections = redis_client.get_all_unattended_detections(camera_id)

            modal_frame = build_modal_frame(
                cv_frames,
                control_frame,
                THRESHOLD,
                detections,
                debug=DEBUG,
                camera_name=camera_name,
            )
            image_bytes = encode_image_to_bytes(modal_frame)
            if DEBUG:
                time_str = time.strftime("%Y%m%d-%H%M%S")
                cv2.imwrite(f"debug/{camera_name}_{time_str}.jpg", modal_frame)
            else:
                print("Debug is False, skipping saving modal frame")
            image_bytes = redis_client.to_jsonable_bytes(image_bytes)
            redis_client.set(redis_bg_key, image_bytes, ttl=600)
            time.sleep(SLEEP_TIME)


if __name__ == "__main__":
    main()
