import json
import os
import redis
import base64
from typing import Any, Optional, Dict, List, Union
from collections import defaultdict
import numpy as np
import cv2
import torch
import io
import traceback
import PIL.Image

class CameraCache:
    """
    Key–value cache for per-camera, per-time-frame data in Redis.

    Key format
    ----------
        <camera_id>_<timeframe>            e.g. "camA_20250526T011513"
        <camera_id>_<timeframe>_<layer_id> e.g. "camA_20250526T011513_L1"

    Examples
    --------
    >>> cache = CameraCache(ttl_default=600)
    >>> cache.set("camA", "20250526T011500", {"boxes": []})
    >>> cache.set("camA", "20250526T011500", {"mask": []}, layer_id="seg")
    >>> cache.get("camA", "20250526T011500")
    {'boxes': []}
    >>> cache.get("camA", "20250526T011500", layer_id="seg")
    {'mask': []}
    """

    # ------------------------------------------------------------------ #
    #  Construction / connection
    # ------------------------------------------------------------------ #

    def __init__(
        self,
        host: str | None = None,
        port: int | None = None,
        db: int | None = None,
        password: str | None = None,
        ttl_default: int = 6000,
        camera_id: str | None = None,
    ) -> None:
        print("Connecting to Redis...")
        print(f"Host: {host or os.getenv('REDIS_HOST')}")
        print(f"Port: {port or int(os.getenv('REDIS_PORT'))}")
        print(f"DB: {db or int(os.getenv('REDIS_DB'))}")
        
        # Configure Redis connection for low latency
        self._redis = redis.Redis(
            host=host or os.getenv("REDIS_HOST"),
            port=port or int(os.getenv("REDIS_PORT")),
            db=db or int(os.getenv("REDIS_DB")),
            password=password or os.getenv("REDIS_PASS"),
            socket_keepalive=True,
            decode_responses=True,  # str in / str out so json loads easily
        )
        self._ttl_default = ttl_default
        print("Connected to Redis")

        # For streaming frame for the frontend
        self.camera_id = camera_id
        self.channel_name = f"{self.camera_id}:live_stream"
        self.publish_key = f"{self.camera_id}:last"
        self.detection_layer_stream_key = f"detection_layer_stream"
        self.detection_layer_group_name = f"detection_layer_group"
        self.detection_layer_consumer_name = f"consumer-{os.getpid()}" 
        self.detection_layer_batch_size = 20
        self.detection_layer_timeout_ms = 200
        
        # Separate keys for List-based queue
        self.queue_key = f"{self.detection_layer_stream_key}_queue"
        self.processing_queue_key = f"{self.detection_layer_stream_key}_processing_{os.getpid()}"
        # self.setup_consumer_group()

    # ------------------------------------------------------------------ #
    #  Public API
    # ------------------------------------------------------------------ #

    def set(
        self,
        key: str,
        value: Any,
        *,
        ttl: Optional[int] = None,
    ) -> None:
        """
        Cache *value* under the standardised key for (*camera_id*, *timeframe*[, *layer_id*]).
        The key expires automatically after *ttl* seconds (default = self._ttl_default).
        """
        payload = json.dumps(value)
        self._redis.set(name=key, value=payload, ex=ttl or self._ttl_default)

    def get(
        self,
        key: str,
        *,
        default: Any = None,
    ) -> Any:
        """
        Retrieve the cached value or return *default* if the key is missing / expired.
        """
        raw = self._redis.get(key)
        return default if raw is None else json.loads(raw)

    # ------------------------------------------------------------------ #
    #  Redis Operations API
    # ------------------------------------------------------------------ #

    def zrangebyscore(self, key: str, min_score: float, max_score: str) -> List[str]:
        """
        Return elements with scores between min_score and max_score.
        """
        return self._redis.zrangebyscore(key, min_score, max_score)

    def hgetall(self, key: str) -> Dict[str, str]:
        """
        Get all fields and values in a hash.
        """
        return self._redis.hgetall(key)

    def pipeline(self) -> redis.client.Pipeline:
        """
        Create a pipeline for batching operations.
        """
        return self._redis.pipeline()

    def hset(self, key: str, mapping: Dict[str, Any]) -> None:
        """
        Set multiple hash fields to multiple values.
        """
        self._redis.hset(key, mapping=mapping)

    def expire(self, key: str, seconds: int) -> None:
        """
        Set a key's time to live in seconds.
        """
        self._redis.expire(key, seconds)

    def zadd(self, key: str, mapping: Dict[str, float]) -> None:
        """
        Add one or more members to a sorted set, or update its score if it already exists.
        """
        self._redis.zadd(key, mapping)

    # ------------------------------------------------------------------ #
    #  Internals
    # ------------------------------------------------------------------ #

    @staticmethod
    def build_key(camera_id: str, timeframe: str, layer_id: str | None) -> str:
        """
        Assemble the canonical Redis key string.
        """
        if layer_id:
            return f"{camera_id}_{timeframe}_{layer_id}"
        return f"{camera_id}_{timeframe}"
    
    # ---------- 1. binary → JSON-friendly ---------------------------------
    @staticmethod
    def to_jsonable_bytes(data: bytes | bytearray) -> str:
        """Encode arbitrary binary as a base-64 ASCII string."""
        return base64.b64encode(data).decode("ascii")

    # ---------- 2. JSON-friendly → binary ---------------------------------
    @staticmethod
    def from_jsonable_bytes(encoded: str) -> bytes:
        """Decode the ASCII base-64 string back to the original bytes."""
        return base64.b64decode(encoded.encode("ascii"))
    
    @staticmethod
    def serialize_numpy_array(arr: np.ndarray) -> bytes:
        """Serializes a numpy array into a single byte string using numpy's native format."""
        with io.BytesIO() as f:
            np.save(f, arr, allow_pickle=False)
            return f.getvalue()

    @staticmethod
    def deserialize_numpy_array(data: bytes) -> np.ndarray:
        """Deserializes a numpy array from a byte string."""
        with io.BytesIO(data) as f:
            return np.load(f, allow_pickle=False)
    
    # Streaming frame fo the frontend
    def publish_latest_frame(self, frame_key, camera_id=None):
        payload = {
            "frame_key": frame_key,
        }

        pipe = self._redis.pipeline()

        if camera_id:
            channel_name = f"{camera_id}:live_stream"
            publish_key = f"{camera_id}:last"
        else:
            channel_name = self.channel_name
            publish_key = self.publish_key

        pipe.set(publish_key, json.dumps(payload))   # serialize to JSON string
        pipe.publish(channel_name, "")       # tiny notification; payload could be a frame-id
        
        pipe.execute()

    def subscribe_to_channel(self):
        ps = self._redis.pubsub()
        ps.subscribe(self.channel_name)
        
        return ps
    
    def send_to_stream(self, data: Dict[str, Any]):
        self._redis.xadd(self.detection_layer_stream_key, data)

    def setup_consumer_group(self):
        """Create the consumer group and stream if they don't exist."""
        try:
            # First ensure the stream exists by adding a dummy message if it doesn't
            try:
                self._redis.xlen(self.detection_layer_stream_key)
            except redis.exceptions.ResponseError:
                # Stream doesn't exist, create it with a dummy message
                self._redis.xadd(self.detection_layer_stream_key, {"init": "dummy"})
                print(f"Created stream '{self.detection_layer_stream_key}'")

            # Now create the consumer group
            try:
                self._redis.xgroup_create(
                    self.detection_layer_stream_key,
                    self.detection_layer_group_name,
                    id='0',
                    mkstream=False  # Stream already exists
                )
                print(f"Created consumer group '{self.detection_layer_group_name}'")
            except redis.exceptions.ResponseError as e:
                if "BUSYGROUP" in str(e):
                    print(f"Consumer group '{self.detection_layer_group_name}' already exists")
                else:
                    raise e

            print(f"Pipeline started for consumer '{self.detection_layer_consumer_name}'. Waiting for jobs...")
        except Exception as e:
            print(f"Error setting up consumer group: {e}")
            raise

    def get_from_stream(self):
        try:
            # First, check for any pending messages that might have been abandoned
            pending = self._redis.xpending(
                self.detection_layer_stream_key,
                self.detection_layer_group_name
            )
            
            # If there are pending messages, claim them
            if pending and pending["pending"] > 0:  # pending[0] is the total count
                # Get detailed pending messages
                pending_msgs = self._redis.xpending_range(
                    self.detection_layer_stream_key,
                    self.detection_layer_group_name,
                    '-',  # start
                    '+',  # end
                    self.detection_layer_batch_size,
                    self.detection_layer_consumer_name
                )
                
                if pending_msgs:
                    message_ids = [msg['message_id'] for msg in pending_msgs]
                    self._redis.xclaim(
                        self.detection_layer_stream_key,
                        self.detection_layer_group_name,
                        self.detection_layer_consumer_name,
                        0,  # min-idle-time
                        message_ids
                    )
                    print(f"Claimed {len(message_ids)} pending messages")
            
            else:
                print("No pending messages")
                return [], []
            
            # Now read new messages
            response = self._redis.xreadgroup(
                groupname=self.detection_layer_group_name,
                consumername=self.detection_layer_consumer_name,
                streams={self.detection_layer_stream_key: '>'}, # '>' for new messages
                count=self.detection_layer_batch_size,
                block=None
            )

            # Get the actual items from the response.
            items = response[0][1] if response else []
            
            # Extract just the message IDs for acknowledging later.
            message_ids = []
            all_cropped_data = []
            for item in items:
                message_ids.append(item[0])
                data = item[1]
                
                # Decode the image bytes from latin1 encoding
                if 'image_bytes' in data:
                    image_bytes = data['image_bytes'].encode('latin1')
                    nparr = np.frombuffer(image_bytes, np.uint8)
                    data['image_bytes'] = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                all_cropped_data.append(data)

            return message_ids, all_cropped_data
        
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            print(f"Error in get_from_stream: {e}")
            return [], []
        
    def acknowledge_message(self, message_ids: List[str]):
        """
        Acknowledge messages in the Redis stream.
        
        Args:
            message_ids: List of message IDs to acknowledge
        """
        if not message_ids:
            return
            
        # Acknowledge the messages
        self._redis.xack(
            self.detection_layer_stream_key, 
            self.detection_layer_group_name, 
            *message_ids
        )
        
        # Delete the acknowledged messages
        self._redis.xdel(self.detection_layer_stream_key, *message_ids)
        print(f"Acknowledged (and effectively 'deleted') {len(message_ids)} messages.")

    def push_to_queue(self, data: List[Dict[str, Any]], ttl: Optional[int] = 2) -> None:
        """
        Push a list of dictionaries as a single message to a Redis queue using LPUSH.
        
        Args:
            data: List of dictionaries to push as one message
            ttl: Optional time-to-live in seconds. If set, the message will be automatically
                 deleted after this many seconds. If None, the message will persist indefinitely.
        """
        try:
            # Serialize the list of dictionaries to JSON string
            message = json.dumps(data)
            self._redis.lpush(self.queue_key, message)
            
            # Set expiration if ttl is provided
            if ttl is not None:
                self._redis.expire(self.queue_key, ttl)
        except Exception as e:
            print(f"Error pushing to queue {self.queue_key}: {str(e)}")
            raise

    def process_queue(self, timeout: int = 0) -> Optional[List[Dict[str, Any]]]:
        """
        Atomically pop an item from source queue and push to destination queue using BRPOPLPUSH.
        Then remove the processed item from destination queue using LREM.
        
        Args:
            timeout: How long to block waiting for data (0 = indefinite)
            
        Returns:
            List of dictionaries if message found, None if timeout
        """
        try:
            # Atomically move message from source to dest queue
            # message = self._redis.brpoplpush(self.queue_key, self.processing_queue_key, timeout)

            message = self._redis.blmove(
                self.queue_key,            # source list
                self.processing_queue_key, # dest list
                src='LEFT',                # pop from head (newest)
                dest='LEFT',               # push onto head
                timeout=timeout            # block up to `timeout` seconds
            )
            
            if message is None or len(message) == 0:
                return {}
                
            # Parse the JSON message back to list of dictionaries
            data = json.loads(message)

            # for m in data:
            #     if 'image_bytes' in m.keys():
            #     #     image_bytes = m['image_bytes'].encode('latin1')
            #     #     nparr = np.frombuffer(image_bytes, np.uint8)
            #     #     m['image_bytes'] = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            #     # Convert list back to PyTorch tensor
            #         m['image_bytes'] = torch.tensor(m['image_bytes'])
            #     if "original_coordinate" in m.keys() and isinstance(m["original_coordinate"], str):
            #         m["original_coordinate"] = json.loads(m["original_coordinate"])
            #     if "original_image_bytes" in m.keys() and isinstance(m["original_image_bytes"], str):
            #         image_bytes = m['original_image_bytes'].encode('latin1')
            #         nparr = np.frombuffer(image_bytes, np.uint8)
            #         m['original_image_bytes'] = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            #     if "image_shape" in m.keys() and isinstance(m["image_shape"], str):
            #         m['image_shape'] = json.loads(m["image_shape"])

            # Only get 1 message at a time
            data = data[0]

            # Get the image from the redis
            image_cache = self.get(f"{data['frame_key']}:gammy:image_resized")
            
            # Decode the base64-encoded image data back to PIL Image
            if image_cache and 'image_resized' in image_cache:
                try:
                    # Decode base64 string back to bytes
                    image_bytes = self.from_jsonable_bytes(image_cache['image_resized'])
                    
                    # Verify we got actual bytes
                    if isinstance(image_bytes, bytes):
                        # Convert bytes to PIL Image
                        image_cache['image_resized'] = PIL.Image.open(io.BytesIO(image_bytes))
                    else:
                        print(f"Warning: Expected bytes but got {type(image_bytes)} for image_resized")
                        # Try to handle as string if it's not bytes
                        if isinstance(image_cache['image_resized'], str):
                            # Maybe it's already a string representation, try direct conversion
                            try:
                                image_cache['image_resized'] = PIL.Image.open(io.BytesIO(image_cache['image_resized'].encode('latin1')))
                            except Exception as e2:
                                print(f"Failed to convert string to image: {e2}")
                                raise e2    # TODO: Remove this
                                # Set to None if we can't decode it
                                image_cache['image_resized'] = None
                        else:
                            image_cache['image_resized'] = None
                except Exception as e:
                    print(f"Error decoding image_resized: {e}")
                    print(f"Type of image_resized: {type(image_cache['image_resized'])}")
                    # Set to None if we can't decode it
                    image_cache['image_resized'] = None
            
            # Add the key value pairs to the data
            data.update(image_cache)
            
            # Remove the processed message from dest queue
            self._redis.lpop(self.processing_queue_key)

            if isinstance(data, list):
                return data[0]
            else:
                return data
            
        except Exception as e:
            print(f"Error processing queues {self.queue_key} -> {self.processing_queue_key}: {str(e)}")
            
            traceback.print_exc()
            return {}

