from __future__ import annotations

from dotenv import load_dotenv

load_dotenv()

import numpy as np
import cv2
import torch
import os
import logging
import time
from typing import List, Tuple, Dict, Union, Any, Optional
import json
from PIL import Image
import groundingdino.datasets.transforms as T
from functions.gammy.common_func import preprocess

from logger_setup import setup_logging
from functions.gammy.common_func import (
    load_dino_model,
    load_image_from_bytes,
    predict_dino_model,
    predict_dino_model_fast,
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

from redis_connection import CameraCache

from license.license_checker import main as license_checker

# Initialize models in main process
DEVICE = None
MODELS = None


def initialize_models():
    """Initialize models in the worker process"""
    if not torch.cuda.is_available():
        logger.warning("CUDA is not available. Using CPU instead.")
        device = "cpu"
    else:
        device = "cuda"
        logger.info("CUDA is available. Using GPU.")

    logger.info(f"Initializing models on device: {device}")
    models = {"gammy_model": load_dino_model(device)}

    logger.info("All models initialized successfully")
    return models, device


Canvas = np.ndarray
CoordMap = Dict[int, Tuple[int, int, int, int]]  # idx → (x1,y1,x2,y2)

CANVAS_W, CANVAS_H = 1920, 1080
PAD_DEFAULT = 12
PAD_COLOUR_DEFAULT = 0  # black


# ─────────────────────────  helpers  ───────────────────────────────────────
def _pad(img: np.ndarray, pad: int, colour: int | Tuple[int, int, int]) -> np.ndarray:
    """Return *new* array with constant-colour border (faster than cv2)."""
    h, w = img.shape[:2]
    if img.ndim == 3:
        padded = np.full((h + 2 * pad, w + 2 * pad, 3), colour, img.dtype)
        padded[pad : pad + h, pad : pad + w, :] = img
    else:
        padded = np.full((h + 2 * pad, w + 2 * pad), colour, img.dtype)
        padded[pad : pad + h, pad : pad + w] = img
    return padded


def _rect_fits(rect: Tuple[int, int], free: Tuple[int, int]) -> bool:
    rh, rw = rect
    fh, fw = free
    return rh <= fh and rw <= fw


def _can_fit_in_canvas(
    images: List[np.ndarray], pad: int, canvas_w: int, canvas_h: int
) -> bool:
    """
    Pre-validate if all images can fit in a single canvas.
    This is a conservative check - actual packing might still fail.
    """
    if not images:
        return True

    total_area = 0
    max_width = 0
    max_height = 0

    print(f"\n=== Canvas Size Analysis ===")
    print(f"Canvas dimensions: {canvas_w}x{canvas_h} = {canvas_w * canvas_h} pixels")
    print(f"Padding per image: {pad} pixels")

    for i, img in enumerate(images):
        h, w = img.shape[:2]
        padded_h, padded_w = h + 2 * pad, w + 2 * pad
        area = padded_h * padded_w
        total_area += area
        max_width = max(max_width, padded_w)
        max_height = max(max_height, padded_h)

        print(f"Image {i}: {w}x{h} -> {padded_w}x{padded_h} (area: {area:,} pixels)")

    print(f"\n=== Summary ===")
    print(f"Total area needed: {total_area:,} pixels")
    print(f"Canvas area available: {canvas_w * canvas_h:,} pixels")
    print(f"Largest single image: {max_width}x{max_height}")

    # Check if any single image is too large
    if max_width > canvas_w or max_height > canvas_h:
        print(
            f"❌ FAIL: Largest image ({max_width}x{max_height}) exceeds canvas ({canvas_w}x{canvas_h})"
        )
        return False

    # Check if total area exceeds canvas area (conservative check)
    canvas_area = canvas_w * canvas_h
    if total_area > canvas_area:
        print(
            f"❌ FAIL: Total area needed ({total_area:,}) exceeds canvas area ({canvas_area:,})"
        )
        print(f"   This is a conservative check - actual packing might still work")
        return False

    print(f"✅ PASS: All images should fit in canvas")
    return True


def _calculate_iou(
    box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]
) -> float:
    """
    Calculate Intersection over Union (IoU) between two bounding boxes.
    Boxes are in format (x1, y1, x2, y2).
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0  # No intersection

    intersection = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection

    return intersection / union if union > 0 else 0.0


def _combine_overlapping_boxes(
    boxes: List[Tuple[int, int, int, int]], iou_threshold: float = 0.5
) -> List[Tuple[int, int, int, int]]:
    """
    Combine overlapping bounding boxes with IoU > threshold.
    Returns a list of combined boxes.
    """
    if not boxes:
        return []

    # Convert to list for modification
    remaining_boxes = list(boxes)
    combined_boxes = []

    while remaining_boxes:
        current_box = remaining_boxes.pop(0)
        combined = False

        # Check if current box overlaps significantly with any existing combined box
        for i, existing_box in enumerate(combined_boxes):
            iou = _calculate_iou(current_box, existing_box)
            if iou > iou_threshold:
                # Combine the boxes
                x1_1, y1_1, x2_1, y2_1 = current_box
                x1_2, y1_2, x2_2, y2_2 = existing_box

                combined_box = (
                    min(x1_1, x1_2),  # min x1
                    min(y1_1, y1_2),  # min y1
                    max(x2_1, x2_2),  # max x2
                    max(y2_1, y2_2),  # max y2
                )

                combined_boxes[i] = combined_box
                combined = True
                break

        if not combined:
            combined_boxes.append(current_box)

    return combined_boxes


def _calculate_optimal_canvas_size(
    images: List[np.ndarray], pad: int = 12
) -> Tuple[int, int]:
    """
    Calculate optimal canvas size to fit all images with minimal waste.
    Returns (width, height) that's close to 1920x1080 but can be larger if needed.
    """
    if not images:
        return 1920, 1080

    # Calculate total area needed (including padding)
    total_area = 0
    max_width = 0
    max_height = 0

    for img in images:
        h, w = img.shape[:2]
        padded_h, padded_w = h + 2 * pad, w + 2 * pad
        total_area += padded_h * padded_w
        max_width = max(max_width, padded_w)
        max_height = max(max_height, padded_h)

    # Start with target size
    target_w, target_h = 1920, 1080

    # If any image is larger than target, increase canvas size
    if max_width > target_w:
        target_w = max_width + pad * 2  # Add some extra padding

    if max_height > target_h:
        target_h = max_height + pad * 2  # Add some extra padding

    # If total area is much larger than target area, increase canvas size
    target_area = target_w * target_h
    if total_area > target_area * 0.8:  # If using more than 80% of target area
        # Calculate a more optimal size
        aspect_ratio = target_w / target_h
        optimal_width = int((total_area * aspect_ratio) ** 0.5)
        optimal_height = int(optimal_width / aspect_ratio)

        # Use the larger of target and optimal
        target_w = max(target_w, optimal_width)
        target_h = max(target_h, optimal_height)

    return target_w, target_h


def _simple_grid_pack(
    images: List[np.ndarray], canvas_w: int, canvas_h: int, pad: int = 12
) -> Tuple[np.ndarray, Dict[int, Tuple[int, int, int, int]]]:
    """
    Simple grid-based packing algorithm that places images in rows.
    Returns (canvas, coordinate_map).
    """
    if not images:
        return np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8), {}

    # Create canvas
    canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)
    coordinate_map = {}

    current_x = pad
    current_y = pad
    row_height = 0

    for i, img in enumerate(images):
        h, w = img.shape[:2]
        padded_h, padded_w = h + 2 * pad, w + 2 * pad

        # Check if image fits on current row
        if current_x + padded_w > canvas_w - pad:
            # Move to next row
            current_x = pad
            current_y += row_height + pad
            row_height = 0

        # Check if we need a new canvas (shouldn't happen with optimal sizing)
        if current_y + padded_h > canvas_h - pad:
            # Resize canvas to fit
            new_canvas_h = current_y + padded_h + pad
            new_canvas = np.zeros((new_canvas_h, canvas_w, 3), dtype=np.uint8)
            new_canvas[:canvas_h, :canvas_w] = canvas
            canvas = new_canvas
            canvas_h = new_canvas_h

        # Place image
        canvas[current_y : current_y + h, current_x : current_x + w] = img
        coordinate_map[i] = (current_x, current_y, current_x + w, current_y + h)

        # Update position
        current_x += padded_w
        row_height = max(row_height, padded_h)

    return canvas, coordinate_map


# ──────────────────────  simplified stitching API  ─────────────────────────
def stitch_images_simple(
    crops: List[np.ndarray],
    pad: int = 12,
    iou_threshold: float = 0.5,
) -> Tuple[np.ndarray, Dict[int, Tuple[int, int, int, int]]]:
    """
    Simplified stitching that combines overlapping boxes and ensures single canvas output.

    Args:
        crops: List of images to stitch
        pad: Padding around each image
        iou_threshold: IoU threshold for combining overlapping boxes

    Returns:
        Tuple of (canvas, coordinate_map) - guaranteed to fit all images
    """
    if not crops:
        return np.zeros((1080, 1920, 3), dtype=np.uint8), {}

    print(f"\n=== Simple Stitching Analysis ===")
    print(f"Original images: {len(crops)}")

    # Step 1: Extract original bounding boxes from crop_data_list
    # Since we don't have the original boxes here, we'll work with the images as-is
    # In practice, you'd want to pass the original bounding boxes and combine them first

    # Step 2: Calculate optimal canvas size
    canvas_w, canvas_h = _calculate_optimal_canvas_size(crops, pad)
    print(f"Optimal canvas size: {canvas_w}x{canvas_h}")

    # Step 3: Simple grid packing
    canvas, coordinate_map = _simple_grid_pack(crops, canvas_w, canvas_h, pad)

    print(f"Successfully packed {len(crops)} images into {canvas_w}x{canvas_h} canvas")
    return canvas, coordinate_map


def combine_overlapping_crops(
    crop_data_list: List[Dict[str, Any]], iou_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """
    Combine overlapping crops in the crop data list based on their original coordinates.

    Args:
        crop_data_list: List of crop data dictionaries
        iou_threshold: IoU threshold for combining overlapping boxes

    Returns:
        List of combined crop data dictionaries
    """
    if not crop_data_list:
        return []

    # Extract original bounding boxes
    boxes = []
    for data in crop_data_list:
        if "original_coordinate" in data:
            coords = data["original_coordinate"]
            if isinstance(coords, str):
                coords = json.loads(coords)
            boxes.append(tuple(coords))
        else:
            # If no original coordinates, use image dimensions as placeholder
            img = data["image_bytes"]
            h, w = img.shape[:2]
            boxes.append((0, 0, w, h))

    # Combine overlapping boxes
    combined_boxes = _combine_overlapping_boxes(boxes, iou_threshold)

    print(f"Combined {len(boxes)} boxes into {len(combined_boxes)} boxes")

    # Create new crop data list with combined boxes
    # For now, we'll return the original list since we can't easily recreate the crops
    # In practice, you'd want to do this combination at the source where the crops are created
    return crop_data_list


def stitch_and_update_data_simple(
    crop_data_list: List[Dict[str, Any]],
    iou_threshold: float = 0.5,
) -> Tuple[List[np.ndarray], List[Dict[str, Dict[str, Any]]]]:
    """
    Simplified version that always returns a single stitched canvas.

    Args:
        crop_data_list: List of crop data dictionaries
        iou_threshold: IoU threshold for combining overlapping boxes

    Returns:
        Tuple of (stitched_images, coordinates_dicts) - always single canvas
    """
    if not crop_data_list:
        return [np.zeros((1080, 1920, 3), dtype=np.uint8)], [{}]

    # Combine overlapping crops first
    combined_crop_data = combine_overlapping_crops(crop_data_list, iou_threshold)

    # Extract images
    images_to_stitch = [data["image_bytes"] for data in combined_crop_data]

    # Stitch images
    stitched_image, coordinate_map = stitch_images_simple(
        images_to_stitch, iou_threshold=iou_threshold
    )

    # Create coordinate dictionary
    coordinates_dict = {}
    for i, data_dict in enumerate(combined_crop_data):
        if i in coordinate_map:
            box_name = data_dict["box_name"]
            coordinates_dict[box_name] = {
                **data_dict,
                "temporary_coordinates": coordinate_map[i],
            }

    return [stitched_image], [coordinates_dict]


# ──────────────────────  legacy functions (simplified)  ─────────────────────────
def stitch_images(
    crops: List[np.ndarray],
    pad: int = 12,
    pad_colour: int = 0,
    canvas_w: int = 1920,
    canvas_h: int = 1080,
) -> List[Tuple[np.ndarray, Dict[int, Tuple[int, int, int, int]]]]:
    """
    Legacy function - now uses simple stitching approach.
    Always returns single canvas.
    """
    canvas, coordinate_map = stitch_images_simple(crops, pad)
    return [(canvas, coordinate_map)]


def stitch_and_update_data(
    crop_data_list: List[Dict[str, Any]],
) -> Tuple[List[np.ndarray], List[Dict[str, Dict[str, Any]]]]:
    """
    Legacy function - now uses simple stitching approach.
    Always returns single canvas.
    """
    return stitch_and_update_data_simple(crop_data_list)


def load_image_from_array(
    image_array: np.ndarray,  # H × W × 3, uint8
    bgr: bool = False,  # True ➜ convert BGR→RGB
    device: Optional[str] = None,  # "cuda", "cpu", or None
) -> Tuple[np.ndarray, torch.Tensor]:
    """
    Take an in-memory NumPy image and return:
      • the original array (RGB, uint8)
      • a DINO-ready, normalised Torch tensor (C×H×W, float32)
    """
    if bgr:  # OpenCV default → RGB
        image_array = image_array[..., ::-1]

    # keep a copy in case caller mutates tensor later
    image_np = image_array.copy()  # (H, W, 3), uint8

    # convert to PIL for torchvision detection-style transforms
    image_pil = Image.fromarray(image_np)

    transform = T.Compose(
        [
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),  # → float32 0-1, C×H×W
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
        ]
    )

    image_tensor, _ = transform(image_pil, None)

    if device:
        image_tensor = image_tensor.to(device)

    return image_np, image_tensor


def main():
    # Initialize models when the worker starts
    global MODELS, DEVICE
    if MODELS is None:
        try:
            logger.info("Initializing models in worker process...")
            MODELS, DEVICE = initialize_models()
            logger.info("Models initialized successfully in worker process")
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
            raise

    redis_client = CameraCache()
    print("\n=== Detection Layer Process Started ===")
    print(f"Device being used: {DEVICE}")

    # Read the gammy yaml
    # prompt = get_cardinal_prompt("weapon")
    # print(f"\nUsing prompt: {prompt}")

    # Get the items from the stream
    while True:
        # TODO: Too slow, need to fix
        # print("\n=== Waiting for new messages from Redis stream ===")
        # message_ids, all_cropped_data = redis_client.get_from_stream()
        # print(f"Received {len(message_ids)} messages from stream")

        # if len(message_ids) == 0:
        #     print("No messages in stream, continuing...")
        #     continue

        # Get from queue with a short timeout to avoid long blocking
        logger.info("Getting from queue")
        start_queue_time = time.time()
        # Use a shorter timeout (1 second) to avoid long blocking
        all_cropped_data = redis_client.process_queue(timeout=0.3)
        end_queue_time = time.time()

        if not all_cropped_data:
            print("No cropped data, continuing...")
            continue

        # Get the image_tensor
        start_preprocess_time = time.time()
        resized_image = all_cropped_data.get("image_resized")
        frame_key = all_cropped_data.get("frame_key")
        print(f"Received {frame_key} messages from queue")
        resized_image_shape_str = all_cropped_data.get("resized_image_shape")
        original_image_shape_str = all_cropped_data.get("original_image_shape")
        cam_id = all_cropped_data.get("camera_id")

        # Check if we have a valid image
        if resized_image is None:
            print(f"Warning: No image_resized found for frame_key {frame_key}")
            continue

        # Convert shape strings back to tuples
        try:
            resized_image_shape = (
                json.loads(resized_image_shape_str) if resized_image_shape_str else None
            )
            original_image_shape = (
                json.loads(original_image_shape_str)
                if original_image_shape_str
                else None
            )
        except (json.JSONDecodeError, TypeError) as e:
            print(f"Error parsing image shapes for frame_key {frame_key}: {e}")
            print(f"resized_image_shape_str: {resized_image_shape_str}")
            print(f"original_image_shape_str: {original_image_shape_str}")
            continue

        try:
            image_tensor = preprocess(resized_image)
        except Exception as e:
            print(f"Error preprocessing image for frame_key {frame_key}: {e}")
            print(f"Image type: {type(resized_image)}")
            if hasattr(resized_image, "size"):
                print(f"Image size: {resized_image.size}")
            continue

        # Validate image shapes
        if not resized_image_shape or not original_image_shape:
            print(f"Warning: Missing image shapes for frame_key {frame_key}")
            print(f"resized_image_shape: {resized_image_shape}")
            print(f"original_image_shape: {original_image_shape}")
            continue

        if (
            not isinstance(resized_image_shape, (list, tuple))
            or len(resized_image_shape) != 2
        ):
            print(
                f"Warning: Invalid resized_image_shape format for frame_key {frame_key}: {resized_image_shape}"
            )
            continue

        if (
            not isinstance(original_image_shape, (list, tuple))
            or len(original_image_shape) != 2
        ):
            print(
                f"Warning: Invalid original_image_shape format for frame_key {frame_key}: {original_image_shape}"
            )
            continue

        end_preprocess_time = time.time()

        # image_tensor = all_cropped_data.get("image_bytes")
        # print(f"Image tensor shape: {image_tensor.shape}")
        # image_shape = all_cropped_data.get("image_shape")
        # frame_id = all_cropped_data.get("frame_id")

        # Start the gammy prediction
        start_predict_time = time.time()
        # print(f"MODEL: {MODELS['gammy_model']}")
        try:
            results = predict_dino_model_fast(
                MODELS["gammy_model"],
                cam_id,
                frame_key,
                resized_image_shape,
                original_image_shape,
                image_tensor,
                DEVICE,
            )
            print(f"Got {len(results)} detections from model")
        except Exception as e:
            print(f"Error in predict_dino_model_fast for frame_key {frame_key}: {e}")
            import traceback

            traceback.print_exc()
            continue
        end_predict_time = time.time()

        # print(f"\n=== Processing {len(all_cropped_data)} cropped images ===")

        # # Debug: Show image sizes
        # start_stitch_time = time.time()
        # print(f"\n=== Image Size Analysis ===")
        # for i, data in enumerate(all_cropped_data):
        #     if 'image_bytes' in data and isinstance(data['image_bytes'], np.ndarray):
        #         h, w = data['image_bytes'].shape[:2]
        #         print(f"Image {i} ({data.get('box_name', 'unknown')}): {w}x{h}")
        #     else:
        #         print(f"Image {i}: No valid image data found")
        # # Process the items
        # stitched_images, crop_data_lists = stitch_and_update_data_simple(all_cropped_data)
        # end_stitch_time = time.time()

        # print(f"Successfully created {len(stitched_images)} stitched images")
        # print(f"Stitched image shapes: {[image.shape for image in stitched_images]}")

        # Process each stitched image
        # all_results = []
        # start_predict_time = time.time()
        # for i, (stitched_image, crop_data_list) in enumerate(zip(stitched_images, crop_data_lists)):
        #     print(f"\n=== Processing stitched image {i+1}/{len(stitched_images)} ===")
        #     print(f"Image shape: {stitched_image.shape}")

        #     print("\n=== Loading and processing image ===")
        #     image_bytes, image_tensor = load_image_from_array(stitched_image, True, DEVICE)

        #     print("\n=== Running GAMMY model prediction ===")
        # results = predict_dino_model(MODELS["gammy_model"], image_bytes, image_tensor, prompt, DEVICE, crop_data_list)
        #     print(f"Got {len(results)} detections from model")

        #     all_results.extend(results)
        # end_predict_time = time.time()

        # Loop through all results and set cache
        print("\n=== Processing and caching results ===")
        # result_key = {}
        start_cache_time = time.time()
        # for result in all_results:
        #     if result["tagged_to_box"] not in result_key:
        #         result_key[result["tagged_to_box"]] = []
        #     result_key[result["tagged_to_box"]].append(result)
        #     print(f"Detection for box {result['tagged_to_box']}: {result['phrase']} (conf: {result['confidence']:.2f})")

        print("\n=== Setting results in Redis cache ===")
        # for box_name, results in result_key.items():
        #     print(f"Setting {len(results)} results for box {box_name}")
        #     redis_client.set(f"{box_name}:detection_layer_stream", results)

        print(f"Setting {len(results)} results for box {frame_key}")
        try:
            redis_client.set(f"{frame_key}:gammy_output", results)
        except Exception as e:
            print(f"Error caching results for frame_key {frame_key}: {e}")
            import traceback

            traceback.print_exc()
            continue

        end_cache_time = time.time()

        print(f"\n=== Time taken ===")
        print(f"Queue time: {end_queue_time - start_queue_time} seconds")
        print(f"Preprocess time: {end_preprocess_time - start_preprocess_time} seconds")
        print(f"Predict time: {end_predict_time - start_predict_time} seconds")
        print(f"Cache time: {end_cache_time - start_cache_time} seconds")
        print(f"Total time: {end_cache_time - start_queue_time} seconds")
        logger.info("End of detection layer")

        # TODO: Too slow, need to fix
        # print("\n=== Acknowledging processed messages ===")
        # redis_client.acknowledge_message(message_ids)
        # print(f"Acknowledged {len(message_ids)} messages")
        # print("\n=== Batch processing complete ===\n")


if __name__ == "__main__":
    try:
        license_checker()
        main()
    except Exception as e:
        import traceback

        logger.error(f"Error in main: {str(e)}")
        traceback.print_exc()
