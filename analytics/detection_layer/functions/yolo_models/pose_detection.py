import json
try:
    from functions.yolo_models.commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
except ModuleNotFoundError:
    from commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image

MODEL_NAME = "yolov8s-pose"
DEVICE = check_cuda_availability()
# POSE_DETECTION_MODEL = load_yolo_model(MODEL_NAME, DEVICE) 


def predict(model, img, conf=0.5, device=DEVICE):
    # Use the specified device for prediction
    results = model.predict(img, conf=conf, device=device)
    return results


def get_pose_position(frame_bytes, model, conf=0.5, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, **kwargs):
    """Key points:
    1. Nose
    2. Left Eye
    3. Right Eye
    4. Left Ear
    5. Right Ear
    6. Left Shoulder
    7. Right Shoulder
    8. Left Elbow
    9. Right Elbow
    10. Left Wrist
    11. Right Wrist
    12. Left Hip
    13. Right Hip
    14. Left Knee
    15. Right Knee
    16. Left Ankle
    17. Right Ankle"""
        
    image_obj = bytes_to_image(frame_bytes)
    # Predict poses in the image
    results = predict(model, image_obj, conf)

    # Convert the results to a JSON-friendly format
    results = results[0].to_json()

    # Convert the results to a list
    results = json.loads(results)

    print(f"Results: {results}")

    # Initialize the detection results list
    detections = []

    # Map keypoint indices to names
    keypoint_names = [
        'Nose', 'Left Eye', 'Right Eye', 'Left Ear', 'Right Ear',
        'Left Shoulder', 'Right Shoulder', 'Left Elbow', 'Right Elbow',
        'Left Wrist', 'Right Wrist', 'Left Hip', 'Right Hip',
        'Left Knee', 'Right Knee', 'Left Ankle', 'Right Ankle'
    ]

    for result in results:
        # Get keypoint coordinates
        x_coords = result.get('keypoints', {}).get('x', [])
        y_coords = result.get('keypoints', {}).get('y', [])
        visible = result.get('keypoints', {}).get('visible', [])
        bbox = result.get('box', {})

        # Create mapped keypoints dictionary
        mapped_keypoints = {}
        x1, y1, x2, y2 = bbox.get('x1'), bbox.get('y1'), bbox.get('x2'), bbox.get('y2')
        for i, name in enumerate(keypoint_names):
            if i < len(x_coords) and i < len(y_coords):
                x = float(x_coords[i])
                y = float(y_coords[i])
                # Only include keypoint if it's inside the bounding box
                if x1 <= x <= x2 and y1 <= y <= y2:
                    mapped_keypoints[name] = {
                        'x': x,
                        'y': y,
                        'visible': bool(visible[i]) if i < len(visible) else True
                    }
        detection = {
            'object_name': result.get('name'),
            'confidence': result.get('confidence'),
            'coordinates': {
                'x1': float(x1),
                'y1': float(y1),
                'x2': float(x2),
                'y2': float(y2)
            },
            'keypoints': {
                'landmarks': mapped_keypoints
            },
            'style': {
                'rectangle_color': rectangle_color,
                'rectangle_thickness': rectangle_thickness,
                'text_thickness': text_thickness
            }
        }

        detections.append(detection)
    
    return detections 


if __name__ == "__main__":
    print("Downloading YOLO models...")
    download_models(MODEL_NAME)