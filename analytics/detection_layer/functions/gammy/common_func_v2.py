# try:
#     from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
# except:
#     try:
#         from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
#     except:
#         from groundingdino.util.inference import load_model, load_image, predict, annotate

import logging
import os
import time
import traceback
import uuid
from collections import defaultdict
from typing import List, Tuple
from pathlib import Path

import clip
import cv2
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import torchvision.transforms as T
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from groundingdino.util.inference import load_model, predict
from PIL import Image, ImageOps
import matplotlib.pyplot as plt
from torchvision.ops import box_convert  # torchvision ≥0.13

# Get logger
logger = logging.getLogger(__name__)

# Global variables for CLIP model and preprocessing
clip_model = None
clip_preprocess = None

MODEL_CFG = os.path.join(
    os.path.dirname(__file__), "groundingdino/config/GroundingDINO_SwinT_OGC.py"
)
# MODEL_CFG = './groundingdino/config/GroundingDINO_SwinT_OGC.py'
MODEL_WEIGHTS = os.path.join(
    os.path.dirname(__file__), "weights/groundingdino_swint_ogc.pth"
)
# MODEL_WEIGHTS = './weights/groundingdino_swint_ogc.pth'
# DETECTION_PROMPT = os.path.join(os.path.dirname(__file__), "detections-prompt.yml")


def load_dino_model(device):
    """Load the GroundingDINO model with detailed logging

    Args:
        device: Device to load the model on ('cuda' or 'cpu')

    Returns:
        The loaded model
    """

    start_time = time.time()
    try:
        model = load_model(MODEL_CFG, MODEL_WEIGHTS, device)
        end_time = time.time()
        logger.info(
            f"DINO model loaded successfully in {end_time - start_time:.2f} seconds"
        )
        return model
    except Exception as e:
        logger.error(f"Error loading DINO model: {str(e)}")
        logger.error(traceback.format_exc())
        raise


# def load_detection_prompt():
#     with open(DETECTION_PROMPT, "r") as f:
#         return yaml.safe_load(f)

# def get_cardinal_prompt(cardinal_requirements):
#     detection_prompt = load_detection_prompt()
#     return detection_prompt[cardinal_requirements]


# Loading in the config
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)

MAX_FRAMES = decrypted_config["GAMMY_CONFIGURATION"]["MAX_FRAMES"]
BOX_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["BOX_THRESHOLD"]
TEXT_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["TEXT_THRESHOLD"]
CATEGORY_MAP = decrypted_config["GAMMY_CONFIGURATION"]["CATEGORY_MAP"]
THRESHOLDS = decrypted_config["GAMMY_CONFIGURATION"]["THRESHOLDS"]
STABLE_MASK_DIFFERENCE_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"][
    "STABLE_MASK_DIFFERENCE_THRESHOLD"
]
STABLE_MASK_OVERLAP_PERCENTAGE = decrypted_config["GAMMY_CONFIGURATION"][
    "STABLE_MASK_OVERLAP_PERCENTAGE"
]

# suspicious person
GRID_ROWS = decrypted_config["SUSPICIOUS_PERSON"]["GRID_ROWS"]
GRID_COLS = decrypted_config["SUSPICIOUS_PERSON"]["GRID_COLS"]
SCALE = decrypted_config["SUSPICIOUS_PERSON"]["SCALE"]
RGB_THRESH = decrypted_config["SUSPICIOUS_PERSON"]["RGB_THRESH"]
MIN_AREA_PX = decrypted_config["SUSPICIOUS_PERSON"]["MIN_AREA_PX"]
CONTEXT_EXPAND_RATIO = decrypted_config["SUSPICIOUS_PERSON"]["CONTEXT_EXPAND_RATIO"]
YES_COUNT = decrypted_config["SUSPICIOUS_PERSON"]["YES_COUNT"]
SUSPICIOUS_THRESHOLD = decrypted_config["SUSPICIOUS_PERSON"]["SUSPICIOUS_THRESHOLD"]
CRITERIA_MET = decrypted_config["SUSPICIOUS_PERSON"]["CRITERIA_MET"]
CLIP_MIN_CONFIDENCE = decrypted_config["SUSPICIOUS_PERSON"]["CLIP_MIN_CONFIDENCE"]
CLIP_CONFIDENCE_THRESHOLD = decrypted_config["SUSPICIOUS_PERSON"]["CLIP_CONFIDENCE_THRESHOLD"]

ALL_TOKENS = [token for group in CATEGORY_MAP.values() for token in group]
TEXT_PROMPT = " . ".join(ALL_TOKENS)
SUSPICIOUS_PERSON_ATTRIBUTES = decrypted_config["SUSPICIOUS_PERSON"].get(
    "ATTRIBUTES", None
)

if not SUSPICIOUS_PERSON_ATTRIBUTES:
    SUSPICIOUS_PERSON_ATTRIBUTES = {
        "headgear": {
            "yes": [
            "person wearing a baseball cap",
            "person with a black cap on his head",
            "person with a hat on",
            "person wearing headgear",
            "person in a wide-brimmed hat",
            "person with a sunhat",
            "person covering their head with a cap"
            "person with a hat or cap that is wider than the head",
            ],
            "no": [
                "person with no hat",
                "person without anything on their head",
                "person not wearing any headgear"
                "i can see the top of their head"
                "i can see all of their hair"
            ]
        },
        "sunglasses": {
            "yes": [
                "person wearing dark sunglasses",
                "person with black sunglasses on",
                "person hiding their eyes with sunglasses",
                "person wearing large sunglasses",
                "person in reflective sunglasses"
            ],
            "no": [
                "person without sunglasses",
                "person with clear vision, no glasses",
                "person with no eyewear",
                "person not wearing sunglasses",
                "person without any glasses"
            ]
        },
        "jacket": {
            "yes": [
                "person wearing a thick jacket",
                "person with a jacket on",
                "person wearing a large coat",
                "person with a coat on",
                "person wearing a jacket or coat"
            ],
            "no": [
                "person not wearing a jacket",
                "person without a jacket",
                "person not wearing a coat",
                "person without a coat",
                "person not wearing a jacket or coat"
            ]
        },
        "long sleeves": {
            "yes": [    
                "person wearing long sleeves covering the forearms",
                "person with long sleeves on",
                "person wearing a long-sleeved shirt",
                "person with long sleeves in a shirt",
                "person wearing a long-sleeved top"
                "person wearing a long-sleeved jacket"
            ],
            "no": [
                "person wearing short sleeves",
                "person without long sleeves",
                "person wearing a short dress"
                "person wearing a short-sleeved shirt",
                "person wearing a mini-skirt",
            ]
        },
        "facemask": {
            "yes": [
                "person wearing a surgical mask",
                "person with a black face mask",
                "person covering their face with a mask",
                "person in a white medical mask",
                "person protecting their face with a cloth mask"
            ],
            "no": [
                "person not wearing a mask",
                "barefaced person",
                "person with no face covering",
                "person without a face mask",
                "uncovered face"
            ]
        },
        "oversized clothing": {
            "yes": [
                "a person in oversized clothes",
                "a man wearing baggy jeans and a large shirt",
                "a person with loose-fitting clothing",
                "a woman dressed in extra large clothes",
                "a person wearing an oversized hoodie"
            ],
            "no": [
                "a person in tight-fitting clothes",
                "a man wearing a slim-fit shirt and pants",
                "a woman dressed in fitted clothing",
                "a person in snug clothes",
                "someone wearing tailored garments"
                "a person with a t-shirt or polo shirt"
                "a man in smart office attire"
                "a lady in stylish office attire"
            ]
        },
        "bag/luggage": {
            "yes": [
                "a person carrying a roller suitcase in full view",
                "a person pulling luggage in full view",
                "a person holding a large travel bag in full view ",
                "a person walking with a suitcase in full view",
                "a person dragging a piece of luggage in full view",
                "a person with a large bag or haversack in full view"
            ],
            "no": [
                "a person with a normal sized bag",
                "a lady with a handbag or sling bag"
                "a lady with a small purse"
                "a person not carrying any bags",
                "a person with empty hands",
                "a person without any luggage",
                "a person walking without a bag",
                "a person not holding anything"
            ]
        },
    }

TOKEN_TO_CATEGORY = {
    token: category for category, tokens in CATEGORY_MAP.items() for token in tokens
}


DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
clip_model, clip_preprocess = clip.load("ViT-B/32", device=DEVICE)


def preprocess(image_pil, device):
    """
    Preprocess an image for model input.

    Args:
        image_pil (PIL.Image): PIL image to preprocess

    Returns:
        torch.Tensor: Preprocessed image tensor
    """
    try:
        # Create transform
        image_pil = np.array(image_pil)
        transform = T.Compose(
            [
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ]
        )

        # Apply transform
        start_time = time.time()
        image_tensor = transform(image_pil).unsqueeze(0).to(device)
        end_time = time.time()

        # Log tensor details
        logger.info(
            f"Preprocessed tensor shape: {image_tensor.shape}, dtype: {image_tensor.dtype}"
        )
        logger.info(f"Preprocessing completed in {(end_time - start_time)*1000:.2f} ms")

        return image_tensor
    except Exception as e:
        logger.error(f"Error in image preprocessing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def load_image_from_bytes(
    image_bgr: bytes, device: str = "cuda"
) -> Tuple[np.ndarray, torch.Tensor]:
    # Convert bytes to numpy array
    # nparr = np.frombuffer(image_bytes, np.uint8)
    # image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)

    # Convert to tensor more efficiently
    image_tensor = (
        torch.from_numpy(image_rgb)  # shape: H×W×C (uint8)
        .float()  # to float32
        .permute(2, 0, 1)  # to C×H×W
        .div(255.0)  # normalize to [0,1]
        .to(device)  # send to GPU/CPU
    )

    # Apply normalization for the model
    normalize = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    image_tensor = normalize(image_tensor)

    # Resize if needed (GroundingDINO expects 800px max dimension and minimum 400px)
    h, w = image_tensor.shape[1:]
    min_size = 400
    max_size = 800

    # Calculate scale to fit within max_size while maintaining aspect ratio
    scale = min(max_size / max(h, w), 1.0)

    # If image is too small, scale up to minimum size
    if min(h, w) * scale < min_size:
        scale = min_size / min(h, w)

    # Apply scaling if needed
    if scale != 1.0:
        h = int(h * scale)
        w = int(w * scale)
        image_tensor = F.interpolate(
            image_tensor.unsqueeze(0), size=(h, w), mode="bilinear", align_corners=False
        )[0]

    return image_rgb, image_tensor


def pixel_boxes(
    boxes: torch.Tensor, image: np.ndarray
) -> List[Tuple[int, int, int, int]]:
    h, w = image.shape[:2]
    scale = torch.tensor([w, h, w, h], dtype=boxes.dtype, device=boxes.device)
    return box_convert(boxes * scale, "cxcywh", "xyxy").round().int().cpu().tolist()


def calculate_iou(boxA, boxB):
    """
    Calculates the Intersection over Union (IoU) between two bounding boxes.
    Boxes are in [x1, y1, x2, y2] format.
    """
    # Determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # Compute the area of intersection
    intersection_area = max(0, xB - xA) * max(0, yB - yA)

    # Compute the area of both bounding boxes
    boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])

    # Compute the IoU
    iou = intersection_area / float(boxA_area + boxB_area - intersection_area)
    return iou


def predict_dino_model(
    model,
    original_rgb_np: np.ndarray,
    image_tensor: torch.Tensor,
    cfg: dict,
    device: str,
    coordinates: dict,
) -> List[dict]:
    """
    1) Run DINO predict(...) on the transformed tensor
    2) Get normalized boxes (cx,cy,w,h) + confidences + phrase labels
    3) Call pixel_boxes(..., original_rgb_np) to convert to correct pixel coords
    4) Build and return a list of dicts containing {state, bounding_box, reason}
    """
    # (A) Run inference on the tensor
    boxes_norm, logits, phrases = predict(
        model=model,
        image=image_tensor,
        caption=cfg["prompt"],
        box_threshold=cfg["box_threshold"],
        text_threshold=cfg["text_threshold"],
        device=device,
    )

    # (B) Convert normalized → absolute pixel coords using the ORIGINAL image's dimensions
    pixel_box_list = pixel_boxes(boxes_norm, original_rgb_np)

    # Iterate over each detected object
    final_tagged_detections = []
    for p_box, phrase, logit in zip(pixel_box_list, phrases, logits):

        highest_iou = -1
        tagged_box_name = None

        # For the current detection, find the best matching coordinate_box
        for box_name, mapping_info in coordinates.items():
            t_box = mapping_info["temporary_coordinates"]
            iou = calculate_iou(p_box, t_box)

            if iou > highest_iou:
                highest_iou = iou
                tagged_box_name = box_name

        # Now that the best match is found, calculate the delta
        delta = {}
        if tagged_box_name:
            matched_t_box = coordinates[tagged_box_name]["temporary_coordinates"]
            delta = {
                "delta_x1": p_box[0] - matched_t_box[0],
                "delta_y1": p_box[1] - matched_t_box[1],
                "delta_x2": p_box[2] - matched_t_box[2],
                "delta_y2": p_box[3] - matched_t_box[3],
            }

        # Append the result for the current detection to our final list
        final_tagged_detections.append(
            {
                "detection_box": p_box,
                "phrase": phrase,
                "confidence": round(logit.item(), 4),
                "tagged_to_box": tagged_box_name,
                "iou_score": round(highest_iou, 4),
                "relative_delta": delta,
            }
        )

    return final_tagged_detections


def group_predictions(boxes, phrases, scores):
    """
    Group model predictions by category.

    Args:
        boxes (torch.Tensor): Bounding boxes
        phrases (list): Text phrases
        scores (torch.Tensor): Confidence scores

    Returns:
        dict: Grouped predictions by category
    """
    grouped = defaultdict(list)
    for box, phrase, score in zip(boxes, phrases, scores):
        phrase_tokens = phrase.lower().split()
        matched = False
        for token in TOKEN_TO_CATEGORY:
            token_words = token.lower().split()
            if all(word in phrase_tokens for word in token_words):
                category = TOKEN_TO_CATEGORY[token]
                threshold = THRESHOLDS.get(category, TEXT_THRESHOLD)
                if score >= threshold:
                    grouped[category].append(
                        {"token": token, "phrase": phrase, "score": score, "box": box}
                    )
                matched = True
                break
        if not matched:
            grouped["unmatched"].append(
                {"token": "unknown", "phrase": phrase, "score": score, "box": box}
            )
    return grouped


def classify_clip_pil(img_array, device, target_attrs=None):
    print(
        f"========================== classify_clip_pil using {device}==================="
    )

    image_pil = Image.fromarray(img_array)

    img_tensor = clip_preprocess(image_pil).unsqueeze(0).to(device)
    with torch.no_grad():
        img_feat = clip_model.encode_image(img_tensor)

    results = {}
    yes_confidences = []

    for attr, prompts in SUSPICIOUS_PERSON_ATTRIBUTES.items():
        if target_attrs and attr not in target_attrs:
            continue
        
        yes_sim = clip_model.encode_text(clip.tokenize(prompts["yes"]).to(device))
        no_sim = clip_model.encode_text(clip.tokenize(prompts["no"]).to(device))

        score_yes = torch.nn.functional.cosine_similarity(img_feat, yes_sim).mean().item()
        score_no = torch.nn.functional.cosine_similarity(img_feat, no_sim).mean().item()

        confidence = abs(score_yes - score_no)
        if confidence < CLIP_MIN_CONFIDENCE:
            verdict = "unsure"
        else:
            verdict = "yes" if score_yes > score_no else "no"

        if verdict == "yes" and confidence >= CLIP_CONFIDENCE_THRESHOLD:
            yes_confidences.append(confidence)

        results[attr] = {
            "verdict": verdict,
            "score_yes": round(score_yes, 4),
            "score_no": round(score_no, 4),
            "confidence": round(confidence, 4)
        }

    if len(yes_confidences) >= CRITERIA_MET and sum(yes_confidences) >= SUSPICIOUS_THRESHOLD:
        results["__final_decision__"] = f"SUSPICIOUS PERSON DETECTED {sum(yes_confidences)}"
    else:
        results["__final_decision__"] = f"NO THREAT {sum(yes_confidences)}"

    return results


def cellwise_difference_mask(
    ctrl_crop_rgb, curr_crop_rgb, grid_size=(40, 25), rgb_thresh=15
):
    h, w, _ = ctrl_crop_rgb.shape
    cell_h = h // grid_size[1]
    cell_w = w // grid_size[0]

    # Initialize blank mask
    mask = np.zeros((h, w), dtype=np.uint8)

    for row in range(grid_size[1]):
        for col in range(grid_size[0]):
            y1 = row * cell_h
            y2 = (row + 1) * cell_h
            x1 = col * cell_w
            x2 = (col + 1) * cell_w

            ctrl_cell = ctrl_crop_rgb[y1:y2, x1:x2]
            curr_cell = curr_crop_rgb[y1:y2, x1:x2]

            # Flatten and get mean RGB
            ctrl_mean = np.mean(ctrl_cell.reshape(-1, 3), axis=0)
            curr_mean = np.mean(curr_cell.reshape(-1, 3), axis=0)

            # Euclidean distance
            dist = np.linalg.norm(curr_mean - ctrl_mean)

            if dist > rgb_thresh:
                mask[y1:y2, x1:x2] = 255

    return mask


def show_foreground_detections_cellwise(
    ctrl_img,
    curr_img,
    dets,
    scale=1.5,
    grid_size=(40, 25),
    rgb_thresh=10,
    min_area_px=40000,
    context_expand_ratio=0.2,
):
    """
    Extract and analyze foreground detections using cellwise difference masking.

    Args:
        ctrl_img: Control/background image (grayscale format)
        curr_img: Current image with potential detections (grayscale format)
        dets: List of detection dictionaries with 'bbox' key
        scale: Scale factor for bounding box coordinates
        grid_size: Tuple of (columns, rows) for cellwise analysis
        rgb_thresh: RGB distance threshold for detecting changes
        min_area_px: Minimum area in pixels for valid detections
        context_expand_ratio: Ratio to expand bounding boxes for context

    Returns:
        List of dictionaries containing detection results with attributes
    """
    # Fast path - return empty list for empty inputs
    if not dets:
        return []

    # Input validation
    if ctrl_img is None or curr_img is None:
        print("Control or current image is None")
        return []

    # Check if images have the same shape
    if ctrl_img.shape != curr_img.shape:
        print(f"Image shapes don't match: {ctrl_img.shape} vs {curr_img.shape}")
        return []

    try:
        H, W = ctrl_img.shape
        results = []

        # Ensure grid size is valid
        if grid_size[0] <= 0 or grid_size[1] <= 0:
            print(f"Invalid grid size: {grid_size}")
            return []

        # Pre-convert images to RGB once (if needed)
        if len(ctrl_img.shape) == 2:  # Grayscale
            ctrl_img_rgb = cv2.cvtColor(ctrl_img, cv2.COLOR_GRAY2RGB)
            curr_img_rgb = cv2.cvtColor(curr_img, cv2.COLOR_GRAY2RGB)
        else:
            ctrl_img_rgb = cv2.cvtColor(ctrl_img, cv2.COLOR_BGR2RGB)
            curr_img_rgb = cv2.cvtColor(curr_img, cv2.COLOR_BGR2RGB)

        # Process detections in batches
        batch_size = min(len(dets), 4)  # Process up to 4 detections at once
        for batch_start in range(0, len(dets), batch_size):
            batch_end = min(batch_start + batch_size, len(dets))
            batch = dets[batch_start:batch_end]

            for i, det in enumerate(batch):
                det_idx = batch_start + i
                try:
                    if "bbox" not in det:
                        print(f"Detection {det_idx+1} missing bbox key, skipping")
                        continue
                    
                    # Remove the scaling
                    # x1, y1, x2, y2 = [int(coord * scale) for coord in det["bbox"]]
                    x1, y1, x2, y2 = [int(coord) for coord in det["bbox"]]

                    # Validate bbox coordinates
                    if x1 >= x2 or y1 >= y2:
                        print(
                            f"Detection {det_idx+1} has invalid bbox: [{x1}, {y1}, {x2}, {y2}], skipping"
                        )
                        continue

                    # Expand bbox by context_expand_ratio with width/height adjustments
                    w = max(1, x2 - x1)  # Ensure positive width
                    h = max(1, y2 - y1)  # Ensure positive height
                    dw = int(w * context_expand_ratio * 3.5)
                    dh = int(h * context_expand_ratio / 2)

                    x1_exp = max(x1 - dw, 0)
                    x2_exp = min(x2 + dw, W)
                    y1_exp = max(y1 - dh, 0)
                    y2_exp = min(y2 + dh, H)

                    # Check if expanded box is valid
                    if x1_exp >= x2_exp or y1_exp >= y2_exp:
                        print(
                            f"Detection {det_idx+1} has invalid expanded bbox, skipping"
                        )
                        continue

                    # Extract crops directly from pre-converted RGB images
                    crop_ctrl = ctrl_img_rgb[y1_exp:y2_exp, x1_exp:x2_exp]
                    crop_curr = curr_img_rgb[y1_exp:y2_exp, x1_exp:x2_exp]

                    # Cellwise mask - ensure grid cells are at least 1 pixel
                    cell_h = max(1, crop_ctrl.shape[0] // grid_size[1])
                    cell_w = max(1, crop_ctrl.shape[1] // grid_size[0])

                    if cell_h == 0 or cell_w == 0:
                        print(
                            f"Detection {det_idx+1} has zero-sized grid cells, skipping"
                        )
                        continue

                    # # Generate mask and extract foreground
                    # mask = cellwise_difference_mask(
                    #     crop_ctrl,
                    #     crop_curr,
                    #     grid_size=grid_size,
                    #     rgb_thresh=rgb_thresh,
                    # )
                    # mask_bool = mask > 0
                    # mask_3ch = np.stack([mask_bool] * 3, axis=-1)

                    # foreground = np.where(mask_3ch, crop_curr, 255).astype(np.uint8)

                    # # Convert to PIL and auto-crop (faster with numpy operations)
                    # foreground_pil = Image.fromarray(foreground)
                    # fg_np = np.array(foreground_pil)

                    # # More efficient way to find non-white pixels
                    # non_white = np.where(np.any(fg_np != 255, axis=2))

                    # if len(non_white[0]) == 0:
                    #     print(
                    #         f"[Detection {det_idx+1}] Skipped: no visible foreground."
                    #     )
                    #     continue

                    # y0, x0 = np.min(non_white[0]), np.min(non_white[1])
                    # y1b, x1b = np.max(non_white[0]) + 1, np.max(non_white[1]) + 1
                    # foreground_pil = foreground_pil.crop((x0, y0, x1b, y1b))

                    # # Check minimum area threshold
                    # area = foreground_pil.width * foreground_pil.height
                    # if area < min_area_px:
                    #     print(
                    #         f"[Detection {det_idx+1}] Skipped: too small ({foreground_pil.width}x{foreground_pil.height} = {area}px²)"
                    #     )
                    #     continue

                    # print(
                    #     f"[Detection {det_idx+1}] Foreground size: ({foreground_pil.width}x{foreground_pil.height} = {area}px²)"
                    # )

                    try:
                        # Use global CLIP model for attribute classification
                        attrs = classify_clip_pil(crop_curr, DEVICE)

                        # Add detection result with attributes
                        detection_result = det.copy()
                        detection_result["attributes"] = attrs
                        results.append(detection_result)

                        # Print CLIP results
                        print(f"\n[CLIP Results for Detection {det_idx+1}] for {det['event_id']}")
                        for k, v in detection_result["attributes"].items():
                            print(f"{k:<20}: {v}")

                        # # Create output directory if it doesn't exist
                        # os.makedirs("output_logs", exist_ok=True)

                        # # Create a copy of the current crop for drawing
                        # output_img = crop_curr.copy()
                        # # Ensure output_img is 3-channel RGB
                        # if len(output_img.shape) == 2:  # If grayscale
                        #     output_img = cv2.cvtColor(output_img, cv2.COLOR_GRAY2RGB)
                        
                        # # Draw bounding box on the image
                        # h, w = output_img.shape[:2]
                        # cv2.rectangle(output_img, (0, 0), (w-1, h-1), (0, 255, 255), 2)

                        # # Create text area for attributes
                        # # Calculate required height for text area (one line per attribute + final decision)
                        # line_height = 30
                        # num_attrs = len(detection_result["attributes"]) 
                        # text_area_height = (num_attrs + 1) * line_height  # +1 for event_id
                        
                        # # Create white background for text
                        # text_area = np.ones((text_area_height, w, 3), dtype=np.uint8) * 255
                        
                        # # Add event ID at the top
                        # cv2.putText(text_area, f"Event ID: {det['event_id']}", 
                        #           (10, line_height-5), cv2.FONT_HERSHEY_SIMPLEX, 
                        #           0.7, (0, 0, 0), 2)
                        
                        # # Add attributes
                        # y_offset = line_height * 2  # Start after event ID
                        # for k, v in detection_result["attributes"].items():
                        #     text = f"{k}: {v}"
                        #     if isinstance(v, dict):
                        #         text = f"{k}: {v.get('verdict', 'N/A')} (conf: {v.get('confidence', 'N/A')})"
                        #     cv2.putText(text_area, text, 
                        #               (10, y_offset-5), cv2.FONT_HERSHEY_SIMPLEX, 
                        #               0.7, (0, 0, 0), 2)
                        #     y_offset += line_height

                        # # Combine image and text area
                        # combined_img = np.vstack([output_img, text_area])
                        
                        # # Save the combined image
                        # output_path = f"output_logs/detection_{det['event_id']}.jpg"
                        # cv2.imwrite(output_path, cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR))

                    except Exception as e:
                        print(f"[Detection {det_idx+1}] CLIP Error: {str(e)}")
                        print(traceback.format_exc())
                        # Add detection without attributes
                        detection_result = det.copy()
                        detection_result["attributes"] = {}
                        results.append(detection_result)

                except Exception as e:
                    print(f"Error processing detection {det_idx+1}: {str(e)}")
                    print(traceback.format_exc())
                    continue

        return results

    except Exception as e:
        print(f"Error in show_foreground_detections_cellwise: {str(e)}")
        print(traceback.format_exc())
        return []


def filter_and_classify_detections(
    ctrl_img,
    curr_img,
    detections,
):
    """
    Runs foreground detection + CLIP attribute classification, then filters based on:
    - bag/luggage must be 'yes'
    - at least 3 of 4 other attributes must be 'yes'
    """

    scale = SCALE
    grid_size = (GRID_ROWS, GRID_COLS)
    rgb_thresh = RGB_THRESH
    min_area_px = MIN_AREA_PX
    context_expand_ratio = CONTEXT_EXPAND_RATIO
    raw_results = show_foreground_detections_cellwise(
        ctrl_img=ctrl_img,
        curr_img=curr_img,
        dets=detections,
        scale=scale,
        grid_size=grid_size,
        rgb_thresh=rgb_thresh,
        min_area_px=min_area_px,
        context_expand_ratio=context_expand_ratio,
    )
    print("raw_results: ", raw_results)

    filtered_results = []
    for result in raw_results:
        attrs = result.get("attributes", {})
        if not attrs:
            continue
        
        # # Apply filter logic
        # # if MAIN_FILTER is not None and attrs.get(MAIN_FILTER) != "yes":
        # #     continue
        # yes_count = sum(1 for k, v in attrs.items() if v == "yes")

        # if yes_count >= YES_COUNT:
        #     filtered_results.append(result)

        # To output the image and the label and the attributes to a file. Draw the bounding box on the image.
        # image = curr_img.copy()
        # x1, y1, x2, y2 = [int(coord) for coord in result["original_bbox"]]
        # cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 255), 2)
        
        # # Add text with attributes
        # text = f"ID: {result['event_id']}\n"
        # if 'attributes' in result:
        #     for attr, value in result['attributes'].items():
        #         if isinstance(value, dict):
        #             text += f"{attr}: {value['verdict']}\n"
        #         else:
        #             text += f"{attr}: {value}\n"
                    
        # # Split text into lines and draw each line
        # y_offset = y1 - 10
        # for line in text.split('\n'):
        #     if line:
        #         cv2.putText(image, line, (x1, y_offset), 
        #                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        #         y_offset += 20
                
        # cv2.imwrite(f"output_logs/suspicious_person_{result['event_id']}.png", image)

        # Dropping those people where __final_decision__ contains "NO THREATS"
        if "__final_decision__" in attrs and "NO THREAT" in attrs["__final_decision__"]:
            print(f"Skipping result {result['event_id']}: {result} for category: {result['category']} because of __final_decision__ = {attrs['__final_decision__']}")
            continue
        
        else:
            print(f"Adding result {result['event_id']}: {result} for category: {result['category']}")
            # Drop attributes
            result.pop("attributes", None)
            print(f"Result after dropping attributes: {result}")
            filtered_results.append(result)

    return filtered_results


def annotate_smaller(image_source, boxes, logits, phrases):
    image = image_source.copy()
    h, w = image.shape[:2]
    boxes = boxes * torch.tensor([w, h, w, h])
    xyxy = boxes.clone()
    xyxy[:, 0] -= xyxy[:, 2] / 2  # cxcywh → x1y1x2y2
    xyxy[:, 1] -= xyxy[:, 3] / 2
    xyxy[:, 2] += xyxy[:, 0]
    xyxy[:, 3] += xyxy[:, 1]
    xyxy = xyxy.int().tolist()

    for (x1, y1, x2, y2), score, label in zip(xyxy, logits.tolist(), phrases):
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 255), 2)
        cv2.putText(
            image,
            label,
            (x1 + 2, y1 + 12),  # smaller y offset
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=0.35,  # smaller font
            color=(0, 255, 255),
            thickness=1,
            lineType=cv2.LINE_AA,
        )
    return image


def is_contained(inner, outer):
    """Check if the inner box is completely inside the outer box."""
    return (
        inner[0] >= outer[0]
        and inner[1] >= outer[1]
        and inner[2] <= outer[2]
        and inner[3] <= outer[3]
    )


def remove_nested_boxes(df):
    # split between unattended_object and others

    unattended_object_df = df[df["category"] == "unattended_object"]
    other_df = df[df["category"] != "unattended_object"]

    unattended_object_bboxes = unattended_object_df["bbox"].tolist()
    unattended_object_indices = unattended_object_df.index.tolist()
    unattended_object_keep = [True] * len(unattended_object_bboxes)

    for i in range(len(unattended_object_bboxes)):
        for j in range(len(unattended_object_bboxes)):
            if i == j:
                continue
            if is_contained(unattended_object_bboxes[i], unattended_object_bboxes[j]):
                unattended_object_keep[i] = False
                break

    unattended_object_keep_indices = [
        idx for idx, k in zip(unattended_object_indices, unattended_object_keep) if k
    ]
    unattended_object_df = unattended_object_df.loc[
        unattended_object_keep_indices
    ].reset_index(drop=True)

    other_bboxes = other_df["bbox"].tolist()
    other_indices = other_df.index.tolist()
    other_keep = [True] * len(other_bboxes)

    for i in range(len(other_bboxes)):
        for j in range(len(other_bboxes)):
            if i == j:
                continue
            if is_contained(other_bboxes[i], other_bboxes[j]):
                other_keep[i] = False
                break

    other_keep_indices = [idx for idx, k in zip(other_indices, other_keep) if k]
    other_df = other_df.loc[other_keep_indices].reset_index(drop=True)

    df = pd.concat([unattended_object_df, other_df], ignore_index=True)

    return df


def predict_dino_model_fast(
    model,
    cam_id,
    image_resized,
    frame_key,
    image_resized_shape,
    original_image_shape,
    image_tensor: torch.Tensor,
    device: str,
    stable_mask,
) -> List[dict]:
    """Run fast prediction with the DINO model and apply filtering based on stable mask

    Args:
        model: The loaded DINO model
        cam_id: Camera ID
        frame_key: Frame key for identification
        image_resized_shape: Shape of the resized image
        original_image_shape: Original shape of the image
        image_tensor: Preprocessed image tensor
        device: Device to run inference on ('cuda' or 'cpu')
        stable_mask: Mask of stable regions to filter detections

    Returns:
        List of detection dictionaries with filtered results
    """

    try:
        with torch.autocast(device_type=device, dtype=torch.float16):
            start_time = time.time()
            boxes, scores, phrases = predict(
                model=model,
                image=image_tensor.squeeze(0),
                caption=TEXT_PROMPT,
                box_threshold=BOX_THRESHOLD,
                text_threshold=TEXT_THRESHOLD,
                device=device,
            )
            predict_time = time.time() - start_time
            logger.info(f"Raw prediction completed in {predict_time:.4f} seconds")
            print(f"Raw prediction completed in {predict_time:.4f} seconds")
            logger.info(f"Got {len(boxes)} raw detections")
    except Exception as e:
        logger.error(f"Error during model prediction: {str(e)}")
        logger.error(traceback.format_exc())
        raise

    grid_rows, grid_cols = stable_mask.shape if stable_mask is not None else (32, 32)
    H, W = image_resized_shape
    y_edges = np.linspace(0, H, grid_rows + 1, dtype=int)
    x_edges = np.linspace(0, W, grid_cols + 1, dtype=int)

    final_detections = []
    all_detections = []
    detection_master_list = []

    # image_rgb = np.array(image_resized)
    # image_bgr = image_rgb[:, :, ::-1].copy()

    grid_rows, grid_cols = stable_mask.shape if stable_mask is not None else (32, 32)

    # FOR VISUALIZATION
    # img_raw = image_bgr.copy()
    # img_mask = image_bgr.copy()
    # img_excluded = image_bgr.copy()
    # img_kept = image_bgr.copy()
    # img_final = image_bgr.copy()

    for box, score, phrase in zip(boxes, scores, phrases):
        x_center, y_center, w, h = box.tolist()
        x1 = int((x_center - w / 2) * W)
        y1 = int((y_center - h / 2) * H)
        x2 = int((x_center + w / 2) * W)
        y2 = int((y_center + h / 2) * H)

        matched_category = "unmatched"
        matched_token = phrase
        for category, tokens in CATEGORY_MAP.items():
            for token in tokens:
                if token.lower() in phrase.lower():
                    matched_category = category
                    matched_token = token
                    break
            if matched_category != "unmatched":
                break

        all_detections.append(
            {
                "bbox": torch.tensor([x1, y1, x2, y2]),
                "confidence": score.item(),
                "label": matched_category,
                "token": matched_token,
                "ground_x": 0.0,
                "ground_y": 0.0,
            }
        )

    detection_exclusion_list = []
    detection_inclusion_list = []
    for d in all_detections:
        x1, y1, x2, y2 = d["bbox"].tolist()
        label = d["token"]
        score = d["confidence"]

        # FOR VISUALIZATION
        # # Draw on raw image
        # cv2.rectangle(img_raw, (x1, y1), (x2, y2), (0, 255, 255), 2)
        # cv2.putText(
        #     img_raw,
        #     f"{label} ({score:.2f})",
        #     (x1, y1 - 5),
        #     cv2.FONT_HERSHEY_SIMPLEX,
        #     0.5,
        #     (0, 255, 255),
        #     1,
        # )

        overlap_pct = 0

        # Clamp coordinates
        x1 = np.clip(x1, 0, W - 1)
        x2 = np.clip(x2, 0, W - 1)
        y1 = np.clip(y1, 0, H - 1)
        y2 = np.clip(y2, 0, H - 1)

        if stable_mask is not None:
            cell_x1 = np.searchsorted(x_edges, x1, side="right") - 1
            cell_x2 = np.searchsorted(x_edges, x2, side="right") - 1
            cell_y1 = np.searchsorted(y_edges, y1, side="right") - 1
            cell_y2 = np.searchsorted(y_edges, y2, side="right") - 1

            cell_x1 = np.clip(cell_x1, 0, grid_cols - 1)
            cell_x2 = np.clip(cell_x2, 0, grid_cols - 1)
            cell_y1 = np.clip(cell_y1, 0, grid_rows - 1)
            cell_y2 = np.clip(cell_y2, 0, grid_rows - 1)

            box_cells = stable_mask[cell_y1 : cell_y2 + 1, cell_x1 : cell_x2 + 1]
            overlap_pct = np.mean(box_cells) if box_cells.size > 0 else 0

        if overlap_pct >= STABLE_MASK_OVERLAP_PERCENTAGE:
            print(f"Skipping {label} ({overlap_pct:.2f})")
            detection_exclusion_list.append(d)
            # FOR VISUALIZATION
            # cv2.rectangle(img_excluded, (x1, y1), (x2, y2), (0, 0, 255), 2)
            # cv2.putText(
            #     img_excluded,
            #     f"{label} ({overlap_pct:.2f})",
            #     (x1, y1 - 5),
            #     cv2.FONT_HERSHEY_SIMPLEX,
            #     0.5,
            #     (0, 0, 255),
            #     1,
            # )
        else:
            print(f"Keeping {label} ({overlap_pct:.2f})")
            detection_inclusion_list.append(d)
            # FOR VISUALIZATION
            # cv2.rectangle(img_kept, (x1, y1), (x2, y2), (0, 255, 0), 2)
            # cv2.putText(
            #     img_kept,
            #     f"{label} ({score:.2f})",
            #     (x1, y1 - 5),
            #     cv2.FONT_HERSHEY_SIMPLEX,
            #     0.5,
            #     (0, 255, 0),
            #     1,
            # )

            # cv2.rectangle(img_final, (x1, y1), (x2, y2), (0, 255, 0), 2)
            # cv2.putText(
            #     img_final,
            #     f"{label} ({score:.2f})",
            #     (x1, y1 - 5),
            #     cv2.FONT_HERSHEY_SIMPLEX,
            #     0.5,
            #     (0, 255, 0),
            #     1,
            # )

            final_detections.append(d)

    print(f"Detection exclusion list: {len(detection_exclusion_list)}")
    print(f"Detection inclusion list: {len(detection_inclusion_list)}")
    print(f"Final detections: {len(final_detections)}")

    # FOR VISUALIZATION
    # if stable_mask is not None:
    #     # Draw the stable mask
    #     cell_h = H // grid_rows
    #     cell_w = W // grid_cols
    #     for row in range(grid_rows):
    #         for col in range(grid_cols):
    #             if stable_mask[row, col]:
    #                 x1 = col * cell_w
    #                 y1 = row * cell_h
    #                 x2 = W if col == grid_cols - 1 else (col + 1) * cell_w
    #                 y2 = H if row == grid_rows - 1 else (row + 1) * cell_h
    #                 cv2.rectangle(img_mask, (x1, y1), (x2, y2), (0, 255, 255), -1)
    #                 cv2.addWeighted(image_bgr[y1:y2, x1:x2], 0.3,
    #                               img_mask[y1:y2, x1:x2], 0.7, 0, img_mask[y1:y2, x1:x2])



    # # Save the visualization images
    # os.makedirs(f"debug_images/{cam_id}", exist_ok=True)
    # timestamp = time.strftime("%Y%m%d_%H%M%S")
    # cv2.imwrite(f"debug_images/{cam_id}/{timestamp}_stable_mask.jpg", img_mask)
    # cv2.imwrite(f"debug_images/{cam_id}/{timestamp}_excluded.jpg", img_excluded)
    # cv2.imwrite(f"debug_images/{cam_id}/{timestamp}_kept.jpg", img_kept)
    # cv2.imwrite(f"debug_images/{cam_id}/{timestamp}_final.jpg", img_final)

    # if stable_mask is not None:
    #     for row in range(grid_rows):
    #         for col in range(grid_cols):
    #             if stable_mask[row, col]:
    #                 # Use the same edge arrays that were used for detection filtering
    #                 x1 = x_edges[col]
    #                 y1 = y_edges[row]
    #                 x2 = x_edges[col + 1]
    #                 y2 = y_edges[row + 1]

    #                 cv2.rectangle(img_mask, (x1, y1), (x2, y2), (0, 255, 255), -1)
    #                 cv2.addWeighted(
    #                     image_bgr[y1:y2, x1:x2],
    #                     0.3,
    #                     img_mask[y1:y2, x1:x2],
    #                     0.7,
    #                     0,
    #                     img_mask[y1:y2, x1:x2],
    #                 )

    # # Create a figure with a grid layout: 4 plots on top, 1 large plot below
    # fig = plt.figure(figsize=(24, 15))


    # FOR VISUALIZATION
    # # Add titles to each image
    # title_height = 30
    # font = cv2.FONT_HERSHEY_SIMPLEX
    # font_scale = 0.7
    # font_thickness = 2
    
    # def add_title(img, title):
    #     h, w = img.shape[:2]
    #     title_img = np.ones((title_height, w, 3), dtype=np.uint8) * 255
    #     cv2.putText(title_img, title, (10, 25), font, font_scale, (0, 0, 0), font_thickness)
    #     return np.vstack([title_img, img])

    
    # img_raw = add_title(img_raw, "Raw Detections")
    # img_mask = add_title(img_mask, "Exclusion Mask")
    # img_excluded = add_title(img_excluded, "Excluded Boxes")
    # img_kept = add_title(img_kept, "Final Detections")
    # # Create a 2x2 grid
    # top_row = np.hstack([img_raw, img_mask])
    # bottom_row = np.hstack([img_excluded, img_kept])
    # combined_img = np.vstack([top_row, bottom_row])
    # # Save the visualization images
    # output_dir = Path("output_logs") / cam_id
    # output_dir.mkdir(parents=True, exist_ok=True)
    # cv2.imwrite(str(output_dir / f"{frame_key}_combined_view.jpg"), combined_img)
    # # Define the grid layout
    # gs = fig.add_gridspec(2, 4, height_ratios=[1, 1.5])

    # # Top row with 4 plots
    # ax1 = fig.add_subplot(gs[0, 0])
    # ax1.imshow(cv2.cvtColor(img_raw, cv2.COLOR_BGR2RGB))
    # ax1.set_title(f"{cam_id} – Raw Detections")
    # ax1.axis("off")

    # ax2 = fig.add_subplot(gs[0, 1])
    # ax2.imshow(cv2.cvtColor(img_mask, cv2.COLOR_BGR2RGB))
    # ax2.set_title(f"{cam_id} – Exclusion Mask")
    # ax2.axis("off")

    # ax3 = fig.add_subplot(gs[0, 2])
    # ax3.imshow(cv2.cvtColor(img_excluded, cv2.COLOR_BGR2RGB))
    # ax3.set_title(f"{cam_id} – Excluded Boxes")
    # ax3.axis("off")

    # ax4 = fig.add_subplot(gs[0, 3])
    # ax4.imshow(cv2.cvtColor(img_kept, cv2.COLOR_BGR2RGB))
    # ax4.set_title(f"{cam_id} – Final Detections")
    # ax4.axis("off")

    # # Bottom row with one large plot spanning all columns
    # ax5 = fig.add_subplot(gs[1, :])
    # ax5.imshow(cv2.cvtColor(img_final, cv2.COLOR_BGR2RGB))
    # ax5.set_title(f"{cam_id} {frame_key} – Final Detections (full view)")
    # ax5.axis("off")

    # plt.tight_layout()

    # # Create output directory if it doesn't exist
    # import os

    # output_dir = "/app/output_logs"
    # os.makedirs(output_dir, exist_ok=True)

    # # Save the combined figure
    # output_path = os.path.join(
    #     output_dir, f"{cam_id}_{frame_key}_detection_summary.png"
    # )
    # plt.savefig(output_path, bbox_inches="tight")

    # # Close the figure to free memory
    # plt.close(fig)

    for d in final_detections:
        if d["label"] == "unmatched":
            continue

        x1, y1, x2, y2 = d["bbox"].tolist()
        h_orig, w_orig = original_image_shape
        h_resized, w_resized = image_resized_shape

        scale_w = w_orig / w_resized
        scale_h = h_orig / h_resized

        o_x1 = int(x1 * scale_w)
        o_y1 = int(y1 * scale_h)
        o_x2 = int(x2 * scale_w)
        o_y2 = int(y2 * scale_h)

        detection_master_list.append(
            {
                "event_id": str(uuid.uuid4()),
                "timestamp": frame_key.split("_")[1],
                "cam_id": cam_id,
                "category": d["label"],
                "bbox": d["bbox"].tolist(),
                "original_bbox": [o_x1, o_y1, o_x2, o_y2],
                "ground_x": d["ground_x"],
                "ground_y": d["ground_y"],
                "confidence": d["confidence"],
                "is_primary": True,
                "group_id": -1,
                "alert_type": "L1",
            }
        )
    df = pd.DataFrame(detection_master_list)
    if df.empty:
        return []
    filtered_df = remove_nested_boxes(df)
    print("Before", len(detection_master_list))
    print("After", len(filtered_df.to_dict("records")))
    filtered_results = filtered_df.to_dict("records")

    return filtered_results


def compute_stable_mask(
    control_gray, current_gray, grid_rows=64, grid_cols=64, diff_thresh=20
):
    """
    Returns binary mask of stable cells where diff < threshold.
    Same logic as visualize_static_cells, but returns mask only.
    """
    try:
        h, w = control_gray.shape
        grid_rows = GRID_ROWS
        grid_cols = GRID_COLS

        y_coords = np.linspace(0, h, grid_rows + 1, dtype=int)
        x_coords = np.linspace(0, w, grid_cols + 1, dtype=int)

        mask = np.zeros((grid_rows, grid_cols), dtype=np.uint8)

        for row in range(grid_rows):
            for col in range(grid_cols):
                y1, y2 = y_coords[row], y_coords[row + 1]
                x1, x2 = x_coords[col], x_coords[col + 1]

                patch_c = control_gray[y1:y2, x1:x2].astype(np.int16)
                patch_n = current_gray[y1:y2, x1:x2].astype(np.int16)

                diff = np.abs(patch_c - patch_n)
                mean_diff = np.mean(diff)

                if mean_diff < STABLE_MASK_DIFFERENCE_THRESHOLD:
                    mask[row, col] = 1

        stable_percentage = np.mean(mask) * 100
        logger.info(
            f"Stable mask computed: {np.sum(mask)}/{mask.size} cells stable ({stable_percentage:.2f}%)"
        )

        return mask
    except Exception as e:
        logger.error(f"Error computing stable mask: {str(e)}")
        logger.error(traceback.format_exc())
        # Return a default mask (all unstable) in case of error
        return np.zeros((grid_rows, grid_cols), dtype=np.uint8)


def load_image_fast_cv2(image_bgr, max_dim=512):
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
    image_pil = Image.fromarray(image_rgb)

    scale = max_dim / max(image_pil.width, image_pil.height)
    new_size = (int(image_pil.width * scale), int(image_pil.height * scale))
    image_resized = image_pil.resize(new_size)

    image_tensor = preprocess(image_resized)
    return image_resized, image_tensor


def should_exclude_bbox(
    bbox, stable_mask, image_size=(1280, 720), grid_rows=64, grid_cols=64, threshold=0.6
):
    """
    Returns True if the bounding box overlaps stable (unchanged) regions above a threshold.

    Args:
        bbox (list[int]): [x1, y1, x2, y2]
        stable_mask (np.ndarray): Binary 2D mask (grid_rows x grid_cols)
        image_size (tuple): (width, height)
        threshold (float): Exclude if this % of box lies in stable region

    Returns:
        bool
    """
    x1, y1, x2, y2 = map(int, bbox)
    W, H = image_size
    x1, y1 = max(0, x1), max(0, y1)
    x2, y2 = min(W, x2), min(H, y2)

    cell_w = W // grid_cols
    cell_h = H // grid_rows

    cell_x1 = x1 // cell_w
    cell_x2 = min(grid_cols - 1, x2 // cell_w)
    cell_y1 = y1 // cell_h
    cell_y2 = min(grid_rows - 1, y2 // cell_h)

    box_cells = stable_mask[cell_y1 : cell_y2 + 1, cell_x1 : cell_x2 + 1]
    if box_cells.size == 0:
        return False

    return np.mean(box_cells) >= threshold

if __name__ == "__main__":
    print("Downloading Gammy models...")
    load_dino_model()
