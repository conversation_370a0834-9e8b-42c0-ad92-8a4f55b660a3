name: dino
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - addict=2.4.0=pyhd8ed1ab_2
  - aiohttp=3.8.5=py39ha55989b_0
  - aiosignal=1.3.1=pyhd8ed1ab_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - async-timeout=4.0.3=pyhd8ed1ab_0
  - attrs=23.1.0=pyh71513ae_1
  - aws-c-auth=0.7.0=h6f3c987_2
  - aws-c-cal=0.6.0=h6ba3258_0
  - aws-c-common=0.8.23=hcfcfb64_0
  - aws-c-compression=0.2.17=h420beca_1
  - aws-c-event-stream=0.3.1=had47b81_1
  - aws-c-http=0.7.11=h72ba615_0
  - aws-c-io=0.13.28=ha35c040_0
  - aws-c-mqtt=0.8.14=h4941efa_2
  - aws-c-s3=0.3.13=he04eaa7_2
  - aws-c-sdkutils=0.1.11=h420beca_1
  - aws-checksums=0.1.16=h420beca_1
  - aws-crt-cpp=0.20.3=h247a981_4
  - aws-sdk-cpp=1.10.57=h1a0519f_17
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=2.118=mkl
  - blas-devel=3.9.0=18_win64_mkl
  - brotli=1.0.9=hcfcfb64_9
  - brotli-bin=1.0.9=hcfcfb64_9
  - brotli-python=1.0.9=py39h99910a6_9
  - bzip2=1.0.8=h8ffe710_4
  - c-ares=1.19.1=hcfcfb64_0
  - ca-certificates=2023.08.22=haa95532_0
  - certifi=2023.7.22=py39haa95532_0
  - charset-normalizer=3.2.0=pyhd8ed1ab_0
  - click=8.1.7=win_pyh7428d3b_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - comm=0.1.2=py39haa95532_0
  - contourpy=1.1.1=py39h1f6ef14_1
  - cuda-cccl=12.2.140=0
  - cuda-cudart=11.8.89=0
  - cuda-cudart-dev=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-libraries-dev=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvrtc-dev=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-profiler-api=12.2.140=0
  - cuda-runtime=11.8.0=0
  - cycler=0.11.0=pyhd8ed1ab_0
  - cython=3.0.0=py39h2bbff1b_0
  - dataclasses=0.8=pyhc8e2a94_3
  - datasets=2.14.5=pyhd8ed1ab_0
  - debugpy=1.6.7=py39hd77b12b_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - dill=0.3.7=pyhd8ed1ab_0
  - exceptiongroup=1.0.4=py39haa95532_0
  - executing=0.8.3=pyhd3eb1b0_0
  - filelock=3.12.4=pyhd8ed1ab_0
  - fonttools=4.42.1=py39ha55989b_0
  - freeglut=3.2.2=h63175ca_2
  - freetype=2.12.1=hdaf720e_2
  - frozenlist=1.4.0=py39ha55989b_1
  - fsspec=2023.6.0=pyh1a96a4e_0
  - gettext=0.21.1=h5728263_0
  - glib=2.78.0=h12be248_0
  - glib-tools=2.78.0=h12be248_0
  - gst-plugins-base=1.22.6=h001b923_1
  - gstreamer=1.22.6=hb4038d2_1
  - huggingface_hub=0.17.3=pyhd8ed1ab_0
  - icu=70.1=h0e60522_0
  - idna=3.4=pyhd8ed1ab_0
  - importlib-metadata=6.8.0=pyha770c72_0
  - importlib-resources=6.1.0=pyhd8ed1ab_0
  - importlib_metadata=6.8.0=hd8ed1ab_0
  - importlib_resources=6.1.0=pyhd8ed1ab_0
  - intel-openmp=2023.2.0=h57928b3_49503
  - ipykernel=6.25.0=py39h9909e9c_0
  - ipython=8.15.0=py39haa95532_0
  - jasper=2.0.33=hc2e4405_1
  - jedi=0.18.1=py39haa95532_1
  - jinja2=3.1.2=pyhd8ed1ab_1
  - joblib=1.3.2=pyhd8ed1ab_0
  - jpeg=9e=hcfcfb64_3
  - jupyter_client=8.1.0=py39haa95532_0
  - jupyter_core=5.3.0=py39haa95532_0
  - kiwisolver=1.4.5=py39h1f6ef14_1
  - krb5=1.20.1=heb0366b_0
  - lcms2=2.14=h90d422f_0
  - lerc=4.0.0=h63175ca_0
  - libabseil=20230125.3=cxx17_h63175ca_0
  - libarrow=12.0.1=h12e5d06_5_cpu
  - libblas=3.9.0=18_win64_mkl
  - libbrotlicommon=1.0.9=hcfcfb64_9
  - libbrotlidec=1.0.9=hcfcfb64_9
  - libbrotlienc=1.0.9=hcfcfb64_9
  - libcblas=3.9.0=18_win64_mkl
  - libclang=15.0.7=default_h77d9078_3
  - libclang13=15.0.7=default_h77d9078_3
  - libcrc32c=1.1.2=h0e60522_0
  - libcublas=*********=0
  - libcublas-dev=*********=0
  - libcufft=*********=0
  - libcufft-dev=*********=0
  - libcurand=**********=0
  - libcurand-dev=**********=0
  - libcurl=8.1.2=h68f0423_0
  - libcusolver=*********=0
  - libcusolver-dev=*********=0
  - libcusparse=11.7.5.86=0
  - libcusparse-dev=11.7.5.86=0
  - libdeflate=1.14=hcfcfb64_0
  - libevent=2.1.12=h3671451_1
  - libffi=3.4.2=h8ffe710_5
  - libglib=2.78.0=he8f3873_0
  - libgoogle-cloud=2.12.0=h00b2bdc_1
  - libgrpc=1.54.3=ha177ca7_0
  - libhwloc=2.9.3=default_haede6df_1009
  - libiconv=1.17=h8ffe710_0
  - liblapack=3.9.0=18_win64_mkl
  - liblapacke=3.9.0=18_win64_mkl
  - libnpp=11.8.0.86=0
  - libnpp-dev=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libnvjpeg-dev=11.9.0.86=0
  - libogg=1.3.4=h8ffe710_1
  - libopencv=4.5.3=py39h488c12c_8
  - libpng=1.6.39=h19919ed_0
  - libprotobuf=3.21.12=h12be248_2
  - libsodium=1.0.18=h62dcd97_0
  - libsqlite=3.43.0=hcfcfb64_0
  - libssh2=1.11.0=h7dfc565_0
  - libthrift=0.18.1=h06f6336_2
  - libtiff=4.4.0=hc4f729c_5
  - libutf8proc=2.8.0=h82a8f57_0
  - libuv=1.44.2=hcfcfb64_1
  - libvorbis=1.3.7=h0e60522_0
  - libwebp-base=1.3.2=hcfcfb64_0
  - libxcb=1.13=hcd874cb_1004
  - libxml2=2.11.5=hc3477c8_1
  - libzlib=1.2.13=hcfcfb64_5
  - lz4-c=1.9.4=hcfcfb64_0
  - m2w64-gcc-libgfortran=5.3.0=6
  - m2w64-gcc-libs=5.3.0=7
  - m2w64-gcc-libs-core=5.3.0=7
  - m2w64-gmp=6.1.0=2
  - m2w64-libwinpthread-git=5.0.0.4634.697f757=2
  - markupsafe=2.1.3=py39ha55989b_1
  - matplotlib-base=3.8.0=py39hf19769e_1
  - matplotlib-inline=0.1.6=py39haa95532_0
  - mkl=2022.1.0=h6a75c08_874
  - mkl-devel=2022.1.0=h57928b3_875
  - mkl-include=2022.1.0=h6a75c08_874
  - mpmath=1.3.0=pyhd8ed1ab_0
  - msys2-conda-epoch=20160418=1
  - multidict=6.0.4=py39ha55989b_0
  - multiprocess=0.70.15=py39ha55989b_1
  - munkres=1.1.4=pyh9f0ad1d_0
  - nest-asyncio=1.5.6=py39haa95532_0
  - networkx=3.1=pyhd8ed1ab_0
  - numpy=1.26.0=py39hddb5d58_0
  - opencv=4.5.3=py39hcbf5309_8
  - openjpeg=2.5.0=hc9384bd_1
  - openssl=3.1.3=hcfcfb64_0
  - orc=1.9.0=hada7b9e_1
  - packaging=23.1=pyhd8ed1ab_0
  - pandas=2.1.1=py39h32e6231_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pcre2=10.40=h17e33f8_0
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.2.0=py39h595c93f_3
  - pip=23.2.1=pyhd8ed1ab_0
  - platformdirs=3.10.0=pyhd8ed1ab_0
  - prompt-toolkit=3.0.36=py39haa95532_0
  - psutil=5.9.0=py39h2bbff1b_0
  - pthread-stubs=0.4=hcd874cb_1001
  - pthreads-win32=2.9.1=hfa6e2cd_3
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - py-opencv=4.5.3=py39h00e5391_8
  - pyarrow=12.0.1=py39hca4e8af_5_cpu
  - pycocotools=2.0.6=py39hc266a54_1
  - pygments=2.15.1=py39haa95532_1
  - pyparsing=3.1.1=pyhd8ed1ab_0
  - pysocks=1.7.1=pyh0701188_6
  - python=3.9.18=h4de0772_0_cpython
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python-tzdata=2023.3=pyhd8ed1ab_0
  - python-xxhash=3.3.0=py39ha55989b_1
  - python_abi=3.9=4_cp39
  - pytorch=2.0.1=py3.9_cuda11.8_cudnn8_0
  - pytorch-cuda=11.8=h24eeafa_5
  - pytorch-mutex=1.0=cuda
  - pytz=2023.3.post1=pyhd8ed1ab_0
  - pywin32=305=py39h2bbff1b_0
  - pyyaml=6.0.1=py39ha55989b_1
  - pyzmq=25.1.0=py39hd77b12b_0
  - qt-main=5.15.8=h720456b_6
  - re2=2023.03.02=hd4eee63_0
  - regex=2023.8.8=py39ha55989b_1
  - requests=2.31.0=pyhd8ed1ab_0
  - sacremoses=0.0.53=pyhd8ed1ab_0
  - safetensors=0.3.3=py39hf21820d_1
  - setuptools=68.2.2=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - snappy=1.1.10=hfb803bf_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - sympy=1.12=pyh04b8f61_3
  - tbb=2021.10.0=h91493d7_1
  - timm=0.9.7=pyhd8ed1ab_0
  - tk=8.6.13=hcfcfb64_0
  - tokenizers=0.13.3=py39hca44cb7_0
  - tomli=2.0.1=pyhd8ed1ab_0
  - tornado=6.3.2=py39h2bbff1b_0
  - tqdm=4.66.1=pyhd8ed1ab_0
  - traitlets=5.7.1=py39haa95532_0
  - transformers=4.33.2=pyhd8ed1ab_0
  - typing-extensions=4.8.0=hd8ed1ab_0
  - typing_extensions=4.8.0=pyha770c72_0
  - tzdata=2023c=h71feb2d_0
  - ucrt=10.0.22621.0=h57928b3_0
  - unicodedata2=15.0.0=py39ha55989b_1
  - urllib3=2.0.5=pyhd8ed1ab_0
  - vc=14.3=h64f974e_17
  - vc14_runtime=14.36.32532=hdcecf7f_17
  - vs2015_runtime=14.36.32532=h05e6639_17
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - wheel=0.41.2=pyhd8ed1ab_0
  - win_inet_pton=1.1.0=pyhd8ed1ab_6
  - xorg-libxau=1.0.11=hcd874cb_0
  - xorg-libxdmcp=1.1.3=hcd874cb_0
  - xxhash=0.8.2=hcfcfb64_0
  - xz=5.2.6=h8d14728_0
  - yaml=0.2.5=h8ffe710_2
  - yapf=0.40.1=pyhd8ed1ab_0
  - yarl=1.9.2=py39ha55989b_0
  - zeromq=4.3.4=hd77b12b_0
  - zipp=3.17.0=pyhd8ed1ab_0
  - zlib=1.2.13=hcfcfb64_5
  - zstd=1.5.5=h12be248_0
  - pip:
      - opencv-python==********
      - supervision==0.6.0
      - torchaudio==2.0.2
      - torchvision==0.15.2
prefix: C:\Users\<USER>\miniconda3\envs\dino
