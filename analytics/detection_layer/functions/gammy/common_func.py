# try:
#     from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
# except:
#     try:
#         from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
#     except:
#         from groundingdino.util.inference import load_model, load_image, predict, annotate

from groundingdino.util.inference import load_model, load_image, predict, annotate
import cv2
import time
import yaml
import os
import torch
import numpy as np
import cv2
from PIL import Image
import torchvision.transforms as T
from typing import Tuple, List
from torchvision.ops import box_convert   # torchvision ≥0.13
from collections import defaultdict
import PIL.Image
import uuid
import torch.nn.functional as F
from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD

MODEL_CFG     = os.path.join(os.path.dirname(__file__), "groundingdino/config/GroundingDINO_SwinT_OGC.py")
# MODEL_CFG = './groundingdino/config/GroundingDINO_SwinT_OGC.py'
MODEL_WEIGHTS = os.path.join(os.path.dirname(__file__), "weights/groundingdino_swint_ogc.pth")
# MODEL_WEIGHTS = './weights/groundingdino_swint_ogc.pth'
# DETECTION_PROMPT = os.path.join(os.path.dirname(__file__), "detections-prompt.yml")

def load_dino_model(device):
    return load_model(MODEL_CFG, MODEL_WEIGHTS, device)


# def load_detection_prompt():
#     with open(DETECTION_PROMPT, "r") as f:
#         return yaml.safe_load(f)

# def get_cardinal_prompt(cardinal_requirements):
#     detection_prompt = load_detection_prompt()
#     return detection_prompt[cardinal_requirements]


# Loading in the config
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)

MAX_FRAMES = decrypted_config["GAMMY_CONFIGURATION"]["MAX_FRAMES"]
BOX_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["BOX_THRESHOLD"]
TEXT_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["TEXT_THRESHOLD"]
CATEGORY_MAP = decrypted_config["GAMMY_CONFIGURATION"]["CATEGORY_MAP"]
THRESHOLDS = decrypted_config["GAMMY_CONFIGURATION"]["THRESHOLDS"]

ALL_TOKENS = [token for group in CATEGORY_MAP.values() for token in group]
TEXT_PROMPT = " . ".join(ALL_TOKENS)

TOKEN_TO_CATEGORY = {
    token: category
    for category, tokens in CATEGORY_MAP.items()
    for token in tokens
}

def preprocess(image_pil):
    """
    Preprocess an image for model input.
    
    Args:
        image_pil (PIL.Image): PIL image to preprocess
        
    Returns:
        torch.Tensor: Preprocessed image tensor
    """
    transform = T.Compose([
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225])
    ])
    return transform(image_pil)


def load_image_from_bytes(image_bgr: bytes, device: str = "cuda") -> Tuple[np.ndarray, torch.Tensor]:
    # Convert bytes to numpy array
    # nparr = np.frombuffer(image_bytes, np.uint8)
    # image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
    
    # Convert to tensor more efficiently
    image_tensor = (
        torch.from_numpy(image_rgb)          # shape: H×W×C (uint8)
        .float()                             # to float32
        .permute(2, 0, 1)                    # to C×H×W
        .div(255.0)                          # normalize to [0,1]
        .to(device)                          # send to GPU/CPU
    )
    
    # Apply normalization for the model
    normalize = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    image_tensor = normalize(image_tensor)
    
    # Resize if needed (GroundingDINO expects 800px max dimension and minimum 400px)
    h, w = image_tensor.shape[1:]
    min_size = 400
    max_size = 800
    
    # Calculate scale to fit within max_size while maintaining aspect ratio
    scale = min(max_size / max(h, w), 1.0)
    
    # If image is too small, scale up to minimum size
    if min(h, w) * scale < min_size:
        scale = min_size / min(h, w)
    
    # Apply scaling if needed
    if scale != 1.0:
        h = int(h * scale)
        w = int(w * scale)
        image_tensor = F.interpolate(image_tensor.unsqueeze(0), size=(h, w), mode='bilinear', align_corners=False)[0]
    
    return image_rgb, image_tensor


def pixel_boxes(boxes: torch.Tensor, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    h, w = image.shape[:2]
    scale = torch.tensor([w, h, w, h], dtype=boxes.dtype, device=boxes.device)
    return (box_convert(boxes * scale, "cxcywh", "xyxy")
            .round().int().cpu().tolist())


def calculate_iou(boxA, boxB):
    """
    Calculates the Intersection over Union (IoU) between two bounding boxes.
    Boxes are in [x1, y1, x2, y2] format.
    """
    # Determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # Compute the area of intersection
    intersection_area = max(0, xB - xA) * max(0, yB - yA)

    # Compute the area of both bounding boxes
    boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])

    # Compute the IoU
    iou = intersection_area / float(boxA_area + boxB_area - intersection_area)
    return iou


def predict_dino_model(model, original_rgb_np: np.ndarray, image_tensor: torch.Tensor, cfg: dict, device: str, coordinates: dict) -> List[dict]:
    """
    1) Run DINO predict(...) on the transformed tensor
    2) Get normalized boxes (cx,cy,w,h) + confidences + phrase labels
    3) Call pixel_boxes(..., original_rgb_np) to convert to correct pixel coords
    4) Build and return a list of dicts containing {state, bounding_box, reason}
    """
    # (A) Run inference on the tensor
    boxes_norm, logits, phrases = predict(
        model=model,
        image=image_tensor,
        caption=cfg["prompt"],
        box_threshold=cfg["box_threshold"],
        text_threshold=cfg["text_threshold"],
        device=device
    )

    # (B) Convert normalized → absolute pixel coords using the ORIGINAL image's dimensions
    pixel_box_list = pixel_boxes(boxes_norm, original_rgb_np)

    # Iterate over each detected object
    final_tagged_detections = []
    for p_box, phrase, logit in zip(pixel_box_list, phrases, logits):
        
        highest_iou = -1
        tagged_box_name = None
        
        # For the current detection, find the best matching coordinate_box
        for box_name, mapping_info in coordinates.items():
            t_box = mapping_info["temporary_coordinates"]
            iou = calculate_iou(p_box, t_box)
            
            if iou > highest_iou:
                highest_iou = iou
                tagged_box_name = box_name

        # Now that the best match is found, calculate the delta
        delta = {}
        if tagged_box_name:
            matched_t_box = coordinates[tagged_box_name]["temporary_coordinates"]
            delta = {
                "delta_x1": p_box[0] - matched_t_box[0],
                "delta_y1": p_box[1] - matched_t_box[1],
                "delta_x2": p_box[2] - matched_t_box[2],
                "delta_y2": p_box[3] - matched_t_box[3]
            }
        
        # Append the result for the current detection to our final list
        final_tagged_detections.append({
            "detection_box": p_box,
            "phrase": phrase,
            "confidence": round(logit.item(), 4),
            "tagged_to_box": tagged_box_name,
            "iou_score": round(highest_iou, 4),
            "relative_delta": delta
        })

    return final_tagged_detections


def group_predictions(boxes, phrases, scores):
    """
    Group model predictions by category.
    
    Args:
        boxes (torch.Tensor): Bounding boxes
        phrases (list): Text phrases
        scores (torch.Tensor): Confidence scores
        
    Returns:
        dict: Grouped predictions by category
    """
    grouped = defaultdict(list)
    for box, phrase, score in zip(boxes, phrases, scores):
        phrase_tokens = phrase.lower().split()
        matched = False
        for token in TOKEN_TO_CATEGORY:
            token_words = token.lower().split()
            if all(word in phrase_tokens for word in token_words):
                category = TOKEN_TO_CATEGORY[token]
                threshold = THRESHOLDS.get(category, TEXT_THRESHOLD)
                if score >= threshold:
                    grouped[category].append({
                        "token": token,
                        "phrase": phrase,
                        "score": score,
                        "box": box
                    })
                matched = True
                break
        if not matched:
            grouped["unmatched"].append({
                "token": "unknown",
                "phrase": phrase,
                "score": score,
                "box": box
            })
    return grouped


def annotate_smaller(boxes: torch.Tensor, image_shape: list) -> List[Tuple[int, int, int, int]]:
    h, w = image_shape

    boxes_scaled = boxes * torch.tensor([w, h, w, h], dtype=boxes.dtype, device=boxes.device)
    xyxy = boxes_scaled.clone()

    # Convert from cxcywh to xyxy
    xyxy[:, 0] -= xyxy[:, 2] / 2  # x center - width/2
    xyxy[:, 1] -= xyxy[:, 3] / 2  # y center - height/2
    xyxy[:, 2] += xyxy[:, 0]      # x center + width/2
    xyxy[:, 3] += xyxy[:, 1]      # y center + height/2

    xyxy_int = xyxy.round().int().tolist()

    return xyxy_int


def predict_dino_model_fast(model, cam_id, frame_key, image_resized_shape, original_image_shape, image_tensor: torch.Tensor, device: str) -> List[dict]:
    # (A) Run inference on the tensor
    boxes, scores, phrases = predict(
        model=model,
        image=image_tensor,
        caption=TEXT_PROMPT,
        box_threshold=BOX_THRESHOLD,
        text_threshold=TEXT_THRESHOLD,
        device=device
    )

    grouped = group_predictions(boxes, phrases, scores)

    # Add to master detection list
    detection_master_list = []
    for category, detections in grouped.items():
        if category == "unmatched":
            continue
            
        for d in detections:
            # Convert box format
            box = d["box"]
            h, w = image_resized_shape
            box_pixels = box * torch.tensor([w, h, w, h])
            x1 = (box_pixels[0] - box_pixels[2] / 2).item()
            y1 = (box_pixels[1] - box_pixels[3] / 2).item()
            x2 = (box_pixels[0] + box_pixels[2] / 2).item()
            y2 = (box_pixels[1] + box_pixels[3] / 2).item()

            # Calculate the original coordinates
            # Get original dimensions
            h_orig, w_orig = original_image_shape
            h_resized, w_resized = image_resized_shape
            
            # Calculate scale factors to go from resized back to original
            scale_w = w_orig / w_resized
            scale_h = h_orig / h_resized
            
            # Scale the coordinates back to original dimensions
            o_x1 = x1 * scale_w
            o_y1 = y1 * scale_h  
            o_x2 = x2 * scale_w
            o_y2 = y2 * scale_h
            
            detection_master_list.append({
                "event_id": str(uuid.uuid4()),
                "timestamp": frame_key.split("_")[1],
                "cam_id": cam_id,
                "category": category,
                "bbox": [x1, y1, x2, y2],
                "original_bbox": [o_x1, o_y1, o_x2, o_y2],
                "ground_x": 0.0,
                "ground_y": 0.0,
                "confidence": d["score"].item(),
                "is_primary": True
            })

    # flattened = []
    # for category, detections in grouped_results.items():
    #     for det in detections:
    #         flattened.append((det["box"], det["score"], f"[{category}] {det['token']} {det['score']:.2f}"))

    # if isinstance(image_source, PIL.Image.Image):
    #     image_source = np.array(image_source)

    # if flattened:
    #     final_boxes_tensor = torch.stack([
    #         box.clone().detach() if isinstance(box, torch.Tensor) else torch.tensor(box)
    #         for box, _, _ in flattened
    #     ])
    #     final_scores_tensor = torch.tensor([score for _, score, _ in flattened])
    #     final_phrases = [phrase for _, _, phrase in flattened]
    # else:
    #     final_boxes_tensor = torch.empty((0, 4))
    #     final_scores_tensor = torch.empty((0,))
    #     final_phrases = []

    # pixel_box_list = annotate_smaller(final_boxes_tensor, image_shape)

    # # Iterate over each detected object
    # final_tagged_detections = []
    # for p_box, phrase, logit in zip(pixel_box_list, final_phrases, final_scores_tensor):
    #     final_tagged_detections.append({
    #         "detection_box": p_box,
    #         "phrase": phrase,
    #         "confidence": round(logit.item(), 4),
    #     })

    return detection_master_list



if __name__ == "__main__":
    print("Downloading GAmmy models...")
    load_dino_model()