#!/usr/bin/env python3
"""
Configuration Encryption Utilities

This module provides functions to encrypt and decrypt configuration dictionaries
and save/load them to/from text files.
"""
from dotenv import load_dotenv
load_dotenv()

import json
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
from typing import Dict, Any

# Predefined salt - you can change this to any 16-byte value
# This ensures consistent encryption/decryption across different runs
PREDEFINED_SALT = bytes.fromhex('6d27c6e7bebf7ba5414e9e84be1268e9')  # 16 bytes
FILENAME = f"./configuration/encrypted_config.txt"
PASSWORD = os.environ.get("CONFIG_PASSWORD")

class ConfigEncryptor:
    """Handles encryption and decryption of configuration dictionaries."""
    
    def __init__(self, password: str, salt: bytes = None):
        """
        Initialize the encryptor with a password and optional salt.
        
        Args:
            password: The password to use for encryption/decryption
            salt: Optional salt bytes. If None, uses the predefined salt.
        """
        self.password = password.encode()
        self.salt = salt or PREDEFINED_SALT
        
        # Generate key from password and salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        self.fernet = Fernet(key)
    
    def encrypt_config(self, config: Dict[str, Any]) -> str:
        """
        Encrypt the configuration dictionary.
        
        Args:
            config: The dictionary to encrypt
            
        Returns:
            Base64 encoded encrypted string
        """
        # Convert dictionary to JSON string
        json_str = json.dumps(config, indent=2)
        
        # Encrypt the JSON string
        encrypted_data = self.fernet.encrypt(json_str.encode())
        
        # Combine salt and encrypted data
        combined = self.salt + encrypted_data
        
        # Debug: Print salt info
        print(f"DEBUG: Using salt: {self.salt.hex()}")
        print(f"DEBUG: Salt length: {len(self.salt)}")
        print(f"DEBUG: Encrypted data length: {len(encrypted_data)}")
        print(f"DEBUG: Combined length: {len(combined)}")
        
        # Return base64 encoded string
        return base64.b64encode(combined).decode()
    
    def decrypt_config(self, encrypted_str: str) -> Dict[str, Any]:
        """
        Decrypt the configuration string back to dictionary.
        
        Args:
            encrypted_str: Base64 encoded encrypted string
            
        Returns:
            Decrypted dictionary
        """
        # Decode base64 string
        combined = base64.b64decode(encrypted_str.encode())
        
        # Extract salt and encrypted data
        salt = combined[:16]
        encrypted_data = combined[16:]
        
        # Debug: Print salt info
        print(f"DEBUG: Extracted salt: {salt.hex()}")
        print(f"DEBUG: Salt length: {len(salt)}")
        print(f"DEBUG: Encrypted data length: {len(encrypted_data)}")
        print(f"DEBUG: Combined length: {len(combined)}")
        
        # Recreate the key with the extracted salt (not the predefined one)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        fernet = Fernet(key)
        
        # Decrypt the data
        decrypted_data = fernet.decrypt(encrypted_data)
        
        # Parse JSON back to dictionary
        return json.loads(decrypted_data.decode())

def encrypt_and_save_config(config: Dict[str, Any], filename: str, password: str) -> None:
    """
    Encrypt a configuration dictionary and save it to a text file.
    
    Args:
        config: The dictionary to encrypt
        filename: The output filename
        password: The password for encryption
    """
    encryptor = ConfigEncryptor(password)
    encrypted_data = encryptor.encrypt_config(config)
    
    with open(filename, 'w') as f:
        f.write(encrypted_data)
    
    print(f"✅ Encrypted config saved to: {filename}")

def load_and_decrypt_config(filename: str = FILENAME, password: str = PASSWORD) -> Dict[str, Any]:
    """
    Load and decrypt a configuration from a text file.
    
    Args:
        filename: The input filename
        password: The password for decryption
        
    Returns:
        Decrypted dictionary
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If decryption fails (wrong password or corrupted file)
    """
    if not os.path.exists(filename):
        raise FileNotFoundError(f"Config file not found: {filename}")
    
    with open(filename, 'r') as f:
        encrypted_data = f.read().strip()
    
    try:
        encryptor = ConfigEncryptor(password)
        return encryptor.decrypt_config(encrypted_data)
    except Exception as e:
        raise ValueError(f"Failed to decrypt config: {str(e)}")

def verify_config_integrity(original_config: Dict[str, Any], filename: str, password: str) -> bool:
    """
    Verify that the decrypted config matches the original.
    
    Args:
        original_config: The original configuration dictionary
        filename: The encrypted config file
        password: The password for decryption
        
    Returns:
        True if the configs match, False otherwise
    """
    try:
        decrypted_config = load_and_decrypt_config(filename, password)
        return original_config == decrypted_config
    except Exception:
        return False

# Example usage functions
def create_sample_config() -> Dict[str, Any]:
    """Create a sample configuration for testing."""
    return {
        "CATEGORY_MAP": {
            "human": ["person", "man", "woman"],
            "weapon": ["knife", "gun", "sword"],
            "container": ["backpack", "suitcase", "box"],
            "suspicious_person": ["masked person", "person with hood"],
            "unattended_suspicious_object": ["unattended bag", "abandoned object"]
        },
        "SETTINGS": {
            "confidence_threshold": 0.8,
            "max_detections": 10,
            "enable_logging": True
        }
    }

def demo_encryption_workflow():
    """Demonstrate the complete encryption/decryption workflow."""
    
    print("=== Configuration Encryption Demo ===\n")
    print(f"Using predefined salt: {PREDEFINED_SALT.hex()}")
    print()
    
    # # Create sample config
    # config = create_sample_config()
    # print("1. Original config:")
    # print(json.dumps(config, indent=2))
    
    # # Encrypt and save
    # print(f"\n2. Encrypting and saving to {FILENAME}...")
    # encrypt_and_save_config(config, FILENAME, PASSWORD)
    
    # Load and decrypt
    print(f"\n3. Loading and decrypting from {FILENAME}...")
    print(f"Password: {PASSWORD}")
    decrypted_config = load_and_decrypt_config(FILENAME, PASSWORD)
    
    # Verify integrity
    # print("\n4. Verifying config integrity...")
    # if verify_config_integrity(config, FILENAME, PASSWORD):
    #     print("✅ Config integrity verified!")
    # else:
    #     print("❌ Config integrity check failed!")
    
    # Show usage example
    print("\n5. Using the decrypted config:")
    print(decrypted_config)
    # category_map = decrypted_config['CATEGORY_MAP']
    # print(f"Human categories: {category_map['human']}")
    # print(f"Weapon categories: {category_map['weapon']}")
    # print(f"Confidence threshold: {decrypted_config['SETTINGS']['confidence_threshold']}")

if __name__ == "__main__":
    demo_encryption_workflow() 