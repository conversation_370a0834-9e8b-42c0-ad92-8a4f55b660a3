cameras_mapping:
  - camera_name: "Cam01"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      - base_layer_name: "Unattended Object Detection"
        camera_layer_config_name: "Unattended Object Detection Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration:
          frame_history: true
      - base_layer_name: "Pose Detection"
        camera_layer_config_name: "Pose Detection Layer"
        enabled: true
        color: '#264b96'
        regions: []
        configuration: {}
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      
      # - base_layer_name: "Trip Wire"
      #   camera_layer_config_name: "Trip Wire Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: ["Red - Ankle Region Warning", "Red - Hip Line Warning", "Red - Head Region Critical", "Red - Hands and Hips Region Critical"]
      #   configuration: {}
      # - base_layer_name: "Evaluation"
      #   camera_layer_config_name: "Evaluation Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      #     extract_from_dependency: 
      #       - "Trip Wire"

  - camera_name: "Cam02"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      # - base_layer_name: "Unattended Object Detection"
      #   camera_layer_config_name: "Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     extract_from_dependency: 
      #       - "Object Tracking"
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}

  - camera_name: "Cam03"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      - base_layer_name: "Unattended Object Detection"
        camera_layer_config_name: "Unattended Object Detection Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration:
          frame_history: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}

  - camera_name: "Cam04"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      # - base_layer_name: "Unattended Object Detection"
      #   camera_layer_config_name: "Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     extract_from_dependency: 
      #       - "Object Tracking"
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}

  - camera_name: "Cam05"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      # - base_layer_name: "Unattended Object Detection"
      #   camera_layer_config_name: "Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     extract_from_dependency: 
      #       - "Object Tracking"
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}

  - camera_name: "Cam08"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      - base_layer_name: "Pose Detection"
        camera_layer_config_name: "Pose Detection Layer"
        enabled: true
        color: '#264b96'
        regions: []
        configuration: {}
      - base_layer_name: "Unattended Object Detection"
        camera_layer_config_name: "Unattended Object Detection Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration:
          frame_history: true
      # TODO: Uncomment this
      # - base_layer_name: "Pose Detection"
      #   camera_layer_config_name: "Pose Detection Layer"
      #   enabled: true
      #   color: '#264b96'
      #   regions: []
      #   configuration:
      #     min_confidence: 0.7
      #     pose_landmarks: ["nose", "left_eye", "right_eye", "left_ear", "right_ear"]
      #     track_movement: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     extract_from_dependency: 
      #       - "Object Tracking"
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Trip Wire"
      #   camera_layer_config_name: "Trip Wire Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: ["Orange - Ankle Region Warning", "Orange - Hip Line Warning", "Orange - Head Region Critical", "Orange - Hands and Hips Region Critical"]
      #   configuration: {}
      # - base_layer_name: "Evaluation"
      #   camera_layer_config_name: "Evaluation Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      #     extract_from_dependency: 
      #       - "Trip Wire"
      
  - camera_name: "Cam09"
    layers:
      - base_layer_name: "Predict with Gammy"
        camera_layer_config_name: "Predict with Gammy Layer"
        enabled: true
        color: '#006f3c'
        regions: []
        configuration: {}
      # - base_layer_name: "Unattended Object Detection"
      #   camera_layer_config_name: "Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     frame_history: true
      # - base_layer_name: "Object Tracking"
      #   camera_layer_config_name: "Object Tracking Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration:
      #     extract_from_dependency: 
      #       - "Object Tracking"
      # - base_layer_name: "Evaluation - Weapon Detection"
      #   camera_layer_config_name: "Evaluation - Weapon Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Suspicious Person Detection"
      #   camera_layer_config_name: "Evaluation - Suspicious Person Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Oversized Object Detection"
      #   camera_layer_config_name: "Evaluation - Oversized Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
      # - base_layer_name: "Evaluation - Unattended Object Detection"
      #   camera_layer_config_name: "Evaluation - Unattended Object Detection Layer"
      #   enabled: true
      #   color: '#006f3c'
      #   regions: []
      #   configuration: {}
