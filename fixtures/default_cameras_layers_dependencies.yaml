# Layer dependencies section
# This defines the dependencies between layers for each camera

layer_dependencies:
  # Unattended Object Detection is dependent on Predict with Gammy
  - source: "Unattended Object Detection"
    depends_on: 
      - "Predict with Gammy"
    conditions: 
      condition: "result != []"

  # - source: "People Tracking"
  #   depends_on:
  #     - "Box Classifier"
  #   conditions: 
  #     condition: "result != []"

  # - source: "Trip Wire"
  #   depends_on:
  #     - "People Tracking"
  #   conditions: 
  #     condition: ""

  # - source: "Evaluation"
  #   depends_on:
  #     - "Trip Wire"
  #   conditions: 
  #     condition: "result != []"

  # # Object Detection
  # - source: "Object Tracking"
  #   depends_on:
  #     - "Predict with Gammy"
  #   conditions:
  #     condition: "result != []"

  # - source: "Evaluation - Weapon Detection"
  #   depends_on:
  #     - "Object Tracking"
  #   conditions:
  #     condition: "result != []"

  # - source: "Evaluation - Suspicious Person Detection"
  #   depends_on:
  #     - "Object Tracking"
  #   conditions:
  #     condition: "result != []"

  # - source: "Evaluation - Oversized Object Detection"
  #   depends_on:
  #     - "Object Tracking"
  #   conditions:
  #     condition: "result != []"

  # - source: "Evaluation - Unattended Object Detection"
  #   depends_on:
  #     - "Object Tracking"
  #   conditions:
  #     condition: "result != []"
      
#   # Movement Analysis Layer depends on Distance Calculation
#   - source: "Movement Analysis Layer"
#     depends_on: 
#       - "Distance Calculation Layer"
#     conditions:
#       min_frames_tracked: 5
#       velocity_threshold: 1.2
#       direction_change_threshold: 30 # degrees