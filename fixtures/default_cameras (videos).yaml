---
# Default camera configurations for SquirrelSentry
# These cameras will be automatically populated at startup if they don't exist

cameras:
  - name: "Cam01"
    location: "Unpaid Area"
    description: "VA Camera - Cam01"
    # rtsp_url: "rtsp://rtsp:8554/cam_one"
    rtsp_url: "video_one.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam02"
    location: "Unpaid Area"
    description: "VA Camera - Cam02"
    # rtsp_url: "rtsp://rtsp:8554/cam_two"
    rtsp_url: "video_two.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true
    
  - name: "Cam03"
    location: "Unpaid Area Overall View"
    description: "VA Camera - Cam03"
    # rtsp_url: "rtsp://rtsp:8554/cam_three"
    rtsp_url: "video_three.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam04"
    location: "Fisheye View"
    description: "VA Camera - Cam04"
    # rtsp_url: "rtsp://rtsp:8554/cam_four"
    rtsp_url: "video_four.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam05"
    location: "Paid Area"
    description: "VA Camera - Cam05"
    # rtsp_url: "rtsp://rtsp:8554/cam_five"
    rtsp_url: "video_five.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam06"
    location: "Paid Area/Staircase A/B"
    description: "Standard Camera - Cam06"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/602"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  - name: "Cam07"
    location: "Paid Escalator Area"
    description: "Standard Camera - Cam07"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/701"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  - name: "Cam08"
    location: "Paid Area"
    description: "VA Camera - Cam08"
    # rtsp_url: "rtsp://rtsp:8554/cam_eight"
    rtsp_url: "video_eight.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam09"
    location: "Paid Area Overall View"
    description: "VA Camera - Cam09"
    # rtsp_url: "rtsp://rtsp:8554/cam_nine"
    rtsp_url: "video_nine.mp4"
    stream_fps: 1
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: true

  - name: "Cam10"
    location: "Paid Area Facing Zone 1 & Zone 2"
    description: "Standard Camera - Cam10"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/1001"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  - name: "Cam11"
    location: "Paid Area Facing Zone 3 & Zone 4"
    description: "Standard Camera - Cam11"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/1101"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  - name: "Cam12"
    location: "Paid Area Facing Staircase B"
    description: "Standard Camera - Cam12"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/1201"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  - name: "Cam13"
    location: "Paid Area Facing Zone 3 & Zone 4"
    description: "Standard Camera - Cam13"
    rtsp_url: "rtsp://USERNAME:PASSWORD@SERVER:PORT/Streaming/Channels/1301"
    stream_fps: 10
    encoding_format: "H.264"
    dynamic_frame_rate: true
    required_analytics: false

  # - name: "Server Room Monitoring Camera"
  #   location: "Server Room"
  #   description: "Server Room Monitoring Camera"
  #   rtsp_url: "rtsp://rtsp:8554/cam_fourteen"
  #   stream_fps: 10
  #   encoding_format: "H.264"
  #   dynamic_frame_rate: true
  #   required_analytics: false