# Environment variables
.env
.env.*
*.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Redis
*redis.conf

# Python
__pycache__/
backend/*.py[cod]
backend/*$py.class
backend/*.so
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/*.egg-info/
backend/.installed.cfg
backend/*.egg

# Next.js
frontend/.next/
frontend/out/
frontend/build/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Node
node_modules/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Local development
*.log
.coverage
htmlcov/
.pytest_cache/
venv/

# Misc
.DS_Store
*.pem

.python-version
license.json