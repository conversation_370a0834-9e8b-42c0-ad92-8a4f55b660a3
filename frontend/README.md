# NextJS Frontend Documentation

## Project Overview

This is a NextJS frontend that integrates with a Django backend for authentication and user management functionality. The backend handles session-based authentication with cookie support and Google OAuth integration, while the frontend provides the user interface and manages the authentication state.

## Tech Stack

- TypeScript
- NextJS
- Tailwind CSS
- Shadcn UI

## Prerequisites

- Node.js

## Setup Instructions

### 1. Install Dependencies

```bash
npm i
```

### 2. Environment Variables

Create a `.env.local` file in the frontend directory with the following variables:

```bash
NEXT_PUBLIC_API_DOMAIN=http://localhost:4000
SESSION_COOKIE_NAME=session_id
```

Make sure the `SESSION_COOKIE_NAME` matches the one in the backend.

### 3. Running the Server

#### Local Development

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

#### Production

```bash
npm run build
npm run start
```

### Project Structure

```bash
frontend/
├── src/
│   ├── app/         # Pages
│   ├── components/  # All related components
│   ├── icons/       # Custom SVG icons
│   ├── lib/         # Utility functions
│   ├── services/    # Functions calling backend endpoints
│   └── types/       # TypeScript type definitions
```

## Security Considerations

1. **Environment Variables**
   - Never commit `.env.local` file

## Deployment Checklist

1. Update environment variables
2. Make sure `npm run build` is successful without any errors
