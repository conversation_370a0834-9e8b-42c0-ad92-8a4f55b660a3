{"endOfLine": "lf", "semi": true, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "importOrder": ["^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "", "^types$", "^@/env(.*)$", "^@/types/(.*)$", "^@/config/(.*)$", "^@/server/(.*)$", "^@/db$", "^@/db/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/styles$", "^@/styles/(.*)$", "^@/app/(.*)$", "^@/(.*)$", "", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderTypeScriptVersion": "5.0.0", "tailwindFunctions": ["cn"], "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"]}