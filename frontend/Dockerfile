FROM node:18-alpine AS base

# Step 1. Rebuild the source code only when needed
FROM base AS builder

# Declare build argument for NEXT_PUBLIC_API_DOMAIN
ARG NEXT_PUBLIC_API_DOMAIN
ARG NEXT_PUBLIC_CREDENTIALS_SALT_KEY

# Optionally, set an environment variable for later stages
# This is required to send the environment variables to the build stage for client side code to access.
RUN echo "NEXT_PUBLIC_API_DOMAIN=${NEXT_PUBLIC_API_DOMAIN}"
RUN echo "NODE_ENV=${NODE_ENV}"
ENV NEXT_PUBLIC_CREDENTIALS_SALT_KEY=${NEXT_PUBLIC_CREDENTIALS_SALT_KEY}
ENV NEXT_PUBLIC_API_DOMAIN=${NEXT_PUBLIC_API_DOMAIN}

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Copy application code
COPY . .

# Environment variables must be present at build time
ENV NEXT_TELEMETRY_DISABLED 1
ENV HOST=0.0.0.0

# Build Next.js
RUN npm run build

# Step 2. Production image, copy all the files and run next
FROM base AS runner

WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs && \
    chown -R nextjs:nodejs /app

# Switch back to nextjs user
USER nextjs

# Copy necessary files from builder
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./
COPY --from=builder --chown=nextjs:nodejs /app/package-lock.json* ./
COPY --from=builder --chown=nextjs:nodejs /app/next.config.ts ./
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules

# Environment variables
ENV NEXT_TELEMETRY_DISABLED 1
ENV HOST=0.0.0.0
ENV PORT=3000

EXPOSE 3000
