"use client";

import React, { useMemo, useState } from "react";
import { AlertTriangle, ChevronRight } from "lucide-react";
import { Virtuoso } from "react-virtuoso";

import { CriticalEventSidebarItem } from "@/components/notification/critical-event-sidebar-item";
import { useNotification } from "@/contexts/notification";

export const CriticalEventSidebar: React.FC = () => {
  const {
    criticalEvents,
    updateNotificationReviewStatus,
    removeCriticalEvent,
    isPageLoaded,
  } = useNotification();

  const [processingId, setProcessingId] = useState<string | null>(null);
  const [collapsed, setCollapsed] = useState(false);

  const sortedCriticalEvents = useMemo(
    () =>
      criticalEvents
        .filter((e) => !!e.eventId)
        .sort((a, b) => {
          if (a.timestamp && b.timestamp) {
            return (
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );
          }
          return 0;
        })
        .slice(0, 50),
    [criticalEvents]
  );

  return collapsed ? (
    <button
      className="fixed top-1/2 right-0 z-50 flex min-w-14 -translate-y-1/2 items-center gap-2 rounded-l-lg bg-red-700 px-3 py-2 shadow-lg hover:bg-red-800 focus:outline-none"
      onClick={() => setCollapsed(false)}
    >
      <AlertTriangle className="size-5 text-white" />
      <span className="rounded-full bg-white px-2 py-0.5 text-sm font-semibold text-red-700">
        {sortedCriticalEvents.length > 9 ? "9+" : sortedCriticalEvents.length}
      </span>
    </button>
  ) : (
    <aside className="relative flex h-full w-80 max-w-full flex-col border-l border-gray-200 bg-white shadow-xl transition-all duration-300 dark:border-gray-700 dark:bg-gray-900">
      <div className="flex items-center justify-between border-b border-gray-200 bg-red-700 px-4 py-2.5 dark:border-gray-700">
        <h2 className="flex items-center gap-1.5 text-xl font-semibold text-white">
          Critical Events
          <span className="rounded-full bg-white px-2 py-0.5 text-sm font-semibold text-red-700">
            {sortedCriticalEvents.length > 9
              ? "9+"
              : sortedCriticalEvents.length}
          </span>
        </h2>
        <button
          className="rounded bg-white/20 p-1 text-white hover:bg-white/30 focus:outline-none"
          title="Collapse sidebar"
          onClick={() => setCollapsed(true)}
        >
          <ChevronRight className="size-4" />
        </button>
      </div>

      <Virtuoso
        data={sortedCriticalEvents}
        components={{
          Footer: () => <div className="h-px" />,
          EmptyPlaceholder: () => (
            <div className="-mb-px flex h-full items-center justify-center text-gray-400 dark:text-gray-500">
              {isPageLoaded ? "No critical events." : "Loading..."}
            </div>
          ),
        }}
        itemContent={(_, event) => (
          <CriticalEventSidebarItem
            event={event}
            processingId={processingId}
            setProcessingId={setProcessingId}
            updateNotificationReviewStatus={updateNotificationReviewStatus}
            removeCriticalEvent={removeCriticalEvent}
          />
        )}
      />
    </aside>
  );
};
