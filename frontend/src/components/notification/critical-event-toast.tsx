"use client";

import React from "react";
import Image from "next/image";
import { format } from "date-fns";
import { ExternalLink, X } from "lucide-react";

import { Notification } from "@/types/notification";
import { Skeleton } from "@/components/ui/skeleton";
import { useEvent } from "@/services/events";

interface CriticalEventToastProps {
  notification: Notification;
  setCurrentCriticalEvent: (notification: Notification | null) => void;
  removeCriticalEvent: (eventId: string) => void;
}

export const CriticalEventToast = ({
  notification,
  setCurrentCriticalEvent,
  removeCriticalEvent,
}: CriticalEventToastProps) => {
  const eventId = notification.eventId || "";
  const { event: eventData, isLoading } = useEvent(eventId);

  // Use frame_bytes directly from notification if available
  const imageData = eventData?.frame?.frame_bytes
    ? `data:image/jpeg;base64,${eventData.frame.frame_bytes}`
    : null;

  const formattedTime = eventData?.timestamp
    ? format(new Date(eventData.timestamp), "h:mm a")
    : format(new Date(notification.timestamp), "h:mm a");

  const handleViewDetails = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentCriticalEvent(notification);
  };

  const handleClose = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (eventId) {
      removeCriticalEvent(eventId);
    }
  };

  return (
    <div className="flex min-w-xs flex-col justify-between overflow-hidden rounded-lg border-l-4 border-red-500 bg-white shadow-2xl dark:bg-gray-800">
      <button
        onClick={handleClose}
        className="absolute right-2 top-2 z-10 rounded-full bg-gray-200 p-1 text-gray-500 hover:bg-gray-300 hover:text-gray-700 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-200"
      >
        <X size={14} />
      </button>
      
      <div className="flex h-full flex-col p-6">
        <div className="mb-4 flex flex-col items-center">
          {isLoading ? (
            <Skeleton className="mb-4 h-48 w-full" />
          ) : (
            imageData && (
              <div className="mb-4 flex w-full justify-center">
                <div className="relative flex h-48 w-full items-center justify-center overflow-hidden rounded-md border border-gray-300 bg-gray-100 dark:border-gray-700 dark:bg-gray-900">
                  <Image
                    src={imageData}
                    alt="Event thumbnail"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )
          )}

          <div className="flex w-full flex-col gap-0.5">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {notification.camera || "Unknown Camera"}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {formattedTime}
            </p>
          </div>
        </div>
        <div className="flex flex-1 flex-col justify-between">
          <p className="mb-4 text-base font-medium text-gray-800 dark:text-gray-200">
            {notification.message}
          </p>
          <button
            onClick={handleViewDetails}
            className="mt-auto flex w-full items-center justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-lg transition hover:bg-red-700"
          >
            <ExternalLink size={14} className="mr-2" />
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};
