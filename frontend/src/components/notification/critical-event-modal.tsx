"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertTriangle,
  Clock,
  ShieldCheck,
  X,
} from "lucide-react";
import { toast } from "react-hot-toast";

import { Notification } from "@/types/notification";
import { useNotification } from "@/contexts/notification";
import {
  markEventLegitimate,
  markEventSuspicious,
  updateEvent,
  useEvent,
} from "@/services/events";

interface CriticalEventModalProps {
  currentBanner: Notification;
}

export const CriticalEventModal = ({
  currentBanner,
}: CriticalEventModalProps) => {
  const {
    closeBanner,
    updateNotificationReviewStatus,
    setCurrentCriticalEvent,
  } = useNotification();
  const router = useRouter();
  const videoRef = React.useRef<HTMLVideoElement | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Explicitly handle closing the modal
  const handleCloseModal = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentCriticalEvent(null); // Close the critical modal
  };

  // Handler for marking event as legitimate (not suspicious)
  const handleMarkAsLegitimate = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentBanner?.eventId) {
      toast.error("Cannot process event: missing event ID");
      return;
    }

    try {
      setIsProcessing(true);
      await markEventLegitimate(currentBanner.eventId);
      toast.success("Event marked as legitimate");
      // Update the notification review status in the context
      updateNotificationReviewStatus(currentBanner.eventId, true);
      // Close the modal after successful action
      closeBanner();
    } catch (error) {
      toast.error("Failed to mark event as legitimate");
      console.log("error", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handler for marking event as suspicious
  const handleMarkAsThreat = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentBanner?.eventId) {
      toast.error("Cannot process event: missing event ID");
      return;
    }

    try {
      setIsProcessing(true);
      await markEventSuspicious(currentBanner.eventId);
      toast.success("Event marked as suspicious");
      // Update the notification review status in the context
      updateNotificationReviewStatus(currentBanner.eventId, true);
      // Close the modal after successful action
      closeBanner();
    } catch (error) {
      toast.error("Failed to mark event as suspicious");
      console.log("error", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handler for viewing full event details
  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentBanner?.eventId) {
      toast.error("Cannot view details: missing event ID");
      return;
    }

    // Close the banner before navigation to prevent state updates during navigation
    closeBanner();

    // Navigate to the event detail page
    router.push(`/dashboard/events/${currentBanner.eventId}`);
  };

  // Load and format the event time
  const [formattedTime, setFormattedTime] = React.useState<string>("");

  useEffect(() => {
    if (!currentBanner?.timestamp) return;

    const date = new Date(currentBanner.timestamp);
    setFormattedTime(format(date, "MMM d, yyyy h:mm a"));
  }, [currentBanner?.timestamp]);

  // Connect to video element if needed
  React.useEffect(() => {
    if (!currentBanner) return;

    // This would need to be connected to the actual video element
    // For now this is a placeholder as we don't have direct access to the video element
    const videoElement = document.querySelector("video");
    if (videoElement) {
      videoRef.current = videoElement;

      // Auto-play if needed
      videoElement.play().catch((e) => console.error("Auto-play failed:", e));
    }

    // Record the exact timestamp when the alert appears on screen
    if (currentBanner.eventId) {
      const alertReceivedTime = new Date().toISOString();
      console.log(
        `Alert received for event ${currentBanner.eventId} at ${alertReceivedTime}`
      );

      // Update the event with the alert received timestamp
      updateEvent(currentBanner.eventId, {
        alert_received_timestamp: alertReceivedTime,
      }).catch((error) =>
        console.error("Failed to record alert received time:", error)
      );
    }

    return () => {
      videoRef.current = null;
    };
  }, [currentBanner]);

  // Fetch the full event data which includes the frame
  const { event, isLoading: isEventLoading } = useEvent(
    currentBanner?.eventId || ""
  );

  // Debug logging to check event data structure
  useEffect(() => {
    if (event) {
      console.log("Event data received:", event);
      console.log("Event frame:", event.frame);
      if (event.frame) {
        console.log("Has frame_bytes:", !!event.frame.frame_bytes);
        console.log(
          "Frame bytes length:",
          event.frame.frame_bytes ? event.frame.frame_bytes.length : 0
        );
      }
    }
  }, [event]);

  // Only return if no banner at all
  if (!currentBanner) {
    return null;
  }

  // This will hold the base64 image data if available
  const imageData = event?.frame?.frame_bytes
    ? `data:image/jpeg;base64,${event.frame.frame_bytes}`
    : null;
  console.log("Image data available:", !!imageData);

  // We don't require boundingBoxes anymore - we'll show the modal for all weapon detection
  // or critical severity events, with or without bounding boxes
  const hasBoundingBoxes =
    currentBanner.boundingBoxes && currentBanner.boundingBoxes.length > 0;
  // We'll use the event timestamp as the selected date for the PlaybackPanel
  // The actual PlaybackPanel component will handle loading the appropriate video stream
  // based on the camera ID and date

  // Get event type label
  const getEventTypeLabel = (type: string | undefined) => {
    switch (type) {
      case "coveredPerson":
        return "Covered Person";
      case "weaponDetection":
        return "Weapon Detection";
      case "oversizeObject":
        return "Oversize Object";
      case "unattendedObject":
        return "Unattended Object";
      case "areaBreach":
        return "Area Breach";
      default:
        return "Critical Event";
    }
  };

  return (
    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
      <div className="flex max-h-[100vh] w-full max-w-7xl flex-col rounded-lg bg-white shadow-xl dark:bg-gray-800">
        {/* Modal Header */}
        <div className="flex items-center justify-between border-b border-gray-200 bg-red-700 px-6 py-4 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Critical Security Alert
          </h2>
          <button
            onClick={handleCloseModal}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={18} />
          </button>
        </div>

        {/* Modal Content - Two column layout */}
        <div className="flex flex-grow flex-col overflow-hidden sm:flex-row">
          {/* Left column - Video */}
          <div className="flex h-full w-full flex-col border-gray-200 sm:w-2/3 sm:border-r dark:border-gray-700">
            <div className="relative flex h-full flex-col">
              {/* Video container */}
              <div className="relative flex-grow bg-gray-900">
                {/* Display the image from frame_bytes if available */}
                {imageData ? (
                  <img
                    src={imageData}
                    alt="Event detection image"
                    className="h-full w-full object-contain"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-gray-800 text-gray-400">
                    {isEventLoading ? (
                      <>
                        <div className="mr-2 h-5 w-5 animate-spin rounded-full border-b-2 border-white"></div>
                        <span>Loading image...</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="mr-2" size={20} />
                        <span>No image available</span>
                      </>
                    )}
                  </div>
                )}

                {/* Overlay container positioned absolutely over the video */}
                <div className="pointer-events-none absolute inset-0">
                  {/* Bounding boxes overlay */}
                  {hasBoundingBoxes &&
                    currentBanner.boundingBoxes?.map((box, index) => (
                      <div
                        key={index}
                        className="absolute border-2 border-red-500"
                        style={{
                          left: `${box.x * 100}%`,
                          top: `${box.y * 100}%`,
                          width: `${box.width * 100}%`,
                          height: `${box.height * 100}%`,
                        }}
                      >
                        <div className="absolute -top-6 left-0 rounded bg-red-500 px-2 py-1 text-xs text-white">
                          {box.class_name} (
                          {Math.round((box.confidence || 0) * 100)}%)
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Event type and confidence overlay */}
              <div className="absolute top-4 left-4 flex items-center rounded-md bg-black/70 px-3 py-2 text-sm text-white">
                <span className="mr-2 font-semibold">
                  {getEventTypeLabel(currentBanner.eventType)}
                </span>
                <span className="text-xs opacity-80">
                  {currentBanner.confidence
                    ? `${Math.round(currentBanner.confidence * 100)}% confidence`
                    : ""}
                </span>
              </div>
            </div>
          </div>

          {/* Right column - Event details and actions */}
          <div className="flex h-full w-full flex-col overflow-y-auto sm:w-1/3">
            <div className="p-6">
              {/* Event metadata */}
              <div className="mb-6">
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Camera
                  </h3>
                  <p className="text-base font-semibold text-gray-900 dark:text-white">
                    {currentBanner.camera || "Unknown"}
                  </p>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Timestamp
                  </h3>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-gray-400" />
                    <p className="text-gray-900 dark:text-white">
                      {formattedTime}
                    </p>
                  </div>
                </div>
              </div>

              {/* Alert message */}
              <div className="mb-6 rounded-md border border-red-400 bg-red-100 p-5 dark:bg-red-900/30">
                <div className="flex items-start">
                  <AlertTriangle
                    className="mr-3 flex-shrink-0 text-red-600"
                    size={24}
                  />
                  <div className="w-full">
                    <h3 className="mb-1 text-base font-semibold text-red-700 dark:text-red-300">
                      {getEventTypeLabel(currentBanner.eventType)} Detected
                    </h3>
                    <p className="text-sm text-red-700 dark:text-red-300">
                      {currentBanner.message}
                    </p>

                    {/* Event details */}
                    <div className="mt-4 space-y-2">
                      {/* Detection details */}
                      <div className="flex justify-between text-sm">
                        <span className="font-medium text-red-800 dark:text-red-200">
                          Detection Type:
                        </span>
                        <span className="text-red-700 dark:text-red-300">
                          {getEventTypeLabel(currentBanner.eventType)}
                        </span>
                      </div>

                      {/* Confidence level */}
                      {currentBanner.confidence && (
                        <div className="flex justify-between text-sm">
                          <span className="font-medium text-red-800 dark:text-red-200">
                            Confidence:
                          </span>
                          <span className="text-red-700 dark:text-red-300">
                            {Math.round(currentBanner.confidence * 100)}%
                          </span>
                        </div>
                      )}

                      {/* Objects detected */}
                      <div className="flex justify-between text-sm">
                        <span className="font-medium text-red-800 dark:text-red-200">
                          Objects Detected:
                        </span>
                        <span className="text-red-700 dark:text-red-300">
                          {currentBanner.boundingBoxes?.length || 0}
                        </span>
                      </div>

                      {/* Severity indicator */}
                      <div className="mt-3 flex items-center justify-between">
                        <span className="text-sm font-medium text-red-800 dark:text-red-200">
                          Severity:
                        </span>
                        <span className="inline-flex items-center rounded-full border border-red-400 bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900/50 dark:text-red-300">
                          {currentBanner.confidence &&
                          currentBanner.confidence > 0.8
                            ? "High"
                            : currentBanner.confidence &&
                                currentBanner.confidence > 0.5
                              ? "Medium"
                              : "Low"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Classification buttons */}
              <div className="mb-6 grid grid-cols-1 gap-3">
                <button
                  onClick={handleMarkAsLegitimate}
                  disabled={isProcessing}
                  className={`flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none ${
                    isProcessing
                      ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                      : "border-green-300 bg-white text-green-700 hover:bg-green-50 dark:border-green-700 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-gray-600"
                  }`}
                >
                  <ShieldCheck className="mr-1.5 h-4 w-4" />
                  {isProcessing
                    ? "Processing..."
                    : "Confirm No Threat (Legitimate)"}
                </button>
                <button
                  onClick={handleMarkAsThreat}
                  disabled={isProcessing}
                  className={`flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none ${
                    isProcessing
                      ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                      : "border-red-300 bg-white text-red-700 hover:bg-red-50 dark:border-red-700 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-gray-600"
                  }`}
                >
                  <AlertCircle className="mr-2 h-4 w-4" />
                  {isProcessing ? "Processing..." : "Mark as Suspicious"}
                </button>
              </div>

              {/* Action buttons */}
              <div className="mt-6">
                <button
                  onClick={handleViewDetails}
                  disabled={isProcessing}
                  className={`mb-3 flex w-full items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none ${
                    isProcessing
                      ? "cursor-not-allowed border-gray-300 bg-gray-300 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
                      : "border-transparent bg-indigo-600 text-white hover:bg-indigo-700"
                  }`}
                >
                  {isProcessing ? "Please wait..." : "View Full Details"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
