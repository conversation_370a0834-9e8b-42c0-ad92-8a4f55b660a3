"use client";

import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { AlertCircle, ExternalLink, ShieldCheck, X } from "lucide-react";
import { toast } from "react-hot-toast";

import { Notification } from "@/types/notification";
import {
  markEventLegitimate,
  markEventSuspicious,
  useEvent,
} from "@/services/events";

interface CriticalEventSidebarItemProps {
  event: Notification;
  processingId: string | null;
  setProcessingId: (id: string | null) => void;
  updateNotificationReviewStatus: (eventId: string, reviewed: boolean) => void;
  removeCriticalEvent: (eventId: string) => void;
}

export const CriticalEventSidebarItem: React.FC<
  CriticalEventSidebarItemProps
> = ({
  event,
  processingId,
  setProcessingId,
  updateNotificationReviewStatus,
  removeCriticalEvent,
}) => {
  const router = useRouter();
  const { eventId: id, camera, message, timestamp } = event;
  const eventId = id as string;
  const { event: eventData, isLoading, mutate } = useEvent(eventId as string);
  const imageData = eventData?.frame?.frame_bytes
    ? `data:image/jpeg;base64,${eventData.frame.frame_bytes}`
    : null;
  const formattedTime = eventData?.timestamp
    ? format(new Date(eventData.timestamp), "MMM d, yyyy h:mm a")
    : format(new Date(timestamp), "MMM d, yyyy h:mm a");

  const handleMarkAsLegitimate = async () => {
    try {
      setProcessingId(eventId);
      await markEventLegitimate(eventId);
      mutate();
      toast.success("Event marked as legitimate");
      updateNotificationReviewStatus(eventId, true);
      removeCriticalEvent(eventId);
    } catch (error) {
      toast.error("Failed to mark event as legitimate");
      console.log("error", error);
    } finally {
      setProcessingId(null);
    }
  };

  const handleMarkAsThreat = async () => {
    try {
      setProcessingId(eventId);
      await markEventSuspicious(eventId);
      mutate();
      toast.success("Event marked as suspicious");
    } catch (error) {
      toast.error("Failed to mark event as suspicious");
      console.log("error", error);
    } finally {
      setProcessingId(null);
    }
  };

  // Handler for viewing full event details
  const handleViewDetails = () => {
    router.push(`/dashboard/events/${eventId}`);
  };

  return (
    <div className="relative m-4 flex flex-col rounded-lg border border-gray-200 bg-white p-3 shadow dark:border-gray-700 dark:bg-gray-800">
      <button
        onClick={() => removeCriticalEvent(eventId)}
        disabled={processingId === eventId}
        aria-label="Remove event"
        className={`absolute -top-3 -right-3 z-10 m-1 rounded-full bg-gray-700 p-1 transition-colors focus:outline-none ${
          processingId === eventId
            ? "cursor-not-allowed text-gray-300 dark:text-gray-600"
            : "text-gray-100 hover:bg-gray-700 hover:text-white"
        }`}
        style={{ lineHeight: 0 }}
      >
        <X className="size-3" />
      </button>

      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <span className="flex items-center text-xs font-bold text-gray-700 dark:text-gray-300">
            {camera || "Unknown Camera"}
          </span>
          <span className="text-xs text-gray-400">{formattedTime}</span>
        </div>

        {isLoading ? (
          <div className="h-36 w-full animate-pulse rounded bg-gray-100 dark:bg-gray-700" />
        ) : imageData ? (
          <div className="flex w-full justify-center">
            <div className="relative h-36 w-full overflow-hidden rounded border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900">
              <Image
                src={imageData}
                alt="Event thumbnail"
                fill
                className="object-cover"
              />
            </div>
          </div>
        ) : null}

        <div className="line-clamp-2 text-sm font-medium text-gray-800 dark:text-gray-200">
          {message}
        </div>

        <div className="grid w-full grid-cols-2 gap-2">
          {eventData?.is_reviewed && eventData.is_suspicious ? (
            <span
              className="col-span-2 flex items-center justify-center rounded-md border border-red-300 bg-red-50 px-2 py-1 text-xs font-semibold text-red-700 dark:border-red-700 dark:bg-gray-700 dark:text-red-400"
              style={{ minWidth: 0 }}
            >
              <AlertCircle className="mr-1 h-4 w-4" />
              Marked as Threat
            </span>
          ) : (
            <>
              <button
                onClick={handleMarkAsLegitimate}
                disabled={processingId === eventId}
                className={`flex items-center justify-center rounded-md border px-2 py-1 text-xs font-medium shadow-sm transition-all duration-100 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none ${
                  processingId === eventId
                    ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                    : "border-green-300 bg-white text-green-700 hover:bg-green-50 dark:border-green-700 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-gray-600"
                }`}
                style={{ minWidth: 0 }}
              >
                <ShieldCheck className="mr-1 h-4 w-4" />
                {processingId === eventId ? "Processing..." : "No Threat"}
              </button>
              <button
                onClick={handleMarkAsThreat}
                disabled={processingId === eventId}
                className={`flex items-center justify-center rounded-md border px-2 py-1 text-xs font-medium shadow-sm transition-all duration-100 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none ${
                  processingId === eventId
                    ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                    : "border-red-300 bg-white text-red-700 hover:bg-red-50 dark:border-red-700 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-gray-600"
                }`}
                style={{ minWidth: 0 }}
              >
                <AlertCircle className="mr-1 h-4 w-4" />
                {processingId === eventId ? "Processing..." : "Suspicious"}
              </button>
            </>
          )}
        </div>

        <button
          onClick={handleViewDetails}
          disabled={processingId === eventId}
          className={`flex items-center justify-center rounded-md border px-2 py-1 text-xs font-medium shadow-sm transition-all duration-100 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none ${
            processingId === eventId
              ? "cursor-not-allowed border-gray-300 bg-gray-300 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
              : "border-transparent bg-indigo-600 text-white hover:bg-indigo-700"
          }`}
          style={{ minWidth: 0 }}
        >
          <ExternalLink className="mr-1 size-3.5" />
          {processingId === eventId ? "Please wait..." : "Details"}
        </button>
      </div>
    </div>
  );
};
