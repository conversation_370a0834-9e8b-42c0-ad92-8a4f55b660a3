"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import CryptoJS from 'crypto-js';

export default function LoginForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  
  // Salt key for credential encryption - should match backend's CREDENTIALS_SALT_KEY
  // TypeScript needs a non-undefined value, so we provide a fallback
  const CREDENTIALS_SALT_KEY = process.env.NEXT_PUBLIC_CREDENTIALS_SALT_KEY || '';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Ensure we have the required fields with correct names
      const credentials = {
        email: formData.email, // This matches what the backend expects
        password: formData.password // This matches what the backend expects
      };
      
      // Debug log (without displaying the actual password)
      console.log('Form data structure:', { email: formData.email, hasPassword: !!formData.password });
      
      // Convert credentials to JSON string
      const jsonString = JSON.stringify(credentials);
      
      // Encrypt the credentials using CryptoJS AES
      const encrypted = CryptoJS.AES.encrypt(jsonString, CREDENTIALS_SALT_KEY).toString();
      
      // Create the payload with encrypted cipher
      const payload = {
        cipher: encrypted
      };
      
      // Debug the payload (safe to log as it's encrypted)
      console.log('Sending encrypted payload');
      
      // Send to the main login endpoint (which now handles encrypted credentials)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_DOMAIN}/user/public/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Login error response:', data);
        throw new Error(data.message || 'Login failed');
      }

      // Successful login
      toast({
        title: "Login successful",
        description: "Welcome back! Securely authenticated.",
      });

      // Redirect to main page
      router.push('/');
      // router.refresh();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Login failed",
        description: error instanceof Error ? error.message : 'An error occurred during secure login',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          required
          value={formData.email}
          onChange={handleChange}
          disabled={isLoading}
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="password">Password</Label>
        </div>
        <Input
          id="password"
          name="password"
          type="password"
          required
          value={formData.password}
          onChange={handleChange}
          disabled={isLoading}
        />
      </div>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? "Logging in..." : "Login"}
      </Button>
    </form>
  );
}
