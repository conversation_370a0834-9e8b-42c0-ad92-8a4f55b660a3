"use client";

import React from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import api from "@/services/api";

const LogoutButton = () => {
  const { push } = useRouter();

  const handleClick = async () => {
    try {
      await api.get("/user/user-session/v1/logout");

      push("/login");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return <Button onClick={handleClick}>Logout</Button>;
};

export default LogoutButton;
