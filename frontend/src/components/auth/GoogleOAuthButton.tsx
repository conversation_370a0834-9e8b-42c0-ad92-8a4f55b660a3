"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";

import { CustomResponse } from "@/types/response";
import { LoaderButton } from "@/components/ui/loader-button";
import Google from "@/icons/Google";
import api from "@/services/api";

const GoogleOAuthButton = () => {
  const { push } = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleClick = async () => {
    try {
      setIsLoading(true);

      const {
        data: {
          data: { message },
        },
      } = await api.get<CustomResponse<{ message: string }>>(
        "/oauth-provider/google-oauth/v1/authorize"
      );

      push(message);
    } catch (error) {
      console.error("Authorization error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LoaderButton
      size="lg"
      className="w-full"
      onClick={handleClick}
      isLoading={isLoading}
      icon={Google}
    >
      Login with Google
    </LoaderButton>
  );
};

export default GoogleOAuthButton;
