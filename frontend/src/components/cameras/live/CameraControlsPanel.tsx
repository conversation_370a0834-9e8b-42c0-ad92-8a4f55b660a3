"use client";

import React, { useEffect } from 'react';
import { Camera, CameraStatus } from '@/types/camera';
import { 
  SettingsIcon 
} from '@/components/icons/CameraIcons';
import { useCamera } from '@/services/cameras';

interface CameraControlsPanelProps {
  cameraId: string;
  camera: Camera | null;
  onSetActiveTab: (tab: 'layers' | 'regions' | 'live' | 'playback' | 'events' | 'settings') => void;
}

const CameraControlsPanel: React.FC<CameraControlsPanelProps> = ({
  cameraId,
  onSetActiveTab
}) => {
  // Use SWR to get the latest camera data directly from the API
  const { camera: cameraData, isLoading } = useCamera(cameraId);
  
  // Debug the current camera status
  useEffect(() => {
    if (cameraData) {
      console.log('CameraControlsPanel: Current camera status from API:', cameraData.status);
    }
  }, [cameraData]);
  // Determine camera status styling - using a more prominent style for the status
  const getStatusColor = (status?: CameraStatus) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'offline':
        return 'bg-red-500';
      case 'error':
        return 'bg-yellow-500';
      default:
        // Default to online since we know streams are working
        return 'bg-green-500';
    }
  };

  const getStatusText = (status?: CameraStatus) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'offline':
        return 'Offline';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-4">
      {/* Camera Info */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <h2 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">Camera Details</h2>
        
        {cameraData ? (
          <div className="space-y-3">
            <div className="rounded-md bg-gray-50 p-3 dark:bg-gray-700">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-3">
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Camera Name</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">{cameraData.name}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Location</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">{cameraData.location || 'Not specified'}</dd>
                </div>
                {/* <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Status</dt>
                  <dd className="flex items-center text-sm font-medium text-gray-900 dark:text-white">
                    <span className={`mr-2 inline-block h-2 w-2 rounded-full ${getStatusColor(cameraData?.status)}`}></span>
                    {getStatusText(cameraData?.status)}
                    {isLoading && <span className="ml-2 text-xs text-gray-400">(refreshing...)</span>}
                  </dd>
                </div> */}
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Description</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white truncate max-w-[60%]">{cameraData?.description || 'No description provided'}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Last Updated</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">
                    {cameraData?.updated_at ? new Date(cameraData.updated_at).toLocaleDateString() : 'Unknown'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Camera ID</dt>
                  <dd className="flex items-center">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                      {cameraData?.id ? 
                        `****${cameraData.id.substring(cameraData.id.length - 4)}` 
                        : 'Not available'}
                    </code>
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Last Status Check</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">
                    {cameraData?.last_status_check ? new Date(cameraData.last_status_check).toLocaleString() : 'Unknown'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500 dark:text-gray-400">Created</dt>
                  <dd className="text-sm font-medium text-gray-900 dark:text-white">
                    {cameraData?.created_at ? new Date(cameraData.created_at).toLocaleDateString() : 'Unknown'}
                  </dd>
                </div>
              </dl>
            </div>
            
            <button
              onClick={() => onSetActiveTab('settings')}
              className="mt-2 flex w-full items-center justify-center rounded-md bg-primary-50 px-3 py-2 text-sm font-medium text-primary-700 hover:bg-primary-100 dark:bg-primary-900 dark:text-primary-200 dark:hover:bg-primary-800"
            >
              <SettingsIcon className="mr-2 h-4 w-4" />
              Edit Camera Settings
            </button>
          </div>
        ) : (
          <div className="rounded-md bg-gray-50 p-4 text-center dark:bg-gray-700">
            <p className="text-sm text-gray-500 dark:text-gray-400">Camera details not available</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CameraControlsPanel;
