"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { CameraComponentProps } from '@/types/camera';
import { CameraLayerConfiguration, CameraLayerConfigurationRequest, Layer, DependencyRequest } from '@/types/layer';
import { useLayers, addLayer, updateLayer, deleteLayer } from '@/services/layers';
import { toast } from 'react-hot-toast';
import { Switch } from '@headlessui/react';
import LayerEditModal from './LayerEditModal';

interface LayersPanelProps extends CameraComponentProps {
  localLayers?: CameraLayerConfiguration[];
  baseLayers?: Layer[];
  activeLayer?: string | null;
  onLayerSelected?: (layerId: string) => void;
  onLayerToggle?: (layerId: string, enabled: boolean) => void;
  isLayersLoading?: boolean;
  isLayersError?: boolean;
  onLayerSaved?: () => void; // New callback for when a layer is saved
}

const PlusIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
  </svg>
);

const EditIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
  </svg>
);

// Function to validate and correct hex color formats
const validateHexColor = (color?: string): boolean => {
  if (!color) return false;
  
  // Valid formats: #RGB or #RRGGBB
  const hexColorRegex = /^#([0-9A-F]{3}){1,2}$/i;
  return hexColorRegex.test(color);
};

// Generate a valid 6-digit hex color
const generateRandomColor = (): string => {
  // Generate random RGB values (0-255)
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  
  // Convert to hex and pad with leading zeros if needed
  const rHex = r.toString(16).padStart(2, '0');
  const gHex = g.toString(16).padStart(2, '0');
  const bHex = b.toString(16).padStart(2, '0');
  
  // Return the full 6-digit hex color
  return `#${rHex}${gHex}${bHex}`;
};

const LayersPanel: React.FC<LayersPanelProps> = ({ 
  cameraId, 
  localLayers = [],
  baseLayers = [],
  onLayerSelected,
  onLayerToggle,
  isLayersLoading = false,
  isLayersError = false,
  onLayerSaved
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingLayer, setEditingLayer] = useState<CameraLayerConfiguration | null>(null);
  const [isEditMode, setIsEditMode] = useState(false); // Track if we're editing or creating
  const [deletingLayer, setDeletingLayer] = useState<CameraLayerConfiguration | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only use SWR if not provided via props (fallback)
  const { isLoading, isError, mutate: refetchLayers } = useLayers(
    localLayers.length === 0 && cameraId ? cameraId : ''
  );
  
  // Function to perform a thorough refresh of layer data
  const refreshLayerData = useCallback(async () => {
    await refetchLayers();
  }, [refetchLayers]);
  
  // If we're using the fallback SWR, update when data changes
  useEffect(() => {
    if (localLayers.length === 0 && cameraId) {
      // Refresh on component mount and when cameraId changes
      refreshLayerData();
    }
  }, [cameraId, localLayers.length, refreshLayerData]);
  
  // Add an effect to periodically refresh data
  useEffect(() => {
    if (!cameraId) return;
    
    const intervalId = setInterval(() => {
      refreshLayerData();
    }, 10000);
    
    return () => clearInterval(intervalId);
  }, [cameraId, refreshLayerData]);
  
  // Initialize a new layer
  const handleAddLayer = async () => {
    // Set a temporary ID until we save to backend
    const tempId = `temp_${Date.now()}`;
    
    // Create a new layer with default values
    const newLayer: CameraLayerConfiguration = {
      id: tempId,
      name: 'New Layer',
      enabled: true,
      color: generateRandomColor(), // Generate a valid 6-digit hex color
      is_default: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      camera: cameraId,
      regions: [],
      dependencies: [],
      configuration: {}
    };
    
    // Open the edit modal with this new layer
    setEditingLayer(newLayer);
    setIsEditMode(false); // Set edit mode flag to false for new layers
    setIsModalOpen(true);
  };
  
  // Save a layer (new or edited)
  const handleSaveLayer = async (layer: CameraLayerConfigurationRequest) => {
    
    // Validate required fields
    if (!layer.name || layer.name.trim() === '') {
      toast.error('Layer name is required');
      return;
    }
    
    if (!layer.layer_id) {
      toast.error('Layer type is required');
      return;
    }
    
    // Extract dependency IDs - backend expects simple array of UUIDs
    const dependencyIds: string[] = [];
    // Store conditions separately - we'll handle them when creating the dependency relationships
    const dependencyRequest: DependencyRequest[] = [];
    
    if (layer.dependencies && layer.dependencies.length > 0) {
      layer.dependencies.forEach(dep => {
        // Get the dependency ID
        let depId;
        if (typeof dep.dependency_id === 'string') {
          depId = dep.dependency_id;
        } else {
          depId = dep.dependency_id;
        }
        
        // Store the ID and conditions separately
        dependencyIds.push(depId);
        dependencyRequest.push({
          dependency_id: depId,
          conditions: dep.conditions || {}
        });
      });
    }
    
    if (!cameraId) return;
    
    setIsSubmitting(true);
    
    try {
      if (!isEditMode) {
        // Creating a new layer
        await addLayer(cameraId, layer);
        toast.success('Layer created successfully');
      } else if (editingLayer && editingLayer.id) {
        // Editing an existing layer
        await updateLayer(cameraId, editingLayer.id, layer);
        toast.success('Layer updated successfully');
      }
      
      setIsModalOpen(false);
      setEditingLayer(null);
      
      // Navigate to the Layers tab if callback provided, otherwise reload page
      if (onLayerSaved) {
        onLayerSaved();
      } else {
        // Fallback to page reload if no callback provided
        window.location.reload();
      }
    } catch (error) {
      console.error('Error saving layer:', error);
      toast.error(`Failed to save layer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle editing an existing layer
  const handleEditLayer = (layerId: string) => {
    // Find the layer in our local state
    const layer = localLayers.find(l => l.id === layerId);
    
    if (layer) {
      setEditingLayer(layer);
      setIsEditMode(true); // Set edit mode flag to true for existing layers
      setIsModalOpen(true);
    } else {
      toast.error('Layer not found');
    }
  };
  
  // Delete a layer
  const handleDeleteLayer = async (layerToDelete?: CameraLayerConfiguration) => {
    // If called from the modal, use the provided layer
    // If called from the confirmation dialog, use the deletingLayer state
    const layerToRemove = layerToDelete || deletingLayer;
    
    if (!layerToRemove || !cameraId) return;
    
    try {
      // If called from modal, we need to close the modal and set the deletingLayer
      if (layerToDelete) {
        setIsModalOpen(false);
        setDeletingLayer(layerToDelete);
        return; // Don't actually delete yet, just show confirmation dialog
      }
      
      // Actual delete operation - only happens after confirmation
      await deleteLayer(cameraId, layerToRemove.id);
      toast.success('Layer deleted successfully');
      
      window.location.reload();
      setDeletingLayer(null);
    } catch (error) {
      console.error('Error deleting layer:', error);
      toast.error(`Failed to delete layer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  
  const handleLayerToggle = async (layer: CameraLayerConfiguration) => {
    try {
      if (onLayerToggle) {
        onLayerToggle(layer.id, !layer.enabled);
      } else {
        if (!layer.id.startsWith('temp_')) {
          await updateLayer(cameraId, layer.id, { enabled: !layer.enabled });
          refetchLayers();
        }
      }
    } catch (error) {
      console.error('Error toggling layer:', error);
      toast.error(`Failed to update layer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Detection Layers</h3>
        
        <div className="flex items-center">
          <div className="flex items-center space-x-2">
            {/* Connection status indicator */}
            <span className={`inline-block h-2 w-2 rounded-full ${isLayersLoading || isLoading 
              ? "bg-yellow-500" 
              : localLayers.length > 0 
                ? "bg-green-500" 
                : isLayersError || isError 
                  ? "bg-red-500" 
                  : "bg-gray-500"}`}></span>
            
            <span className="text-sm text-gray-500">
              {isLayersLoading || isLoading 
                ? "Loading..." 
                : localLayers.length > 0 
                  ? "Connected to server" 
                  : isLayersError || isError 
                    ? "Failed to connect to server" 
                    : "No layers found"}
            </span>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        {/* Always render layers in alphabetical order by name */}
        {localLayers.map(layer => (
          <div 
            key={layer.id}
            className={`group relative mb-2 cursor-pointer rounded-md border ${
              layer.enabled 
                ? 'border-green-500 dark:border-green-700' 
                : 'border-gray-300 dark:border-gray-700'
            } p-3 shadow-sm transition-all hover:border-primary-400 dark:hover:border-primary-600`}
            onClick={() => {
              if (onLayerSelected && layer.id) {
                onLayerSelected(layer.id);
              }
            }}
          >
            <div className="flex items-stretch">
              {/* Left side content (name and chips) */}
              <div className="flex-grow flex flex-col space-y-2">
                {/* Layer name row */}
                <div className="flex items-center space-x-2">
                  <span
                    className="h-4 w-4 flex-shrink-0 rounded-full"
                    style={{ backgroundColor: validateHexColor(layer.color) ? layer.color : '#888888' }}
                  ></span>
                  <span className="font-medium">{layer.name}</span>
                </div>
                
                {/* Chips row */}
                <div className="flex flex-wrap items-center gap-1">
                  {/* Layer type chip */}
                  {layer.layer && layer.layer.layer_type && (
                    <span 
                      aria-label={`Layer type ${layer.layer.layer_type}`}
                      className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    >
                      Type {layer.layer.layer_type}
                    </span>
                  )}
                  
                  {/* Base layer name chip */}
                  {layer.layer && layer.layer.name && (
                    <span 
                      aria-label={`Base layer: ${layer.layer.name}`}
                      className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                    >
                      {layer.layer.name}
                    </span>
                  )}
                  
                  {/* Region status chip - Calculate status outside of JSX */}
                  {(() => {
                    // Prepare region status text and determine if regions exist
                    const hasRegions = layer.regions && layer.regions.length > 0;
                    
                    let regionStatusText = 'No Region';
                    if (hasRegions) {
                      const hasLine = layer.regions.some((region) => region.roi_type === 'line');
                      const hasArea = layer.regions.some((region) => region.roi_type === 'region');
                      
                      if (hasLine && hasArea) regionStatusText = 'Trip Wire & Area';
                      else if (hasLine) regionStatusText = 'Trip Wire';
                      else if (hasArea) regionStatusText = 'Area';
                      else regionStatusText = 'Region'; // Fallback
                    }
                    
                    return (
                      <span 
                        aria-label={`Region status: ${regionStatusText}`}
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          hasRegions
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}
                      >
                        {regionStatusText}
                      </span>
                    );
                  })()}
                </div>
              </div>
              
              {/* Right side controls (vertically centered) */}
              <div className="flex items-center ml-3 pl-3 border-l border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3">
                  <Switch
                    checked={layer.enabled}
                    onChange={() => handleLayerToggle(layer)}
                    className={`${
                      layer.enabled ? 'bg-green-600' : 'bg-gray-300 dark:bg-gray-700'
                    } relative inline-flex h-5 w-10 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2`}
                  >
                    <span
                      aria-hidden="true"
                      className={`${layer.enabled ? 'translate-x-5' : 'translate-x-0'}
                      inline-block h-4 w-4 transform rounded-full bg-white dark:bg-gray-100 shadow-md ring-0 transition duration-200 ease-in-out`}
                    />
                  </Switch>
                  
                  <EditIcon
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent the card's onClick from firing
                      handleEditLayer(layer.id);
                    }}
                    className="h-4 w-4 cursor-pointer text-gray-500 hover:text-gray-700"
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Empty state - No layers */}
        {localLayers.length === 0 && !isLoading && (
          <div className="flex flex-col items-center justify-center space-y-2 rounded-md border border-dashed border-gray-300 p-6 text-center dark:border-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-gray-500 dark:text-gray-400">No layers configured. Click &quot;Add Layer&quot; to create one.</p>
          </div>
        )
        }
        
        {/* Add Layer button as a card */}
        {!isLoading && (
          <div 
            onClick={handleAddLayer}
            className="group relative mb-2 cursor-pointer rounded-md border-2 border-dashed border-gray-300 p-3 shadow-sm transition-all hover:border-primary-500 hover:bg-gray-50 dark:border-gray-700 dark:hover:border-primary-400 dark:hover:bg-gray-800"
          >
            <div className="flex items-center justify-center">
              <div className="flex items-center">
                <PlusIcon className="h-5 w-5 text-gray-400 group-hover:text-primary-500 dark:text-gray-500 dark:group-hover:text-primary-400" />
                <p className="text-sm font-medium text-gray-500 group-hover:text-primary-600 dark:text-gray-400 dark:group-hover:text-primary-400">Add New Layer</p>
              </div>
            </div>
          </div>
        )}
        
        {isLoading && (
          <div className="flex items-center justify-center space-x-2 p-6 text-center">
            <svg className="h-5 w-5 animate-spin text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-sm text-gray-500 dark:text-gray-400">Loading layers...</span>
          </div>
        )}
      </div>
      
      {/* Layer Edit Dialog - Using the extracted LayerEditModal component */}
      {isModalOpen && editingLayer && (
        <LayerEditModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          editingLayer={editingLayer}
          onSave={handleSaveLayer}
          onDelete={handleDeleteLayer}
          isSubmitting={isSubmitting}
          cameraId={cameraId}
          baseLayers={baseLayers}
          isEditMode={isEditMode}
        />
      )}
      
      {/* Delete Confirmation Dialog */}
      {deletingLayer && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-medium">Delete Layer</h3>
            <p>Are you sure you want to delete <strong>{deletingLayer.name}</strong>?</p>
            <p className="mt-2 text-sm text-gray-500">This action cannot be undone.</p>
            
            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => setDeletingLayer(null)}
                className="rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (deletingLayer && deletingLayer.id) {
                    handleDeleteLayer();
                  }
                  setDeletingLayer(null);
                }}
                className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LayersPanel;