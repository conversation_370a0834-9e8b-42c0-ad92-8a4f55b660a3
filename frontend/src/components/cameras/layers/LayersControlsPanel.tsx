"use client";

import React from 'react';
import { CameraComponentProps } from '@/types/camera';
import { CameraLayerConfiguration, Layer } from '@/types/layer';
import LayersPanel from '@/components/cameras/layers/LayersPanel';

interface LayersControlsPanelProps extends CameraComponentProps {
  localLayers?: CameraLayerConfiguration[];
  baseLayers?: Layer[];
  activeLayer?: string | null;
  isLayersLoading?: boolean;
  isLayersError?: boolean;
  onLayerToggle?: (layerId: string, enabled: boolean) => void;
  onLayerSaved?: () => void; // New callback for when a layer is saved
}

const LayersControlsPanel: React.FC<LayersControlsPanelProps> = ({
  cameraId,
  camera,
  localLayers,
  baseLayers,
  isLayersLoading,
  isLayersError,
  onLayerToggle,
  onLayerSaved
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
      <LayersPanel 
        cameraId={cameraId} 
        camera={camera}
        localLayers={localLayers}
        baseLayers={baseLayers}
        isLayersLoading={isLayersLoading}
        isLayersError={isLayersError}
        onLayerToggle={onLayerToggle}
        onLayerSaved={onLayerSaved}
      />
    </div>
  );
};

export default LayersControlsPanel;
