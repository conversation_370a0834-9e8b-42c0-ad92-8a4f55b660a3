"use client";

import React, { useRef, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { CameraComponentProps, Camera } from '@/types/camera';
import { CameraLayerConfiguration } from '@/types/layer';
import { Coordinate } from '@/types/region';
import StreamViewer from '../common/StreamViewer';

interface LayersTabProps extends CameraComponentProps {
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  localLayers: CameraLayerConfiguration[];
}

/**
 * Component to display and edit regions of interest for camera layers
 */
const LayersTab: React.FC<LayersTabProps> = ({ 
  camera, 
  localLayers,
}) => {
  // Ref for video container to get dimensions
  const videoContainerRef = useRef<HTMLDivElement | null>(null);
  // State to track container dimensions
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  
  // Update container dimensions on mount and on window resize
  useEffect(() => {
    // Function to update dimensions
    const updateDimensions = () => {
      if (videoContainerRef.current) {
        const width = videoContainerRef.current.clientWidth;
        const height = videoContainerRef.current.clientHeight;
        setContainerDimensions({ width, height });
      }
    };
    
    // Update dimensions on mount
    updateDimensions();
    
    // Also set up a small delay to ensure the component has fully rendered
    const timeoutId = setTimeout(updateDimensions, 300);
    
    // Add resize listener
    window.addEventListener('resize', updateDimensions);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', updateDimensions);
      clearTimeout(timeoutId);
    };
  }, []);
  
  // Helper function to render a region of interest based on coordinates
  const renderRegion = (coordinates: Array<Coordinate>, color: string, isActive: boolean = false) => {
    if (!coordinates || coordinates.length === 0) {
      return null;
    }

    // Function to safely get normalized value or fallback to raw value divided by container size
    const getNormalizedValue = (coord: Coordinate, property: 'x' | 'y') => {
      // If normalized values exist, use them
      if (property === 'x' && coord.normalizedX !== undefined) {
        return coord.normalizedX;
      } else if (property === 'y' && coord.normalizedY !== undefined) {
        return coord.normalizedY;
      }
      
      // If raw values exist and they're between 0 and 1, they might already be normalized
      if (coord[property] !== undefined) {
        // If values are already in the 0-1 range, they're likely normalized
        if (coord[property] >= 0 && coord[property] <= 1) {
          return coord[property];
        }
        
        // Otherwise, divide by container dimensions to normalize
        // We'll use a default of 100% of container size here
        const containerSize = property === 'x' ? containerDimensions.width : containerDimensions.height;
        return containerSize > 0 ? coord[property] / containerSize : 0;
      }
      
      // Fallback
      return 0;
    };
    
    if (coordinates.length >= 3) {
      // SVG polygons can't use percentage strings for points attribute
      // We need to map normalized coordinates to actual viewport dimensions
      const viewportWidth = containerDimensions.width || 100;
      const viewportHeight = containerDimensions.height || 100;
      
      const coordinatesString = coordinates.map((p: Coordinate) => {
        // Convert 0-1 normalized coordinates to actual pixel values based on container size
        const x = getNormalizedValue(p, 'x') * viewportWidth;
        const y = getNormalizedValue(p, 'y') * viewportHeight;
        return `${x},${y}`;
      }).join(' ');
      
      return (
        <>
          {/* Polygon with semi-transparency */}
          <polygon
            points={coordinatesString}
            fill={`${color}33`} /* 20% opacity */
            stroke={color}
            strokeWidth="2"
            strokeOpacity={isActive ? "1" : "0.7"}
          />
          {isActive && coordinates.map((coordinate: Coordinate, idx: number) => {
            const viewportWidth = containerDimensions.width || 100;
            const viewportHeight = containerDimensions.height || 100;
            const cx = getNormalizedValue(coordinate, 'x') * viewportWidth;
            const cy = getNormalizedValue(coordinate, 'y') * viewportHeight;
            
            return (
              <circle
                key={idx}
                cx={cx}
                cy={cy}
                r="5"
                fill="white"
                stroke={color}
                strokeWidth="2"
              />
            );
          })}
        </>
      );
    } else if (coordinates.length === 2) {
      // If two points, render a line
      // Use the same coordinate conversion as polygons for consistency
      const viewportWidth = containerDimensions.width || 100;
      const viewportHeight = containerDimensions.height || 100;
      
      const x1 = getNormalizedValue(coordinates[0], 'x') * viewportWidth;
      const y1 = getNormalizedValue(coordinates[0], 'y') * viewportHeight;
      const x2 = getNormalizedValue(coordinates[1], 'x') * viewportWidth;
      const y2 = getNormalizedValue(coordinates[1], 'y') * viewportHeight;
      
      return (
        <line
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
          stroke={color}
          strokeWidth="3"
          strokeOpacity={isActive ? "1" : "0.7"}
        />
      );
    }
    
    return null;
  };
  
  // Helper function to get stream URL for the camera
  const getCameraStreamUrl = (camera: Camera | null | undefined): string => {
    if (!camera) return '';
    
    return camera.stream_url || '';
  };
  
  // For debugging - add a small debug panel to the component
  const [showDebug, setShowDebug] = useState(false);

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
      {/* Debug toggle button */}
      <div className="absolute top-2 right-2 z-20">
        <button 
          onClick={() => setShowDebug(!showDebug)}
          className="rounded-md bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
        >
          {showDebug ? 'Hide Debug' : 'Debug'}
        </button>
      </div>
      
      {/* Debug panel */}
      {showDebug && (
        <div className="absolute top-10 right-2 z-20 max-h-64 w-72 overflow-auto rounded-md bg-white p-2 text-xs shadow-lg">
          <h4 className="font-bold">Active Layers ({localLayers.filter(l => l.enabled).length}):</h4>
          <ul className="mt-1">
            {localLayers.filter(l => l.enabled).map(layer => (
              <li key={layer.id} className="mb-1">
                <div className="font-medium">{layer.name}</div>
                <div className="ml-2">
                  Regions: {layer.regions?.length || 0}
                  {layer.regions?.map((r, i) => (
                    <div key={i} className="ml-2 text-xs">
                      {r.name || `Region ${i+1}`}: {r.coordinates?.length || 0} points
                    </div>
                  ))}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      {/* Header with title */}
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {camera?.name || 'Camera'} - Layer Configuration
            </h2>
          </div>
        </div>
      </div>
      
      {/* Video container */}
      <div className="relative bg-black w-full h-0 pb-[56.25%] overflow-hidden"> {/* 16:9 aspect ratio */}
        <div className="absolute inset-0">
          {/* StreamViewer component for MJPEG stream display */}
          <div className="relative w-full h-full">
            <StreamViewer
              streamUrl={getCameraStreamUrl(camera)}
              onError={() => toast.error("Stream loading failed")}
              onSuccess={() => console.log("Stream loaded successfully")}
              debugMode={false} /* Changed to false to use real RTSP stream instead of simulation */
            />
          </div>
          
          {/* SVG Overlay for ROI drawing */}
          <div
            ref={videoContainerRef}
          className="absolute inset-0 cursor-crosshair"
            style={{ zIndex: 10 }}
          >
            <svg width="100%" height="100%" style={{ position: "absolute", top: 0, left: 0 }}>
              {/* Display enabled layers' regions of interest */}
              {localLayers
                .filter(layer => {
                  if (!layer.enabled || !layer.regions || layer.regions.length === 0) {
                    return false;
                  }
                  
                  // Check if any region has coordinates in the standardized format
                  const hasRegionWithCoordinates = layer.regions.some(region => {
                    const hasCoords = region.coordinates && region.coordinates.length > 0;
                    return hasCoords;
                  });
                  
                  return hasRegionWithCoordinates;
                })
                .flatMap(layer => {
                  // Check each region and its coordinate format
                  return layer.regions
                    .filter(region => {
                      const hasCoords = region.coordinates && region.coordinates.length > 0;
                      
                      return hasCoords;
                    })
                    .map(region => ({
                      layerId: layer.id,
                      layerColor: layer.color || '#ff0000',
                      region,
                      coordinates: region.coordinates
                    }));
                })
                .map(({ layerId, layerColor, region, coordinates }) => {
                  
                  return (
                    <g key={`${layerId}-${region.id || Math.random()}`}>
                      {renderRegion(
                        coordinates, 
                        layerColor, 
                        false
                      )}
                    </g>
                  );
                })}
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LayersTab;
