import React, { useState, useEffect } from 'react';
import DrawingOverlayWithStream from '../common/DrawingOverlayWithStream';
import { CameraLayerConfiguration, CameraLayerConfigurationDependency, CameraLayerConfigurationRequest, Layer } from '@/types/layer';
import { RegionOfInterest, Coordinate, KeypointType, AlertCategoryType, RequirementType } from '@/types/region';
import { addRegion, updateRegion, deleteRegion, useAllRegions } from '@/services/regions';
import { toast } from 'react-hot-toast';
import { Switch } from '@headlessui/react';
import { useLayers } from '@/services/layers';

interface LayerEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingLayer: CameraLayerConfiguration;
  onSave: (layer: CameraLayerConfigurationRequest) => void;
  onDelete?: (layer: CameraLayerConfiguration) => void;
  isSubmitting?: boolean;
  cameraId: string;
  baseLayers?: Layer[];
  isEditMode?: boolean; // Flag to determine if we're editing an existing layer or creating a new one
}

// Map layer names to their type for easier identification
const LayerNameToTypeMap: Record<string, number> = {
  'Box Classifier': 1,
  'Segmentation': 1,
  'Pose Detection': 1,
  'Object Detection': 1,
  'Trip Wire': 2,
  'Region of Interest': 3,
  'Augmentation': 4,
  'Evaluation': 4
};

const LayerEditModal: React.FC<LayerEditModalProps> = ({
  isOpen,
  onClose,
  editingLayer,
  onSave,
  onDelete,
  isSubmitting = false,
  cameraId,
  baseLayers = [],
}) => {
  // Fetch available regions and layers
  const { regions, mutate: refreshRegionData } = useAllRegions();
  const { isLoading: isLoadingLayers } = useLayers(cameraId);

  // Layer state
  const [layer, setLayer] = useState<CameraLayerConfiguration>(editingLayer);
  const [selectedLayerType, setSelectedLayerType] = useState<Layer | null>(editingLayer.layer || null);
  const [selectedRegions, setSelectedRegions] = useState<RegionOfInterest[]>(editingLayer.regions || []);
  const [configJson, setConfigJson] = useState<string>(JSON.stringify(editingLayer.configuration || {}, null, 2));
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  // Region drawing state
  const [isDrawingRegion, setIsDrawingRegion] = useState(true);
  const [isEditingMode, setIsEditingMode] = useState(false);
  
  // State for ROI creation and editing
  const [regionName, setRegionName] = useState<string>('');
  const [regionDescription, setRegionDescription] = useState<string>('');
  const [regionIsActive, setRegionIsActive] = useState<boolean>(true);
  const [regionAlertCategory, setRegionAlertCategory] = useState<AlertCategoryType | undefined>(undefined);
  const [regionRequirement, setRegionRequirement] = useState<RequirementType | undefined>(undefined);
  const [regionPoseKeypoints, setRegionPoseKeypoints] = useState<KeypointType[]>([]);
  const [drawnCoordinates, setDrawnCoordinates] = useState<Coordinate[]>([]);
  const [isSubmittingROI, setIsSubmittingROI] = useState<boolean>(false);
  const [editingRegionId, setEditingRegionId] = useState<string | null>(null);
  const [isDeletingRegion, setIsDeletingRegion] = useState<boolean>(false);
  const [regionRoiType, setRegionRoiType] = useState<'line' | 'region'>('region');
  
  // Dependencies state
  const [dependencyConditions] = useState<string>(
    editingLayer.dependencies && editingLayer.dependencies.length > 0 
      ? JSON.stringify(editingLayer.dependencies[0].conditions || {}, null, 2)
      : '{}'
  );
  const [selectedDependencies] = useState<string[]>(
    (editingLayer.dependencies || []).map(dep => {
      // Handle the format from the backend which includes just id
      if (dep.id && !dep.dependency_layer) {
        return dep.id;
      }
      // Handle the original format with dependency_layer
      if (typeof dep.dependency_layer === 'string') {
        return dep.dependency_layer;
      }
      if (dep.dependency_layer && dep.dependency_layer.id) {
        return dep.dependency_layer.id;
      }
      // Fallback to the dependency's own ID if available
      if (dep.id) {
        return dep.id;
      }
      console.warn('Unable to extract dependency ID from dependency object', dep);
      return '';
    }).filter(id => id !== '') // Filter out any empty IDs
  );
  
  // Safely parse configuration
  const parseConfig = () => {
    try {
      return JSON.parse(configJson);
    } catch (e) {
      console.log("Error parsing config", e)
      return {};
    }
  };

  // Get layer type (1, 2, or 3) from the selected layer
  const getLayerTypeFromSelected = (): number => {
    if (!selectedLayerType) return 0; // No type selected yet
    return LayerNameToTypeMap[selectedLayerType.name] || 
           (selectedLayerType.layer_type || 0);
  };
  
  // Determine current layer type
  const currentLayerType = getLayerTypeFromSelected();

  // Reset form state when editing layer changes
  useEffect(() => {
    setLayer(editingLayer);
    setSelectedLayerType(editingLayer.layer || null);
    setSelectedRegions(editingLayer.regions || []);
    setConfigJson(JSON.stringify(editingLayer.configuration || {}, null, 2));
    setFormErrors({});
  }, [editingLayer]);
  
  // Set drawing state and region type based on layer type
  useEffect(() => {
    if (currentLayerType === 2 || currentLayerType === 3) {
      setIsDrawingRegion(true);
      // Set ROI type based on layer type (2 = line, 3 = region)
      setRegionRoiType(currentLayerType === 2 ? 'line' : 'region');
    } else {
      setIsDrawingRegion(false);
    }
  }, [currentLayerType]);
  
  // Reset form state on modal close
  useEffect(() => {
    if (!isOpen) {
      setDrawnCoordinates([]);
      setRegionName('');
      setRegionDescription('');
      setRegionIsActive(true);
      setRegionAlertCategory(undefined);
      setRegionRequirement(undefined);
      setRegionPoseKeypoints([]);
      setIsDrawingRegion(false);
      setIsEditingMode(false);
      setEditingRegionId(null);
      setFormErrors({});
      setRegionRoiType('region');
    }
  }, [isOpen]);

  // Handle input changes
  const handleChange = (field: string, value: string) => {
    setLayer(prevLayer => ({
      ...prevLayer,
      [field]: value
    }));
    
    // Clear form errors for this field
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: ''
      });
    }
  };

  // Handle drawing completion
  const handleDrawingComplete = () => {
    setIsEditingMode(false);
  };

  // Handle cancellation of drawing
  const handleCancelDrawing = () => {
    setDrawnCoordinates([]);
    setIsEditingMode(false);
    setEditingRegionId(null);
  };

  // Create a new Region of Interest
  const handleCreateROI = async () => {
    if (!regionName || drawnCoordinates.length === 0) {
      toast.error('Please provide a name and draw the region on the camera feed');
      return;
    }
    
    if (currentLayerType === 2 && drawnCoordinates.length !== 2) {
      toast.error('Trip wire must have exactly 2 points');
      return;
    }
    
    if (currentLayerType === 3 && drawnCoordinates.length < 3) {
      toast.error('Region of interest must have at least 3 points');
      return;
    }
    
    setIsSubmittingROI(true);
    
    try {
      if (editingRegionId) {
        // Update existing region
        const updatedRegion = {
          name: regionName,
          description: regionDescription,
          coordinates: drawnCoordinates,
          is_active: regionIsActive,
          alerts_category: regionAlertCategory,
          requirement: regionRequirement,
          pose_keypoints: regionPoseKeypoints,
          roi_type: regionRoiType
        };
        
        // Update the region using the updateRegion service function
        await updateRegion(editingRegionId, updatedRegion);
        
        // Update selected regions
        setSelectedRegions(prev => prev.map(r => 
          r.id === editingRegionId 
            ? { ...r, name: regionName, coordinates: drawnCoordinates }
            : r
        ));
        
        // Show success message
        toast.success(`${currentLayerType === 2 ? 'Trip Wire' : 'Region'} updated successfully`);
        
        // Reset state
        setRegionName('');
        setRegionDescription('');
        setRegionIsActive(true);
        setRegionAlertCategory(undefined);
        setRegionRequirement(undefined);
        setRegionPoseKeypoints([]);
        setDrawnCoordinates([]);
        setEditingRegionId(null);
        setIsEditingMode(false);
        setRegionRoiType('region');
        
        // Toggle drawing state off and back on to force refresh of the component
        setIsDrawingRegion(false);
        setTimeout(() => setIsDrawingRegion(true), 50);
      } else {
        // Create new region
        const regionData = {
          name: regionName,
          description: regionDescription,
          coordinates: drawnCoordinates,
          is_active: regionIsActive,
          alerts_category: regionAlertCategory,
          requirement: regionRequirement,
          pose_keypoints: regionPoseKeypoints,
          roi_type: regionRoiType
        };
        
        // Create the region using the addRegion service function
        const newRegion = await addRegion(regionData);
        
        // Add to selected regions
        setSelectedRegions([...selectedRegions, newRegion]);
        
        // Show success message
        toast.success(`${newRegion.roi_type === 'line' ? 'Trip Wire' : 'Region'} created successfully`);
        
        // Reset state
        setRegionName('');
        setRegionDescription('');
        setRegionIsActive(true);
        setRegionAlertCategory(undefined);
        setRegionRequirement(undefined);
        setRegionPoseKeypoints([]);
        setDrawnCoordinates([]);
        
        // Refresh regions list
        await refreshRegionData();
      }
      
      // Clear form
      setRegionName('');
      setDrawnCoordinates([]);
      
      // Refresh regions list
      await refreshRegionData();
    } catch (error) {
      console.error('Error with region operation:', error);
      toast.error(`Failed to ${editingRegionId ? 'update' : 'create'} region`);
    } finally {
      setIsSubmittingROI(false);
    }
  };
  
  // Handle editing an existing region
  const handleEditRegion = (region: RegionOfInterest) => {
    if (region.id) {
      setEditingRegionId(region.id);
      setRegionName(region.name);
      setRegionDescription(region.description || '');
      setRegionIsActive(region.is_active !== false);
      setRegionAlertCategory(region.alerts_category);
      setRegionRequirement(region.requirement);
      setRegionPoseKeypoints(region.pose_keypoints || []);
      setDrawnCoordinates(region.coordinates);
      setRegionRoiType(region.roi_type);
      
      // Set drawing state based on region type and ensure drawing is active
      setIsDrawingRegion(true);
      setIsEditingMode(true);
    }
  };
  
  // Handle deleting a region
  const handleDeleteRegion = async (regionId: string) => {
    if (confirm('Are you sure you want to delete this region? This action cannot be undone.')) {
      setIsDeletingRegion(true);
      
      try {
        // Delete the region using the deleteRegion service function
        await deleteRegion(regionId);
        
        // Remove from selected regions
        setSelectedRegions(prev => prev.filter(r => r.id !== regionId));
        
        // Show success message
        toast.success('Region deleted successfully');
        
        // Refresh regions list
        await refreshRegionData();
      } catch (error) {
        console.error('Error deleting region:', error);
        toast.error('Failed to delete region');
      } finally {
        setIsDeletingRegion(false);
      }
    }
  };

  // Validate the form before submission
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    // Name is required
    if (!layer.name) {
      errors.name = 'Name is required';
    }
    
    // Layer type is required
    if (!selectedLayerType) {
      errors.layer_type = 'Layer type is required';
    }
    
    // If it's a region-based layer, at least one region is required
    if ((currentLayerType === 2 || currentLayerType === 3) && selectedRegions.length === 0) {
      errors.regions = 'At least one region is required';
    }
    
    // Validate JSON configuration if this is a type 1 layer
    if (currentLayerType === 1) {
      try {
        JSON.parse(configJson);
      } catch (e) {
        console.log("Error parsing config", e)
        errors.configuration = 'Invalid JSON configuration';
      }
    }
    
    // Set form errors
    setFormErrors(errors);
    
    // Return true if no errors
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) {
      toast.error('Please fix the form errors before saving.');
      return;
    }
    
    // Build the updated layer configuration
    const layerConfig: CameraLayerConfigurationRequest = {
      name: layer.name,
      enabled: layer.enabled,
      color: layer.color,
      // Convert camera to camera_id as expected by the API
      camera_id: typeof layer.camera === 'string' ? layer.camera : layer.camera?.id || '',
      // Use layer_id instead of id for the selected layer type
      layer_id: selectedLayerType?.id || '',
      // Format region_ids as expected by the API - flatten the structure
      region_ids: selectedRegions.filter(r => r.id).map(r => r.id as string),
      configuration: parseConfig(),
      // Format dependencies correctly
      dependencies: layer.dependencies?.map(dep => ({
        dependency_id: typeof dep.dependency_layer === 'string' ? dep.dependency_layer : dep.dependency_layer.id,
        conditions: dep.conditions || {}
      })) || []
    };
    
    // Keep the original layer format for the state update
    const updatedLayer: CameraLayerConfiguration = {
      ...layer,
      name: layer.name,
      enabled: layer.enabled,
      layer: selectedLayerType,
      regions: selectedRegions,
      configuration: parseConfig(),
    };
    
    // Keep track of the updatedLayer for local state
    updatedLayer.dependencies = selectedDependencies.map(id => {
      // Parse dependency conditions
      let conditions: Record<string, unknown> = {};
      try {
        conditions = JSON.parse(dependencyConditions);
      } catch (e) {
        console.error('Error parsing dependency conditions', e);
        conditions = {};
      }
      
      return {
        id,
        dependency_layer: id,
        conditions
      } as CameraLayerConfigurationDependency;
    });
    
    onSave(layerConfig);
    
    // Clear any locally stored drawing points after save
    localStorage.removeItem('drawnPoints');
  };

  // Update layer status
  const handleStatusChange = (enabled: boolean) => {
    setLayer({
      ...layer,
      enabled,
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 sm:p-6 md:p-8">
      <div className={`w-full sm:max-w-6xl md:max-w-7xl lg:max-w-8xl rounded-lg bg-white shadow-xl dark:bg-gray-800 flex flex-col max-h-[85vh] mx-auto`}>
        <div className="absolute top-2 right-2 z-10">
          <button
            type="button"
            onClick={onClose}
            className="rounded-full p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        {/* Header with name and layer type selection - always visible */}
        <div className="p-5 md:p-6 border-b border-gray-200 dark:border-gray-700">
          {/* All elements in a single row */}
          <div className="flex flex-wrap items-center gap-x-4 gap-y-3">
            {/* Title */}
            <h3 className="text-lg font-medium flex-shrink-0 mr-1">
              {layer.id?.startsWith('temp_') ? 'Add Layer' : 'Edit Layer'}
            </h3>
            
            {/* Name input */}
            <div className="flex-grow min-w-[200px]">
              <input
                type="text"
                value={layer.name || ''}
                onChange={(e) => handleChange('name', e.target.value)}
                className={`block w-full rounded-md border ${formErrors.name ? 'border-red-500' : 'border-gray-300'} px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
                placeholder="Input Layer Name"
              />
              {formErrors.name && (
                <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
              )}
            </div>
            
            {/* Layer type dropdown */}
            <div className="flex-grow min-w-[200px]">
              {isLoadingLayers ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">Loading...</p>
              ) : (
                <select
                  value={selectedLayerType?.id || ''}
                  onChange={(e) => {
                    const selected = baseLayers.find(layerType => layerType.id === e.target.value);
                    setSelectedLayerType(selected || null);
                    // Reset drawn coordinates when changing layer type
                    setDrawnCoordinates([]);
                    // Reset region name as well for a cleaner state
                    setRegionName('');
                    if (!layer.id || layer.id.startsWith('temp_')) {
                      setConfigJson('{}');
                    }
                  }}
                  className={`block w-full rounded-md border ${formErrors.layer_type ? 'border-red-500' : 'border-gray-300'} px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm`}
                >
                  <option value="">Select type</option>
                  {baseLayers && baseLayers.length > 0 ? (
                    baseLayers.map(layerType => (
                      <option key={layerType.id} value={layerType.id}>
                        {layerType.name}
                      </option>
                    ))
                  ) : (
                    <option value="" disabled>No layer types available</option>
                  )}
                </select>
              )}
              {formErrors.layer_type && (
                <p className="mt-1 text-sm text-red-600">{formErrors.layer_type}</p>
              )}
            </div>
            
            {/* Enable toggle */}
            <div className="flex items-center space-x-1 flex-shrink-0">
              <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Status:</span>
              <Switch
                checked={layer.enabled}
                onChange={() => handleStatusChange(!layer.enabled)}
                className={`${layer.enabled ? 'bg-green-600' : 'bg-gray-300 dark:bg-gray-700'} relative inline-flex h-6 w-10 flex-shrink-0 cursor-pointer rounded-full border border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-1 focus:ring-primary-500 focus:ring-offset-1`}
              >
                <span
                  aria-hidden="true"
                  className={`${layer.enabled ? 'translate-x-4' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out`}
                />
              </Switch>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {layer.enabled ? 'On' : 'Off'}
              </span>
            </div>

            <div>
              {onDelete && layer.id && !layer.id.startsWith('temp_') && (
                <button
                  type="button"
                  onClick={() => onDelete(layer)}
                  disabled={isSubmitting}
                  className="rounded-md border border-red-300 bg-white px-4 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 dark:border-red-700 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                disabled={isSubmitting || !layer.name || !selectedLayerType}
                className="rounded-md border-0 bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 dark:bg-blue-600 dark:hover:bg-blue-700"
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </button>
            </div>
          </div>
        </div>
        
        {/* Content area - Only shown after name and layer type are selected */}
        <div className="p-5 md:p-6 overflow-y-auto flex-grow">
          {(!layer.name || !selectedLayerType) ? (
            <div className="text-center p-6">
              <p className="text-gray-500 dark:text-gray-400">
                Please provide a name and select a layer type to continue.
              </p>
            </div>
          ) : (
            <div>
              {/* TYPE 1: Configuration JSON (Detection Layers) */}
              {currentLayerType === 1 && (
                <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                  <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="text-md font-medium">Detection Configuration</h3>
                  </div>
                  
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    JSON Configuration
                  </label>
                  <textarea
                    rows={10}
                    value={configJson}
                    onChange={(e) => {
                      setConfigJson(e.target.value);
                      try {
                        JSON.parse(e.target.value);
                        // Clear JSON error if parsing succeeds
                        if (formErrors.configuration) {
                          setFormErrors({
                            ...formErrors,
                            configuration: ''
                          });
                        }
                      } catch (e) {
                        // Don't set form error yet, wait until save
                        console.log("Error parsing config", e)
                      }
                    }}
                    className={`block w-full rounded-md border ${formErrors.configuration ? 'border-red-500' : 'border-gray-300'} px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white font-mono text-sm`}
                    placeholder="{}"
                  />
                  {formErrors.configuration && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.configuration}</p>
                  )}
                </div>
              )}
              
              {/* TYPE 2: Trip Wire Configuration - Two column layout */}
              {currentLayerType === 2 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Left column - Camera feed */}
                  <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                      <h3 className="text-md font-medium">Trip Wire Drawing</h3>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {isDrawingRegion ? 'Click to add 2 points for a line' : 'Ready for drawing'}
                      </div>
                    </div>
                    {isDrawingRegion && (
                      <DrawingOverlayWithStream
                        cameraId={cameraId}
                        onComplete={(coordinates) => {
                          setDrawnCoordinates(coordinates);
                          handleDrawingComplete();
                        }}
                        onCancel={handleCancelDrawing}
                        onPointsUpdate={setDrawnCoordinates}
                        initialColor={layer.color} 
                        regionType="line"
                        selectedRegions={selectedRegions.filter(r => r.roi_type === 'line' && (!editingRegionId || r.id !== editingRegionId))}
                        initialPoints={isEditingMode ? drawnCoordinates : []}
                      />
                    )}
                  </div>
                  
                  {/* Right column - ROI Creator and Selector */}
                  <div className="flex flex-col space-y-6">
                    {/* ROI Instance Creator */}
                    <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-md font-medium">Create Trip Wire</h3>
                      </div>
                      
                      <div className="space-y-4">
                        {/* ROI Name */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Trip Wire Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={regionName}
                            onChange={(e) => setRegionName(e.target.value)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                            placeholder="Enter a name for this trip wire"
                          />
                        </div>
                        
                        {/* ROI Description */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                          </label>
                          <textarea
                            value={regionDescription}
                            onChange={(e) => setRegionDescription(e.target.value)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                            placeholder="Describe the purpose of this trip wire"
                            rows={2}
                          />
                        </div>
                        
                        {/* ROI Active Status */}
                        <div>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="tripWireIsActive"
                              checked={regionIsActive}
                              onChange={(e) => setRegionIsActive(e.target.checked)}
                              className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                            <label htmlFor="tripWireIsActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                              Active
                            </label>
                          </div>
                        </div>
                        
                        {/* ROI Alert Category */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Alert Category
                          </label>
                          <select
                            value={regionAlertCategory || ''}
                            onChange={(e) => setRegionAlertCategory(e.target.value as AlertCategoryType || undefined)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                          >
                            <option value="">Select category</option>
                            <option value="Warning">Warning</option>
                            <option value="Critical">Critical</option>
                          </select>
                        </div>
                        
                        {/* ROI Requirement */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Requirement
                          </label>
                          <select
                            value={regionRequirement || ''}
                            onChange={(e) => setRegionRequirement(e.target.value as RequirementType || undefined)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                          >
                            <option value="">Select requirement</option>
                            <option value="above the line">Above the line</option>
                            <option value="below the line">Below the line</option>
                          </select>
                        </div>
                        
                        {/* ROI Pose Keypoints */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Pose Keypoints
                          </label>
                          <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-2">
                            {[
                              'Nose', 'Left Eye', 'Right Eye', 'Left Ear', 'Right Ear',
                              'Left Shoulder', 'Right Shoulder', 'Left Elbow', 'Right Elbow',
                              'Left Wrist', 'Right Wrist', 'Left Hip', 'Right Hip',
                              'Left Knee', 'Right Knee', 'Left Ankle', 'Right Ankle'
                            ].map(keypoint => (
                              <div key={keypoint} className="flex items-center mb-1">
                                <input
                                  type="checkbox"
                                  id={`keypoint-line-${keypoint}`}
                                  checked={regionPoseKeypoints.includes(keypoint as KeypointType)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setRegionPoseKeypoints([...regionPoseKeypoints, keypoint as KeypointType]);
                                    } else {
                                      setRegionPoseKeypoints(regionPoseKeypoints.filter(k => k !== keypoint));
                                    }
                                  }}
                                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                />
                                <label htmlFor={`keypoint-line-${keypoint}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                  {keypoint}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {/* Coordinates Display */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Coordinates
                          </label>
                          <div className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm">
                            {drawnCoordinates.length > 0 ? (
                              drawnCoordinates.map((coordinate, idx) => (
                                <div key={idx} className="mb-2">
                                  <div className="font-medium">Point {idx + 1}:</div>
                                  <div className="text-xs grid grid-cols-2 gap-1">
                                    <div>Pixels: [{Math.round(coordinate.x)}, {Math.round(coordinate.y)}]</div>
                                    <div>Normalized: [{(coordinate.normalizedX !== undefined ? coordinate.normalizedX * 100 : 0).toFixed(1)}%, {(coordinate.normalizedY !== undefined ? coordinate.normalizedY * 100 : 0).toFixed(1)}%]</div>
                                  </div>
                                </div>
                              ))
                            ) : (
                              'No points drawn'
                            )}
                          </div>
                        </div>
                        
                        {/* Create ROI Button */}
                        <button
                          type="button"
                          onClick={handleCreateROI}
                          disabled={!regionName || drawnCoordinates.length !== 2 || isSubmittingROI}
                          className="w-full mt-2 inline-flex justify-center items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-300 disabled:cursor-not-allowed"
                        >
                          {isSubmittingROI ? (editingRegionId ? 'Updating...' : 'Creating...') : (editingRegionId ? 'Update Trip Wire' : 'Create Trip Wire')}
                        </button>
                      </div>
                    </div>
                    
                    {/* ROI Instance Selector */}
                    <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-md font-medium">Select Trip Wire</h3>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Trip Wires <span className="text-red-500">*</span>
                          </label>
                          <div className={`p-3 rounded-md border ${formErrors.regions ? 'border-red-500' : 'border-gray-300'} shadow-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white`}>
                            <div className="max-h-48 overflow-y-auto space-y-2">
                              {regions.filter(r => r.roi_type === 'line').length > 0 ? (
                                regions.filter(r => r.roi_type === 'line').map(region => (
                                  <div key={region.id} className="flex items-center justify-between w-full">
                                    <div className="flex items-center">
                                      <input
                                        id={`trip-wire-${region.id}`}
                                        type="checkbox"
                                        checked={selectedRegions.some(r => r.id === region.id)}
                                        onChange={(e) => {
                                          if (e.target.checked) {
                                            // Add to selected regions
                                            const updatedRegions = [...selectedRegions, region];
                                            setSelectedRegions(updatedRegions);
                                          } else {
                                            // Remove from selected regions
                                            const updatedRegions = selectedRegions.filter(r => r.id !== region.id);
                                            setSelectedRegions(updatedRegions);
                                          }
                                        }}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                                      />
                                      <label
                                        htmlFor={`trip-wire-${region.id}`}
                                        className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                                      >
                                        {region.name || 'Unnamed Trip Wire'}
                                      </label>
                                    </div>
                                    <div className="flex space-x-2">
                                      <button
                                        type="button"
                                        onClick={() => handleEditRegion(region)}
                                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                        title="Edit Trip Wire"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                        </svg>
                                      </button>
                                      <button
                                        type="button"
                                        onClick={() => region.id && handleDeleteRegion(region.id)}
                                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                        title="Delete Trip Wire"
                                        disabled={isDeletingRegion}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                      </button>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <p className="text-sm text-gray-500 dark:text-gray-400">No trip wires available. Create one first.</p>
                              )}
                            </div>
                          </div>
                          {formErrors.regions && (
                            <p className="mt-1 text-sm text-red-600">{formErrors.regions}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* TYPE 3: Region of Interest Configuration - Two column layout */}
              {currentLayerType === 3 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Left column - Camera feed */}
                  <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                      <h3 className="text-md font-medium">Area Drawing</h3>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {isDrawingRegion ? 'Click to add at least 3 points for an area' : 'Ready for drawing'}
                      </div>
                    </div>
                    {isDrawingRegion && (
                      <DrawingOverlayWithStream
                        cameraId={cameraId}
                        onComplete={(coordinates) => {
                          setDrawnCoordinates(coordinates);
                          handleDrawingComplete();
                        }}
                        onCancel={handleCancelDrawing}
                        onPointsUpdate={setDrawnCoordinates}
                        initialColor={layer.color}
                        regionType="region"
                        selectedRegions={selectedRegions.filter(r => r.roi_type === 'region' && (!editingRegionId || r.id !== editingRegionId))}
                        initialPoints={isEditingMode ? drawnCoordinates : []}
                      />
                    )}
                  </div>
                  
                  {/* Right column - ROI Creator and Selector */}
                  <div className="flex flex-col space-y-6">
                    {/* ROI Instance Creator */}
                    <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-md font-medium">Create Region of Interest</h3>
                      </div>
                      
                      <div className="space-y-4">
                        {/* ROI Name */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Region Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={regionName}
                            onChange={(e) => setRegionName(e.target.value)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                            placeholder="Enter a name for this region"
                          />
                        </div>
                        
                        {/* ROI Description */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                          </label>
                          <textarea
                            value={regionDescription}
                            onChange={(e) => setRegionDescription(e.target.value)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                            placeholder="Describe the purpose of this region"
                            rows={2}
                          />
                        </div>
                        
                        {/* ROI Active Status */}
                        <div>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="regionIsActive"
                              checked={regionIsActive}
                              onChange={(e) => setRegionIsActive(e.target.checked)}
                              className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                            <label htmlFor="regionIsActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                              Active
                            </label>
                          </div>
                        </div>
                        
                        {/* ROI Alert Category */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Alert Category
                          </label>
                          <select
                            value={regionAlertCategory || ''}
                            onChange={(e) => setRegionAlertCategory(e.target.value as AlertCategoryType || undefined)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                          >
                            <option value="">Select category</option>
                            <option value="Warning">Warning</option>
                            <option value="Critical">Critical</option>
                          </select>
                        </div>
                        
                        {/* ROI Requirement */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Requirement
                          </label>
                          <select
                            value={regionRequirement || ''}
                            onChange={(e) => setRegionRequirement(e.target.value as RequirementType || undefined)}
                            className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                          >
                            <option value="">Select requirement</option>
                            {drawnCoordinates.length === 2 ? (
                              <>
                                <option value="above the line">Above the line</option>
                                <option value="below the line">Below the line</option>
                              </>
                            ) : (
                              <>
                                <option value="inside the region">Inside the region</option>
                                <option value="outside the region">Outside the region</option>
                              </>
                            )}
                          </select>
                        </div>
                        
                        {/* ROI Pose Keypoints */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Pose Keypoints
                          </label>
                          <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-2">
                            {[
                              'Nose', 'Left Eye', 'Right Eye', 'Left Ear', 'Right Ear',
                              'Left Shoulder', 'Right Shoulder', 'Left Elbow', 'Right Elbow',
                              'Left Wrist', 'Right Wrist', 'Left Hip', 'Right Hip',
                              'Left Knee', 'Right Knee', 'Left Ankle', 'Right Ankle'
                            ].map(keypoint => (
                              <div key={keypoint} className="flex items-center mb-1">
                                <input
                                  type="checkbox"
                                  id={`keypoint-${keypoint}`}
                                  checked={regionPoseKeypoints.includes(keypoint as KeypointType)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setRegionPoseKeypoints([...regionPoseKeypoints, keypoint as KeypointType]);
                                    } else {
                                      setRegionPoseKeypoints(regionPoseKeypoints.filter(k => k !== keypoint));
                                    }
                                  }}
                                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                                />
                                <label htmlFor={`keypoint-${keypoint}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                  {keypoint}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {/* Coordinates Display */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Coordinates
                          </label>
                          <div className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm">
                            {drawnCoordinates.length > 0 ? (
                              drawnCoordinates.map((coordinate, idx) => (
                                <div key={idx} className="mb-2">
                                  <div className="font-medium">Point {idx + 1}:</div>
                                  <div className="text-xs grid grid-cols-2 gap-1">
                                    <div>Pixels: [{Math.round(coordinate.x)}, {Math.round(coordinate.y)}]</div>
                                    <div>Normalized: [{(coordinate.normalizedX !== undefined ? coordinate.normalizedX * 100 : 0).toFixed(1)}%, {(coordinate.normalizedY !== undefined ? coordinate.normalizedY * 100 : 0).toFixed(1)}%]</div>
                                  </div>
                                </div>
                              ))
                            ) : (
                              'No points drawn'
                            )}
                          </div>
                        </div>
                        
                        {/* Create ROI Button */}
                        <button
                          type="button"
                          onClick={handleCreateROI}
                          disabled={!regionName || drawnCoordinates.length < 3 || isSubmittingROI}
                          className="w-full mt-2 inline-flex justify-center items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-300 disabled:cursor-not-allowed"
                        >
                          {isSubmittingROI ? (editingRegionId ? 'Updating...' : 'Creating...') : (editingRegionId ? 'Update Region' : 'Create Region')}
                        </button>
                      </div>
                    </div>
                    
                    {/* ROI Instance Selector */}
                    <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                        <h3 className="text-md font-medium">Select Region of Interest</h3>
                      </div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Regions <span className="text-red-500">*</span>
                      </label>
                      <div className={`p-3 rounded-md border ${formErrors.regions ? 'border-red-500' : 'border-gray-300'} shadow-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white`}>
                        <div className="max-h-48 overflow-y-auto space-y-2">
                          {regions.filter(r => r.roi_type === 'region').length > 0 ? (
                            regions.filter(r => r.roi_type === 'region').map(region => (
                              <div key={region.id} className="flex items-center justify-between w-full">
                                <div className="flex items-center">
                                  <input
                                    id={`region-${region.id}`}
                                    type="checkbox"
                                    checked={selectedRegions.some(r => r.id === region.id)}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        // Add to selected regions
                                        setSelectedRegions([...selectedRegions, region]);
                                      } else {
                                        // Remove from selected regions
                                        setSelectedRegions(selectedRegions.filter(r => r.id !== region.id));
                                      }
                                    }}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                                  />
                                  <label
                                    htmlFor={`region-${region.id}`}
                                    className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                                  >
                                    {region.name || 'Unnamed Region'}
                                  </label>
                                </div>
                                <div className="flex space-x-2">
                                  <button
                                    type="button"
                                    onClick={() => handleEditRegion(region)}
                                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                    title="Edit Region"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                    </svg>
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() => region.id && handleDeleteRegion(region.id)}
                                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                    title="Delete Region"
                                    disabled={isDeletingRegion}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-gray-500 dark:text-gray-400">No regions available. Create one first.</p>
                          )}
                        </div>
                      </div>
                      {formErrors.regions && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.regions}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* TYPE 4: Evaluation Layer - Using Detection Configuration */}
              {(currentLayerType === 4 || currentLayerType === 5) && (
                <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                  <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="text-md font-medium">Detection Configuration</h3>
                  </div>
                  
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    JSON Configuration
                  </label>
                  <textarea
                    rows={10}
                    value={configJson}
                    onChange={(e) => {
                      setConfigJson(e.target.value);
                      try {
                        JSON.parse(e.target.value);
                        // Clear JSON error if parsing succeeds
                        if (formErrors.configuration) {
                          setFormErrors({
                            ...formErrors,
                            configuration: ''
                          });
                        }
                      } catch (e) {
                        // Don't set form error yet, wait until save
                        console.log("Error parsing config", e)
                      }
                    }}
                    className={`block w-full rounded-md border ${formErrors.configuration ? 'border-red-500' : 'border-gray-300'} px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white font-mono text-sm`}
                    placeholder="{}"
                  />
                  {formErrors.configuration && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.configuration}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LayerEditModal;
