"use client";

import React, { useState, useEffect, useRef } from 'react';
import { CameraComponentProps } from '@/types/camera';
import { RegionOfInterest } from '@/types/region';
import { useRegionsByCamera, addRegion, updateRegion, deleteRegion } from '@/services/regions';
import { toast } from 'react-hot-toast';
import { Coordinate } from '@/types/region';

// Create simplified icons
const PlusIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
  </svg>
);

const EditIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
  </svg>
);

const TrashIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
  </svg>
);

interface RegionsPanelProps extends CameraComponentProps {
  videoContainerRef?: React.RefObject<HTMLDivElement | null>;
  onRegionSelected?: (region: RegionOfInterest) => void;
}

const RegionsPanel: React.FC<RegionsPanelProps> = ({ 
  cameraId,
  videoContainerRef,
  onRegionSelected 
}) => {
  // Regions state
  const { regions, isLoading, isError, mutate: refetchRegions } = useRegionsByCamera(cameraId);
  const [localRegions, setLocalRegions] = useState<RegionOfInterest[]>([]);
  
  // Drawing state
  const [isDrawing, setIsDrawing] = useState(false);
  const [points, setPoints] = useState<Coordinate[]>([]);
  const [showRegionDialog, setShowRegionDialog] = useState(false);
  const [editingRegion, setEditingRegion] = useState<RegionOfInterest | null>(null);
  const [deletingRegion, setDeletingRegion] = useState<RegionOfInterest | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  
  // Process backend regions to frontend format
  useEffect(() => {
    if (regions?.length > 0) {
      const currentRegionIds = new Set(localRegions.map(r => r.id || ''));
      const backendRegionIds = new Set(regions.map(r => r.id || ''));
      
      // Check if we need to update by comparing IDs
      const needsUpdate = 
        localRegions.length !== regions.length ||
        regions.some(region => !currentRegionIds.has(region.id || '')) ||
        localRegions.some(region => !backendRegionIds.has(region.id || ''));
      
      if (needsUpdate) {
        setLocalRegions(regions);
      }
    } else if (regions?.length === 0 && localRegions.length > 0) {
      // Clear if backend returned empty array
      setLocalRegions([]);
    } else if (isError && localRegions.length === 0) {
      // Handle error case - only if we don't already have regions
      console.error("Error loading regions:", isError);
      setLocalRegions([]);
    }
  }, [regions, isLoading, isError, localRegions]);
  
  // Start drawing a new region
  const handleStartDrawing = () => {
    setIsDrawing(true);
    setPoints([]);
  };
  
  // Handle canvas click when drawing
  const handleCanvasClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!isDrawing || !svgRef.current) return;
    
    // Get click position relative to the SVG
    const svg = svgRef.current;
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate normalized coordinates (0-1 range)
    const normalizedX = x / rect.width;
    const normalizedY = y / rect.height;
    
    // Add point to the current path with normalized coordinates
    setPoints([...points, { 
      x, 
      y, 
      normalizedX, 
      normalizedY 
    }]);
  };
  
  // Save the drawn region
  const handleSaveRegion = () => {
    if (points.length < 3) {
      toast.error("A region must have at least 3 points");
      return;
    }
    
    // Create a temporary region
    const tempRegion: RegionOfInterest = {
      id: `temp_${Date.now()}`,
      name: 'New Region',
      coordinates: points,
      roi_type: 'region'
    };
    
    // Open edit dialog with the new region
    setEditingRegion(tempRegion);
    setShowRegionDialog(true);
    setIsDrawing(false);
  };
  
  // Cancel drawing
  const handleCancelDrawing = () => {
    setIsDrawing(false);
    setPoints([]);
  };
  
  // Save region (create or update)
  const handleSaveRegionDialog = async (region: RegionOfInterest) => {
    try {
      let savedRegion: RegionOfInterest;
      
      if (region.id?.startsWith('temp_')) {
        // This is a new region
        savedRegion = await addRegion({
          name: region.name,
          coordinates: region.coordinates,
          roi_type: region.roi_type || 'region'
        });
        
        // Add the new region to local state
        setLocalRegions([...localRegions, savedRegion]);
      } else if (region.id) {
        // This is an existing region
        savedRegion = await updateRegion(region.id, {
          name: region.name,
          coordinates: region.coordinates,
          roi_type: region.roi_type
        });
        
        // Update the region in local state
        setLocalRegions(localRegions.map(r => 
          r.id === region.id ? savedRegion : r
        ));
      }
      
      // Close dialog and refresh
      setShowRegionDialog(false);
      refetchRegions();
      toast.success(`Region ${region.id?.startsWith('temp_') ? 'created' : 'updated'} successfully`);
    } catch (error) {
      console.error('Error saving region:', error);
      toast.error(`Failed to save region: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  
  // Delete a region
  const handleDeleteRegion = async (region: RegionOfInterest) => {
    if (!region || !region.id) return;
    
    try {
      if (!region.id.startsWith('temp_')) {
        // Only call API for non-temporary regions
        await deleteRegion(region.id);
      }
      
      // Remove from local state
      setLocalRegions(localRegions.filter(r => r.id !== region.id));
      setDeletingRegion(null);
      refetchRegions();
      toast.success(`${region.name} region deleted successfully`);
    } catch (error) {
      console.error('Error deleting region:', error);
      toast.error(`Failed to delete region: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Regions of Interest</h3>
        
        <div className="flex items-center">
          <span className={`mr-2 inline-block h-2 w-2 rounded-full ${
            isLoading 
              ? "bg-yellow-400" 
              : !isError && regions && regions.length > 0 
                ? "bg-green-500" 
                : "bg-red-500"
          }`}></span>
          <span className="text-sm text-gray-500">
            {isLoading 
              ? "Loading regions..." 
              : !isError && regions && regions.length > 0 
                ? "Connected to server" 
                : isError 
                  ? "Failed to reach server" 
                  : "No regions found"}
          </span>
        </div>
      </div>
      
      {/* Drawing overlay - would be positioned over the video */}
      {isDrawing && videoContainerRef?.current && (
        <div className="absolute inset-0">
          <svg
            ref={svgRef}
            className="absolute inset-0 z-10 h-full w-full cursor-crosshair"
            onClick={handleCanvasClick}
          >
            {/* Display current points */}
            {points.map((point, index) => (
              <circle
                key={index}
                cx={point.x}
                cy={point.y}
                r={5}
                fill="red"
              />
            ))}
            
            {/* Connect points with lines */}
            {points.length > 1 && (
              <polyline
                points={points.map(pt => `${pt.x},${pt.y}`).join(' ')}
                fill="none"
                stroke="red"
                strokeWidth={2}
              />
            )}
            
            {/* Close the polygon if we have at least 3 points */}
            {points.length >= 3 && (
              <polyline
                points={`${points[points.length - 1].x},${points[points.length - 1].y} ${points[0].x},${points[0].y}`}
                fill="none"
                stroke="red"
                strokeDasharray="5,5"
                strokeWidth={2}
              />
            )}
          </svg>
          
          {/* Drawing controls */}
          <div className="absolute bottom-4 left-0 right-0 flex justify-center">
            <div className="rounded-lg bg-black bg-opacity-70 p-2">
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleCancelDrawing}
                  className="rounded-md bg-gray-600 px-3 py-1 text-white hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveRegion}
                  className="rounded-md bg-primary-600 px-3 py-1 text-white hover:bg-primary-500"
                  disabled={points.length < 3}
                >
                  Save {points.length >= 3 ? 'Region' : 'Line'}
                </button>
                <button
                  onClick={() => setPoints(points.slice(0, -1))}
                  className="rounded-md bg-gray-600 px-3 py-1 text-white hover:bg-gray-500"
                  disabled={points.length === 0}
                >
                  Undo Point
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* List of regions */}
      <div className="space-y-2">
        {localRegions.map(region => (
          <div 
            key={region.id}
            className="group relative mb-2 cursor-pointer rounded-md border border-gray-200 bg-white p-3 shadow-sm transition-all hover:border-primary-400 dark:border-gray-700 dark:bg-gray-800 dark:hover:border-primary-600"
            onClick={() => onRegionSelected && onRegionSelected(region)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {/* Region icon */}
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200">
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2}
                      d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                    />
                  </svg>
                </div>
                
                {/* Region name */}
                <div className="flex flex-col">
                  <span className="font-medium">{region.name}</span>
                  <span className="text-xs text-gray-500">
                    {region.coordinates?.length || 0} points
                  </span>
                </div>
              </div>
              
              {/* Region actions */}
              <div className="flex items-center space-x-1" onClick={e => e.stopPropagation()}>
                <EditIcon
                  onClick={() => {
                    setEditingRegion({ ...region });
                    setShowRegionDialog(true);
                  }}
                  className="h-4 w-4 cursor-pointer text-gray-500 hover:text-gray-700"
                />
                <TrashIcon
                  onClick={() => setDeletingRegion(region)}
                  className="h-4 w-4 cursor-pointer text-gray-500 hover:text-red-500"
                />
              </div>
            </div>
          </div>
        ))}
        
        {/* Add region button */}
        <button
          onClick={handleStartDrawing}
          className="flex w-full items-center justify-center space-x-1 rounded-md border border-dashed border-gray-300 p-3 text-gray-500 hover:border-primary-400 hover:text-primary-500 dark:border-gray-700 dark:hover:border-primary-600"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Region of Interest</span>
        </button>
      </div>
      
      {/* Region edit dialog - would be a modal in real implementation */}
      {showRegionDialog && editingRegion && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-medium">
              {editingRegion.id?.startsWith('temp_') ? 'Add New Region' : 'Edit Region'}
            </h3>
            
            {/* Form would go here - simplified for this example */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium">Name</label>
                <input
                  type="text"
                  value={editingRegion.name}
                  onChange={e => setEditingRegion({...editingRegion, name: e.target.value})}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
              
              {/* More form fields would go here */}
              
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowRegionDialog(false)}
                  className="rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSaveRegionDialog(editingRegion)}
                  className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Delete confirmation dialog */}
      {deletingRegion && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-medium">Delete Region</h3>
            <p>Are you sure you want to delete <strong>{deletingRegion.name}</strong>?</p>
            <p className="mt-2 text-sm text-gray-500">This action cannot be undone.</p>
            
            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => setDeletingRegion(null)}
                className="rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteRegion(deletingRegion)}
                className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegionsPanel;
