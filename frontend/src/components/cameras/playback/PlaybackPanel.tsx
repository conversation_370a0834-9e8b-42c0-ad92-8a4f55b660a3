"use client";

import React, { useEffect, useRef, useState } from 'react';
import { CameraComponentProps } from '@/types/camera';
import StreamViewer from '@/components/cameras/common/StreamViewer';

interface CombinedPlaybackPanelProps extends CameraComponentProps {
  /** Timestamp to play back (applied) */
  selectedDate: Date;
}

export default function CombinedPlaybackPanel({
  cameraId,
  camera,
  selectedDate,
}: CombinedPlaybackPanelProps) {
  const [playbackUrl, setPlaybackUrl] = useState<string>("");
  // Removed RTSP fallback option as we'll always use direct NVR connection for playback
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cameraId && selectedDate) {
      // Format date to preserve the exact time entered without timezone conversion
      // Don't use toISOString() as that converts to UTC
      const year = selectedDate.getFullYear();
      const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
      const day = String(selectedDate.getDate()).padStart(2, '0');
      const hours = String(selectedDate.getHours()).padStart(2, '0');
      const minutes = String(selectedDate.getMinutes()).padStart(2, '0');
      const seconds = String(selectedDate.getSeconds()).padStart(2, '0');
      
      // Format as YYYY-MM-DD HH:MM:SS to preserve the exact time without timezone conversion
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      
      // Create playback URL with correct parameters
      const baseUrl = `/api/cameras/${cameraId}/stream/playback/`;
      
      // Build parameters object for clean URL construction
      const params = new URLSearchParams();
      params.append('start_time', formattedDate);
      
      // Add cache buster to prevent caching
      params.append('cache_buster', Date.now().toString());
      
      // Construct the final URL
      const fullUrl = `${baseUrl}?${params.toString()}`;
      setPlaybackUrl(fullUrl);
      
      // Log the full URL for debugging
      console.log('Playback stream URL:', fullUrl);
      console.log('Connection mode: Direct NVR');
      console.log('Requesting playback for time:', formattedDate);
    }
  }, [cameraId, selectedDate]);

  return (
    <div className="space-y-4">
      <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700 p-4 flex justify-between items-center bg-slate-50 dark:bg-gray-750">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
            <span>Playback from</span>
            <span className="inline-flex items-center mx-2 px-3 py-1 bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-100 rounded-md font-mono font-bold">
              {selectedDate.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}
            </span>
            <span>at</span>
            <span className="inline-flex items-center mx-2 px-3 py-1 bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-200 rounded-md font-mono font-bold">
              {selectedDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false })}
            </span>
          </h2>
        </div>
        <div ref={previewRef} className="aspect-video bg-black">
          {playbackUrl && (
            <StreamViewer
              streamUrl={playbackUrl}
              className="w-full h-full object-contain"
              debugMode={true} /* Enable debug mode to help troubleshoot */
              isPlayback={true}
              playbackTime={selectedDate}
            />
          )}
        </div>
      </div>
    </div>
  );
}
