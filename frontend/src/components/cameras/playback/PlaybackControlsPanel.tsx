"use client";

import React, { useState, useEffect } from 'react';
import { Camera } from '@/types/camera';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';

interface PlaybackControlsPanelProps {
  cameraId: string;
  camera: Camera | null;
  /** Currently applied playback timestamp */
  selectedDate: Date;
  /** Called to update the applied date in parent */
  onDateChange: (date: Date) => void;
  /** Called to trigger playback fetch in parent */
  onApplyFilters: () => void;
}

const PlaybackControlsPanel: React.FC<PlaybackControlsPanelProps> = ({
  camera,
  selectedDate,
  onDateChange,
  onApplyFilters,
}) => {
  // Track date and time locally before applying
  const [localDate, setLocalDate] = useState<Date>(selectedDate);

  // Keep local date in sync with parent date
  useEffect(() => {
    setLocalDate(selectedDate);
  }, [selectedDate]);

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const days: React.ReactNode[] = [];
    const year = localDate.getFullYear();
    const month = localDate.getMonth();
    const firstDay = new Date(year, month, 1).getDay();
    const lastDate = new Date(year, month + 1, 0).getDate();
    const today = new Date();

    // Empty slots before first day
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`blank-${i}`} className="h-7" />);
    }

    for (let d = 1; d <= lastDate; d++) {
      const isToday =
        d === today.getDate() &&
        month === today.getMonth() &&
        year === today.getFullYear();
      const isSelected = d === localDate.getDate();

      days.push(
        <button
          key={d}
          onClick={() => setLocalDate(new Date(year, month, d, localDate.getHours(), localDate.getMinutes()))}
          className={cn(
            'h-9 w-9 rounded-md text-xs flex items-center justify-center transition-all duration-150',
            isToday && !isSelected && 'font-bold border-2 border-primary-300 dark:border-primary-600',
            isSelected
              ? 'bg-primary-600 text-white font-bold shadow-lg ring-4 ring-primary-300 dark:ring-primary-800 scale-125 transform border-2 border-white dark:border-gray-800'
              : 'text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border-0'
          )}
        >
          {d}
        </button>
      );
    }

    return days;
  };

  // Month navigation
  const changeMonth = (offset: number) => {
    const dt = new Date(localDate);
    dt.setMonth(dt.getMonth() + offset);
    setLocalDate(dt);
  };

  // Time component change handlers
  const handleTimeComponentChange = (component: 'hours' | 'minutes' | 'seconds', value: number) => {
    if (isNaN(value)) return;
    
    const dt = new Date(localDate);
    switch (component) {
      case 'hours':
        if (value >= 0 && value < 24) {
          dt.setHours(value, dt.getMinutes(), dt.getSeconds());
        }
        break;
      case 'minutes':
        if (value >= 0 && value < 60) {
          dt.setMinutes(value, dt.getSeconds());
        }
        break;
      case 'seconds':
        if (value >= 0 && value < 60) {
          dt.setSeconds(value);
        }
        break;
    }
    setLocalDate(dt);
  };

  // Apply selected date/time and trigger fetch
  const handleRetrieve = () => {
    // Make sure we pass the full date with time to parent
    onDateChange(localDate);
    onApplyFilters();
  };


  return (
    <div className="space-y-4">
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-3 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Playback Calendar</h2>
          <div className="flex space-x-1">
            <Button variant="ghost" size="icon" onClick={() => changeMonth(-1)} className="h-8 w-8">
              <ChevronLeftIcon className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={() => changeMonth(1)} className="h-8 w-8">
              <ChevronRightIcon className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <p className="mb-2 text-base font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 py-1 px-2 rounded border border-gray-200 dark:border-gray-600 shadow-sm inline-block">
          {localDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </p>

        <div className="rounded-lg border border-gray-300 p-2 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="mb-2 grid grid-cols-7 gap-1 text-center">
            {['Sun','Mon','Tue','Wed','Thu','Fri','Sat'].map(d => (
              <div key={d} className="text-xs font-medium text-gray-500 dark:text-gray-400">{d}</div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1 text-center">
            {generateCalendarDays()}
          </div>
        </div>

        <div className="mt-4 flex items-end gap-4">
          <div className="flex-grow">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">Select Time</label>
            <div className="flex items-center">
              <div className="flex items-center">
                {/* Hours */}
                <div className="relative">
                  <Input 
                    type="number" 
                    min={0} 
                    max={23} 
                    value={String(localDate.getHours()).padStart(2, '0')} 
                    onChange={(e) => handleTimeComponentChange('hours', parseInt(e.target.value, 10))}
                    className="h-10 w-16 text-center font-mono text-lg"
                  />
                </div>
                
                <span className="mx-1 text-lg font-bold text-gray-600 dark:text-gray-300 px-1">:</span>
                
                {/* Minutes */}
                <div className="relative">
                  <Input 
                    type="number" 
                    min={0} 
                    max={59} 
                    value={String(localDate.getMinutes()).padStart(2, '0')} 
                    onChange={(e) => handleTimeComponentChange('minutes', parseInt(e.target.value, 10))}
                    className="h-10 w-16 text-center font-mono text-lg"
                  />
                </div>
                
                <span className="mx-1 text-lg font-bold text-gray-600 dark:text-gray-300 px-1">:</span>
                
                {/* Seconds */}
                <div className="relative">
                  <Input 
                    type="number" 
                    min={0} 
                    max={59} 
                    value={String(localDate.getSeconds()).padStart(2, '0')} 
                    onChange={(e) => handleTimeComponentChange('seconds', parseInt(e.target.value, 10))}
                    className="h-10 w-16 text-center font-mono text-lg"
                  />
                </div>
              </div>
            </div>
          </div>
          <Button 
            onClick={handleRetrieve} 
            className="shrink-0 bg-primary-500 hover:bg-primary-600 text-white" 
            variant="default"
          >
            Play
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PlaybackControlsPanel;
