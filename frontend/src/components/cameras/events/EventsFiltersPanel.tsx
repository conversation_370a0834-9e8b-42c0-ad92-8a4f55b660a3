"use client";

import React from 'react';
import { Camera } from '@/types/camera';
import { RefreshIcon } from '@/components/icons/CameraIcons';

interface EventsFiltersPanelProps {
  cameraId: string;
  camera: Camera | null;
  filters: {
    motion: boolean;
    person: boolean;
    object: boolean;
  };
  onFilterChange: (filterName: string, value: boolean) => void;
  onApplyFilters: () => void;
  eventCounts: {
    motion: number;
    person: number;
    object: number;
  };
}

const EventsFiltersPanel: React.FC<EventsFiltersPanelProps> = ({
  filters,
  onFilterChange,
  onApplyFilters,
  eventCounts
}) => {
  // Handle filter changes
  const handleFilterChange = (filterName: 'motion' | 'person' | 'object') => {
    onFilterChange(filterName, !filters[filterName]);
  };

  // Reset all filters to true
  const resetFilters = () => {
    onFilterChange('motion', true);
    onFilterChange('person', true);
    onFilterChange('object', true);
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="mb-3 flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Event Filters</h2>
        <button 
          onClick={resetFilters}
          className="rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
          title="Reset filters"
        >
          <RefreshIcon className="h-5 w-5" />
        </button>
      </div>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input 
              type="checkbox" 
              id="filter-motion" 
              className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              checked={filters.motion}
              onChange={() => handleFilterChange('motion')}
            />
            <label htmlFor="filter-motion" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Motion Events
            </label>
          </div>
          <span className="text-xs rounded-full bg-yellow-100 px-2 py-0.5 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            {eventCounts.motion}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input 
              type="checkbox" 
              id="filter-person" 
              className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              checked={filters.person}
              onChange={() => handleFilterChange('person')}
            />
            <label htmlFor="filter-person" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Person Detection
            </label>
          </div>
          <span className="text-xs rounded-full bg-red-100 px-2 py-0.5 text-red-800 dark:bg-red-900 dark:text-red-200">
            {eventCounts.person}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input 
              type="checkbox" 
              id="filter-object" 
              className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              checked={filters.object}
              onChange={() => handleFilterChange('object')}
            />
            <label htmlFor="filter-object" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              Object Detection
            </label>
          </div>
          <span className="text-xs rounded-full bg-blue-100 px-2 py-0.5 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {eventCounts.object}
          </span>
        </div>
        
        <div className="pt-3">
          <button 
            onClick={onApplyFilters}
            className="w-full rounded-md bg-primary-50 px-3 py-2 text-sm font-medium text-primary-700 hover:bg-primary-100 dark:bg-primary-900 dark:text-primary-200 dark:hover:bg-primary-800"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default EventsFiltersPanel;
