"use client";

import React, { useState } from 'react';
import { Camera } from '@/types/camera';
import {
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@/components/icons/CameraIcons';

interface EventsCalendarPanelProps {
  cameraId: string;
  camera: Camera | null;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

const EventsCalendarPanel: React.FC<EventsCalendarPanelProps> = ({
  camera,
  selectedDate,
  onDateChange
}) => {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date(selectedDate));

  // Navigate to previous month
  const goToPrevMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentMonth(newDate);
  };

  // Navigate to next month
  const goToNextMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentMonth(newDate);
  };

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const days = [];
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // Create a date for the first day of the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0); // Last day of current month
    
    // Calculate the starting day of the week (0 = Sunday)
    const startDay = firstDay.getDay();
    
    // Add empty slots for days before the first of the month
    for (let i = 0; i < startDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-7"></div>
      );
    }
    
    // Add calendar days
    const today = new Date();
    const hasEvents = [1, 5, 10, 15, 20, 25]; // Example days with events
    
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const isToday = i === today.getDate() && 
                      month === today.getMonth() && 
                      year === today.getFullYear();
      
      const isSelected = i === selectedDate.getDate() && 
                        month === selectedDate.getMonth() && 
                        year === selectedDate.getFullYear();
                        
      const hasEvent = hasEvents.includes(i);
      
      days.push(
        <button
          key={i}
          onClick={() => onDateChange(new Date(year, month, i))}
          className={`h-7 w-7 rounded-full text-xs relative ${
            isToday ? 'font-bold' : ''
          } ${
            isSelected ? 'bg-primary-500 text-white' : hasEvent ? 'text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'
          } ${
            hasEvent && !isSelected ? 'border border-primary-400' : ''
          } hover:bg-gray-100 dark:hover:bg-gray-700`}
        >
          {i}
          {hasEvent && !isSelected && (
            <span className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full bg-primary-500"></span>
          )}
        </button>
      );
    }
    
    return days;
  };

  return (
    <div className="space-y-4">
      {/* Calendar Panel */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-3 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Event Calendar</h2>
          <div className="flex space-x-1">
            <button 
              onClick={goToPrevMonth}
              className="rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button 
              onClick={goToNextMonth}
              className="rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        {/* Month and year display */}
        <div className="mb-3 text-center font-medium text-gray-700 dark:text-gray-300">
          {currentMonth.toLocaleString('default', { month: 'long' })} {currentMonth.getFullYear()}
        </div>
        
        {/* Calendar grid */}
        <div className="mb-2 grid grid-cols-7 gap-1 text-center">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
            <div key={i} className="text-xs font-medium text-gray-500 dark:text-gray-400">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-1 text-center">
          {generateCalendarDays()}
        </div>
        
        {/* Date selector */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Selected:
            </span>
            <div className="relative">
              <input
                type="date"
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-900 shadow-sm focus:border-primary-500 focus:ring-primary-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
                value={selectedDate.toISOString().split('T')[0]}
                onChange={(e) => onDateChange(new Date(e.target.value))}
              />
            </div>
          </div>
          
          <button 
            onClick={() => onDateChange(new Date())}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            <CalendarIcon className="mr-1.5 h-4 w-4" />
            Today
          </button>
        </div>
      </div>
      
      {/* Event Info Panel */}
      {camera && (
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <h2 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">Event Info</h2>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Camera:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{camera.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Date:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {selectedDate.toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Total Events:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">67</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Storage Used:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">1.7 GB</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventsCalendarPanel;
