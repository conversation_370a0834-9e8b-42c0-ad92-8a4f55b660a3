"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Check, Eye, TriangleAlert } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { LoaderButton } from "@/components/ui/loader-button";
import { Progress } from "@/components/ui/progress";

const ViewEventDrillDown = () => {
  const [isReviewed, setIsReviewed] = useState<boolean>(false);
  const [isMarkingSafe, setIsMarkingSafe] = useState<boolean>(false);
  const [isMarkingSuspicious, setIsMarkingSuspicious] =
    useState<boolean>(false);

  const handleMarkSafe = async () => {
    setIsMarkingSafe(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsReviewed(true);
    } catch (error) {
      console.error("Failed to mark as safe:", error);
    }

    setIsMarkingSafe(false);
  };

  const handleMarkSuspicious = async () => {
    setIsMarkingSuspicious(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsReviewed(true);
    } catch (error) {
      console.error("Failed to mark as suspicious:", error);
    }

    setIsMarkingSuspicious(false);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="icon" variant="ghost">
          <Eye />
        </Button>
      </DialogTrigger>

      <DialogContent className="max-h-[calc(100dvh-2rem)] overflow-auto sm:max-w-[calc(80rem-2rem)]">
        <DialogHeader>
          <DialogTitle>Event #1</DialogTitle>
          <DialogDescription>25 Apr 2025, 8:14 AM</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 md:grid-cols-2">
          <video
            className="bg-muted h-full w-full rounded-md border object-contain"
            src="/videos/vid_1.mp4"
            playsInline
            loop
            muted
            autoPlay
            controls
            preload="auto"
          />

          <Image
            src="/images/segmented_image.jpg"
            alt="Segmented Image"
            width={512}
            height={512}
            className="bg-muted h-full w-full rounded-md border object-contain"
          />
        </div>

        <div className="flex items-center gap-2 text-sm">
          <p>Confidence Score</p>
          <Progress value={87} className="flex-1" />
          <span className="text-primary">87%</span>
        </div>

        {!isReviewed && (
          <DialogFooter>
            <LoaderButton
              onClick={handleMarkSafe}
              isLoading={isMarkingSafe}
              icon={Check}
            >
              Not Suspicious
            </LoaderButton>

            <LoaderButton
              variant="destructive"
              onClick={handleMarkSuspicious}
              isLoading={isMarkingSuspicious}
              icon={TriangleAlert}
            >
              Mark as Suspicious
            </LoaderButton>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ViewEventDrillDown;
