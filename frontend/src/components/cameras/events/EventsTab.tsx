"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { Camera } from '@/types/camera';
import ViewEventDrillDown from './ViewEventDrillDown';
import { 
  SearchIcon, 
  FilterIcon, 
  ExternalLinkIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@/components/icons/CameraIcons';

// Types for events
interface Event {
  id: number;
  type: 'person' | 'motion' | 'object';
  timestamp: string;
  confidence: number;
  thumbnail: string;
}

// Props interface
interface EventsTabProps {
  cameraId: string;
  camera: Camera | null;
  selectedDate: Date;
  filters: {
    motion: boolean;
    person: boolean;
    object: boolean;
  };
  searchQuery: string;
  eventType: string;
  onSearchChange: (query: string) => void;
  onEventTypeChange: (type: string) => void;
  onApplyFilters: () => void;
}

const EventsTab: React.FC<EventsTabProps> = ({ 
  selectedDate,
  filters,
  searchQuery,
  eventType,
  onSearchChange,
  onEventTypeChange,
  onApplyFilters
}) => {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Mock events data
  const [events] = useState<Event[]>([
    {
      id: 1,
      type: 'motion',
      timestamp: '2023-09-25T08:14:23',
      confidence: 87,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 2,
      type: 'person',
      timestamp: '2023-09-25T09:22:46',
      confidence: 94,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 3,
      type: 'motion',
      timestamp: '2023-09-25T10:03:12',
      confidence: 82,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 4,
      type: 'person',
      timestamp: '2023-09-25T11:42:05',
      confidence: 98,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 5,
      type: 'object',
      timestamp: '2023-09-25T13:15:37',
      confidence: 91,
      thumbnail: '/images/office.jpg',
    },
    // Add more mock data for pagination testing
    {
      id: 6,
      type: 'motion',
      timestamp: '2023-09-25T14:20:15',
      confidence: 76,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 7,
      type: 'person',
      timestamp: '2023-09-25T15:30:42',
      confidence: 89,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 8,
      type: 'object',
      timestamp: '2023-09-25T16:45:18',
      confidence: 93,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 9,
      type: 'motion',
      timestamp: '2023-09-25T17:12:33',
      confidence: 81,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 10,
      type: 'person',
      timestamp: '2023-09-25T18:25:07',
      confidence: 95,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 11,
      type: 'object',
      timestamp: '2023-09-25T19:40:55',
      confidence: 88,
      thumbnail: '/images/office.jpg',
    },
    {
      id: 12,
      type: 'motion',
      timestamp: '2023-09-25T20:15:22',
      confidence: 79,
      thumbnail: '/images/office.jpg',
    },
  ]);

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle event type selection
  const handleEventTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onEventTypeChange(e.target.value);
    setCurrentPage(1); // Reset to first page when changing filters
  };

  // Filter events based on current filters
  const filteredEvents = events.filter(event => {
    // Type filter
    if (eventType !== 'all' && event.type !== eventType) {
      return false;
    }
    
    // Specific type filters
    if (!filters[event.type]) {
      return false;
    }
    
    // Search query
    if (searchQuery && !`Event #${event.id}`.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Date filter (checking only the day part for demo purposes)
    const eventDate = new Date(event.timestamp);
    if (
      eventDate.getDate() !== selectedDate.getDate() ||
      eventDate.getMonth() !== selectedDate.getMonth() ||
      eventDate.getFullYear() !== selectedDate.getFullYear()
    ) {
      return false;
    }
    
    return true;
  });

  // Pagination calculations
  const totalItems = filteredEvents.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentEvents = filteredEvents.slice(startIndex, endIndex);

  // Handle page changes
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      // Adjust if we're near the end
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  // Calculate event counts for stats
  const totalEvents = events.length;
  const personEvents = events.filter((e) => e.type === "person").length;
  const motionEvents = events.filter((e) => e.type === "motion").length;
  const objectEvents = events.filter((e) => e.type === "object").length;

  return (
    <div className="space-y-4">
      {/* Events Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
          Event History
        </h3>

        <div className="flex items-center space-x-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search events..."
              value={searchQuery}
              onChange={handleSearch}
              className="w-full rounded-md border border-gray-300 bg-white px-3 py-1.5 pr-10 text-sm text-gray-900 shadow-sm focus:border-primary-500 focus:ring-primary-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
            />
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <SearchIcon className="h-4 w-4 text-gray-400" />
            </div>
          </div>

          <select 
            className="rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm text-gray-900 shadow-sm focus:border-primary-500 focus:ring-primary-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
            value={eventType}
            onChange={handleEventTypeChange}
          >
            <option value="all">All Types</option>
            <option value="motion">Motion</option>
            <option value="person">Person</option>
            <option value="object">Object</option>
          </select>

          <button 
            onClick={onApplyFilters}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            <FilterIcon className="mr-1.5 h-4 w-4" />
            Filters
          </button>

          <button className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none">
            <ExternalLinkIcon className="mr-1.5 h-4 w-4" />
            Export
          </button>
        </div>
      </div>

      {/* Event Statistics */}
      <div className="grid grid-cols-4 gap-4 mb-4">
        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total Events
          </div>
          <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
            {totalEvents}
          </div>
        </div>

        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Person Detections
          </div>
          <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
            {personEvents}
          </div>
        </div>

        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Motion Events
          </div>
          <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
            {motionEvents}
          </div>
        </div>

        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Object Detections
          </div>
          <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
            {objectEvents}
          </div>
        </div>
      </div>

      {/* Events Table */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                Event
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                Time
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                Confidence
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                Actions
              </th>
            </tr>
          </thead>

          <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
            {currentEvents.length > 0 ? currentEvents.map((event) => {
              // Format timestamp for display
              const eventDate = new Date(event.timestamp);
              const formattedTime = eventDate.toLocaleTimeString(
                "en-US",
                { hour: "2-digit", minute: "2-digit" }
              );
              const formattedDate = eventDate.toLocaleDateString(
                "en-US",
                { month: "short", day: "numeric" }
              );

              return (
                <tr
                  key={event.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="mr-4 h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
                        <Image
                          src={event.thumbnail}
                          alt="Event thumbnail"
                          className="h-full w-full object-cover"
                          width={64}
                          height={64}
                        />
                      </div>

                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          Event #{event.id}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formattedDate}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        event.type === "person"
                          ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                          : event.type === "motion"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                            : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                      }`}
                    >
                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formattedTime}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="mb-1 h-1.5 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                      <div
                        className="h-1.5 rounded-full bg-primary-500"
                        style={{ width: `${event.confidence}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {event.confidence}%
                    </div>
                  </td>

                  <td className="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                    <div className="flex justify-end space-x-2">
                      <ViewEventDrillDown />
                    </div>
                  </td>
                </tr>
              );
            }) : (
              <tr>
                <td colSpan={5} className="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                  No events found matching your criteria. Try adjusting your filters.
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 dark:border-gray-700 dark:bg-gray-800">
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{" "}
                  <span className="font-medium">{Math.min(endIndex, totalItems)}</span> of{" "}
                  <span className="font-medium">{totalItems}</span>{" "}
                  results
                </p>
              </div>
              <div>
                <nav
                  className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm"
                  aria-label="Pagination"
                >
                  {/* Previous button */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`relative inline-flex items-center rounded-l-md border px-2 py-2 text-sm font-medium focus:outline-none ${
                      currentPage === 1
                        ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-400 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-500"
                        : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                    }`}
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  
                  {/* Page numbers */}
                  {getPageNumbers().map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium focus:outline-none ${
                        page === currentPage
                          ? "z-10 border-primary-500 bg-primary-50 text-primary-600 dark:border-primary-400 dark:bg-primary-900/30 dark:text-primary-400"
                          : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  
                  {/* Next button */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`relative inline-flex items-center rounded-r-md border px-2 py-2 text-sm font-medium focus:outline-none ${
                      currentPage === totalPages
                        ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-400 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-500"
                        : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                    }`}
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EventsTab;
