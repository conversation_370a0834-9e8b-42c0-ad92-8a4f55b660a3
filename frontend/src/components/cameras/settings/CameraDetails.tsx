"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { CameraComponentProps } from '@/types/camera';
import { updateCamera, deleteCamera } from '@/services/cameras';

// Create simplified icons
const PencilIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
  </svg>
);

const SaveIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h1a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h1v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
  </svg>
);

const XIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" {...props}>
    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
  </svg>
);

interface CameraSettingsState {
  name: string;
  location: string;
  rtsp_url: string;
  enabled: boolean;
  resolution: string;
  quality: string;
  motionDetection: boolean;
  personDetection: boolean;
  objectDetection: boolean;
  ipAddress: string;
  subnetMask: string;
}

const CameraDetails: React.FC<CameraComponentProps> = ({ 
  cameraId, 
  camera 
}) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const initialCameraSettings: CameraSettingsState = {
    name: camera?.name || '',
    location: camera?.location || '',
    rtsp_url: camera?.rtsp_url || '',
    enabled: camera?.enabled || false,
    resolution: '1080p', // Default values for new fields
    quality: 'high',
    motionDetection: true,
    personDetection: true,
    objectDetection: true,
    ipAddress: '*************',
    subnetMask: '*************'
  };
  
  const [editedCamera, setEditedCamera] = useState<CameraSettingsState>(initialCameraSettings);
  
  // Handle camera update
  const handleUpdateCamera = async () => {
    if (!editedCamera) return;
    
    try {
      await updateCamera(cameraId, {
        name: editedCamera.name,
        location: editedCamera.location,
        rtsp_url: editedCamera.rtsp_url,
        enabled: editedCamera.enabled
        // Note: The backend API might not support the additional fields yet
        // We'd need to update the API to handle these new fields
      });
      
      setIsEditing(false);
      toast.success('Camera settings updated successfully');
    } catch (error) {
      console.error('Error updating camera:', error);
      toast.error(`Failed to update camera: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  
  // Handle camera deletion
  const handleDeleteCamera = async () => {
    if (isDeleting) {
      try {
        await deleteCamera(cameraId);
        toast.success('Camera deleted successfully');
        // Navigate back to cameras list
        router.push('/dashboard/cameras');
      } catch (error) {
        console.error('Error deleting camera:', error);
        toast.error(`Failed to delete camera: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsDeleting(false);
      }
    } else {
      setIsDeleting(true);
    }
  };
  
  // Handle input field change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setEditedCamera({
      ...editedCamera,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    });
  };
  
  if (!camera) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
        <div className="animate-pulse space-y-4">
          <div className="h-5 w-1/3 rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="h-4 w-2/3 rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="h-4 w-full rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="h-4 w-full rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-bold">Camera Settings</h2>
        
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setEditedCamera(initialCameraSettings);
                }}
                className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700"
                title="Cancel"
              >
                <XIcon className="h-5 w-5" />
              </button>
              <button
                onClick={handleUpdateCamera}
                className="rounded-full p-1 text-primary-600 hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900"
                title="Save changes"
              >
                <SaveIcon className="h-5 w-5" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700"
                title="Edit camera"
              >
                <PencilIcon className="h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>
      
      <div className="space-y-6">
        {/* Basic Camera Information */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Basic Information</h3>
          
          {/* Camera name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Camera Name
            </label>
            {isEditing ? (
              <input
                type="text"
                name="name"
                value={editedCamera.name}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
              />
            ) : (
              <p className="mt-1 text-gray-900 dark:text-white">{camera.name}</p>
            )}
          </div>
          
          {/* Camera location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Location
            </label>
            {isEditing ? (
              <input
                type="text"
                name="location"
                value={editedCamera.location}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
              />
            ) : (
              <p className="mt-1 text-gray-900 dark:text-white">{camera.location || 'No location set'}</p>
            )}
          </div>
          
          {/* Enabled status */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                name="enabled"
                checked={editedCamera.enabled}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 focus:ring-offset-0 dark:border-gray-600 dark:bg-gray-700"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Camera Enabled
              </span>
            </label>
          </div>
        </div>
        
        {/* Stream Settings */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Stream Settings</h3>
          
          {/* RTSP URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              RTSP Stream URL
            </label>
            {isEditing ? (
              <input
                type="text"
                name="rtsp_url"
                value={editedCamera.rtsp_url}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
              />
            ) : (
              <p className="mt-1 text-gray-900 dark:text-white">
                {camera.rtsp_url || 'No RTSP URL set'}
              </p>
            )}
          </div>
          
          {/* Resolution and Quality Settings */}
          {isEditing && (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Resolution
                </label>
                <select
                  name="resolution"
                  value={editedCamera.resolution}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="720p">720p</option>
                  <option value="1080p">1080p (HD)</option>
                  <option value="1440p">1440p (2K)</option>
                  <option value="4k">4K (UHD)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Quality
                </label>
                <select
                  name="quality"
                  value={editedCamera.quality}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="ultra">Ultra</option>
                </select>
              </div>
            </div>
          )}
        </div>
        
        {/* Detection Settings */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Detection Settings</h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Motion Detection
              </span>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  name="motionDetection"
                  checked={editedCamera.motionDetection}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="peer sr-only"
                />
                <div className="peer h-6 w-11 rounded-full bg-gray-200 peer-checked:bg-primary-600 peer-focus:ring-4 peer-focus:ring-primary-300 peer-focus:outline-none after:absolute after:top-[2px] after:left-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary-800"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Person Detection
              </span>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  name="personDetection"
                  checked={editedCamera.personDetection}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="peer sr-only"
                />
                <div className="peer h-6 w-11 rounded-full bg-gray-200 peer-checked:bg-primary-600 peer-focus:ring-4 peer-focus:ring-primary-300 peer-focus:outline-none after:absolute after:top-[2px] after:left-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary-800"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Object Detection
              </span>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  name="objectDetection"
                  checked={editedCamera.objectDetection}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="peer sr-only"
                />
                <div className="peer h-6 w-11 rounded-full bg-gray-200 peer-checked:bg-primary-600 peer-focus:ring-4 peer-focus:ring-primary-300 peer-focus:outline-none after:absolute after:top-[2px] after:left-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary-800"></div>
              </label>
            </div>
          </div>
        </div>
        
        {/* Network Settings */}
        {isEditing && (
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">Network Settings</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  IP Address
                </label>
                <input
                  type="text"
                  name="ipAddress"
                  value={editedCamera.ipAddress}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Subnet Mask
                </label>
                <input
                  type="text"
                  name="subnetMask"
                  value={editedCamera.subnetMask}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
            </div>
          </div>
        )}
        
        {/* Camera Info */}
        <div className="mt-6 rounded-md bg-gray-50 p-4 dark:bg-gray-700">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Technical Information</h3>
          <div className="mt-2 space-y-2 text-sm text-gray-500 dark:text-gray-400">
            <p>ID: {camera.id ? `...${camera.id.slice(-4)}` : 'N/A'}</p>
            <p>Created: {camera.created_at ? new Date(camera.created_at).toLocaleString() : 'N/A'}</p>
            <p>Last Updated: {camera.updated_at ? new Date(camera.updated_at).toLocaleString() : 'N/A'}</p>
            <p>Last Status Check: {camera.last_status_check ? new Date(camera.last_status_check).toLocaleString() : 'N/A'}</p>
          </div>
        </div>
        
        {/* Danger Zone */}
        <div className="mt-8 rounded-md border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-red-800 dark:text-red-200">Danger Zone</h3>
            <div className="mt-2 max-w-xl text-sm text-red-700 dark:text-red-300">
              <p>Once you delete a camera, all of its data including detection layers and history will be permanently removed. This action cannot be undone.</p>
            </div>
            <div className="mt-5">
              <button
                type="button"
                onClick={handleDeleteCamera}
                className={`inline-flex w-full items-center justify-center rounded-md border ${isDeleting ? 'border-red-900 bg-red-800' : 'border-red-300 bg-red-600'} px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:hover:bg-red-800`}
              >
                {isDeleting ? 'Click Again to Confirm Deletion' : 'Delete This Camera'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CameraDetails;
