"use client";

import React, { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { CircleCheck, HelpCircle, Plus, TriangleAlert } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { <PERSON>ert, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { LoaderButton } from "@/components/ui/loader-button";
import { NumberInput } from "@/components/ui/number-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { addCamera, useAllCameras } from "@/services/cameras";

const formSchema = z.object({
  name: z.string().min(1, "Required"),
  rtsp_url: z
    .string()
    .min(1, "Required")
    .regex(/^rtsp:\/\/.+/, "Invalid"),
  location: z.string().min(1, "Required"),
  description: z.string().optional(),
  stream_fps: z.coerce
    .number()
    .min(1, "Minimum 1 FPS")
    .max(60, "Maximum 60 FPS"),
  dynamic_frame_rate: z.boolean(),
  encoding_format: z.enum(["H.264", "H.265"]),
});

const AddCamera = () => {
  const { mutate } = useAllCameras();
  const [open, setOpen] = useState<boolean>(false);

  const [isTesting, setIsTesting] = useState<boolean>(false);
  const [isConnectionValid, setIsConnectionValid] = useState<boolean | null>(
    null
  );

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      rtsp_url: "",
      location: "",
      description: "",
      stream_fps: 10,
      dynamic_frame_rate: true,
      encoding_format: "H.264",
    },
  });

  const rtspUrl = form.watch("rtsp_url");
  const rtspValidation = formSchema.shape.rtsp_url.safeParse(rtspUrl);

  useEffect(() => {
    setIsConnectionValid(null);
    setIsTesting(false);
  }, [rtspUrl]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await addCamera(values);
      mutate();
      toast.success("New camera added");
      setOpen(false);
    } catch (error) {
      toast.error(String(error));
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsConnectionValid(true);
    } catch (error) {
      console.error("Failed to test connection:", error);
      setIsConnectionValid(false);
    }

    setIsTesting(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="size-4" />
          Add Camera
        </Button>
      </DialogTrigger>

      <DialogContent
        onCloseAutoFocus={() => {
          form.reset();
          setIsConnectionValid(null);
          setIsTesting(false);
        }}
      >
        <Form {...form}>
          <form
            autoComplete="off"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-6"
          >
            <DialogHeader>
              <DialogTitle>New Camera</DialogTitle>
              <DialogDescription>
                New camera will be added to the list.
              </DialogDescription>
            </DialogHeader>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Camera Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Surveillance Camera" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rtsp_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>RTSP URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="rtsp://username:password@ip_address:port/path"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="Building entrance" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Camera details or notes" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="after:border-border relative -mt-1.5 -mb-2 text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
              <span className="bg-background text-muted-foreground relative z-10 px-2">
                Advanced Settings
              </span>
            </div>

            <div className="grid items-start gap-6 md:grid-cols-2">
              <div className="flex flex-col gap-4">
                <FormField
                  control={form.control}
                  name="stream_fps"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>FPS</FormLabel>
                      <FormControl>
                        <NumberInput
                          placeholder="10"
                          decimalScale={0}
                          allowNegative={false}
                          min={1}
                          max={60}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dynamic_frame_rate"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Dynamic Frame Rate</FormLabel>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="cursor-help" asChild>
                            <HelpCircle className="size-4" />
                          </TooltipTrigger>

                          <TooltipContent
                            className="max-w-40 text-center"
                            side="bottom"
                          >
                            Lowers FPS when idle, increases when more people are
                            detected
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="encoding_format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Encoding Method</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full min-w-24">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>

                      <SelectContent>
                        {["H.264", "H.265"].map((encoding) => (
                          <SelectItem key={encoding} value={encoding}>
                            {encoding}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {isConnectionValid !== null && (
              <Alert variant={isConnectionValid ? "default" : "destructive"}>
                {isConnectionValid ? <CircleCheck /> : <TriangleAlert />}
                <AlertTitle>
                  {isConnectionValid
                    ? "Connected to the camera!"
                    : "Failed to connect, please update the RTSP URL and try again."}
                </AlertTitle>
              </Alert>
            )}

            <DialogFooter>
              <LoaderButton
                variant="outline"
                className="order-1 sm:order-none sm:mr-auto"
                onClick={handleTestConnection}
                isLoading={isTesting}
                disabled={isConnectionValid || !rtspValidation.success}
              >
                Test Connection
              </LoaderButton>

              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>

              <LoaderButton
                type="submit"
                isLoading={form.formState.isSubmitting}
                disabled={!isConnectionValid}
              >
                Add Camera
              </LoaderButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddCamera;
