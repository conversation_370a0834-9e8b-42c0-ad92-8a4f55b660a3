"use client";

import React, { useState, useEffect, useRef } from 'react';
import StreamViewer from '@/components/cameras/common/StreamViewer';
import CameraOverlay from './CameraOverlay';
import { useCameraOverlays } from '@/services/overlays';

interface OverlayStreamViewerProps {
  cameraId: string;
  streamUrl: string;
  className?: string;
  onError?: () => void;
  onSuccess?: () => void;
  debugMode?: boolean;
}

/**
 * Enhanced StreamViewer that displays permanent overlays on camera feeds
 */
const OverlayStreamViewer: React.FC<OverlayStreamViewerProps> = ({
  cameraId,
  streamUrl,
  className = "w-full h-full",
  onError,
  onSuccess,
  debugMode = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const { overlays, isLoading: overlaysLoading, isError: overlaysError } = useCameraOverlays(cameraId);

  // Update dimensions when container size changes
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setDimensions({
          width: clientWidth,
          height: clientHeight,
        });
      }
    };

    // Initialize dimensions
    updateDimensions();

    // Set up resize observer to track container size changes
    const observer = new ResizeObserver(updateDimensions);
    
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    // Clean up observer
    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Regular stream viewer */}
      <StreamViewer
        streamUrl={streamUrl}
        className="w-full h-full"
        onError={onError}
        onSuccess={onSuccess}
        debugMode={debugMode}
      />

      {/* Overlay layer */}
      {!overlaysLoading && !overlaysError && overlays.length > 0 && dimensions.width > 0 && dimensions.height > 0 && (
        <CameraOverlay
          overlays={overlays}
          containerWidth={dimensions.width}
          containerHeight={dimensions.height}
        />
      )}
    </div>
  );
};

export default OverlayStreamViewer;
