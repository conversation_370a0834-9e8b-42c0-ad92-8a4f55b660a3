"use client";

import React, { useRef } from 'react';
import { PermanentOverlay } from '@/types/overlay';

interface CameraOverlayProps {
  overlays: PermanentOverlay[];
  containerWidth: number;
  containerHeight: number;
  className?: string;
}

/**
 * Renders permanent camera overlays as SVG polygons
 */
const CameraOverlay: React.FC<CameraOverlayProps> = ({
  overlays,
  containerWidth,
  containerHeight,
  className = "",
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  
  // Function to render a single overlay polygon
  const renderOverlay = (overlay: PermanentOverlay) => {
    const { coordinates, text, color } = overlay;
    
    // Use normalized coordinates for better scaling across different camera resolutions
    if (!coordinates || !coordinates.normalized || coordinates.normalized.length === 0) {
      return null;
    }
    
    // Convert normalized coordinates to actual pixels based on container dimensions
    const points = coordinates.normalized.map(([x, y]) => {
      const pixelX = x * containerWidth;
      const pixelY = y * containerHeight;
      return `${pixelX},${pixelY}`;
    }).join(' ');
    
    // Find the center point for text placement
    const centerPoint = coordinates.normalized.reduce(
      (acc, [x, y]) => {
        acc.x += x * containerWidth;
        acc.y += y * containerHeight;
        return acc;
      },
      { x: 0, y: 0 }
    );
    
    centerPoint.x /= coordinates.normalized.length;
    centerPoint.y /= coordinates.normalized.length;
    
    // Generate a unique ID for the polygon
    const polygonId = `overlay-${overlay.id}`;
    
    return (
      <g key={overlay.id}>
        <polygon 
          id={polygonId}
          points={points} 
          fill={`${color}40`} // 40 = 25% opacity in hex
          stroke={color} 
          strokeWidth="2"
          strokeLinejoin="round" 
          className="overlay-polygon"
        />
        
        {text && (
          <text 
            x={centerPoint.x}
            y={centerPoint.y}
            textAnchor="middle" 
            dominantBaseline="middle"
            fill="white"
            stroke={color} 
            strokeWidth="0.5"
            fontSize="16"
            fontWeight="bold"
          >
            {text}
          </text>
        )}
      </g>
    );
  };
  
  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      <svg 
        ref={svgRef}
        width={containerWidth}
        height={containerHeight}
        className="w-full h-full"
        style={{ overflow: 'visible' }}
      >
        {overlays.map(renderOverlay)}
      </svg>
    </div>
  );
};

export default CameraOverlay;
