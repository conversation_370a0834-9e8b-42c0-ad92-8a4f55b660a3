"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import StreamViewer from './StreamViewer';

interface Detection {
  id: string;
  type: string;
  confidence: number;
  bbox: number[];  // [x1, y1, x2, y2] normalized coordinates (0-1)
  color: string;
}

interface StreamViewerWithDetectionsProps {
  streamUrl: string;
  className?: string;
  onError?: () => void;
  onSuccess?: () => void;
  showDetections?: boolean;
  showLabels?: boolean;
  showConfidence?: boolean;
  minConfidence?: number;
  useRandomBoxes?: boolean;  // Controls whether to use random boxes (separate from debug)
  debugMode?: boolean;
}

/**
 * Enhanced MJPEG stream viewer component with real-time detection overlays.
 * Uses a custom stream endpoint that embeds detection data in HTTP headers.
 */
const StreamViewerWithDetections: React.FC<StreamViewerWithDetectionsProps> = ({
  streamUrl,
  className = "w-full h-full object-contain",
  onError,
  onSuccess,
  showDetections = true,
  showLabels = true,
  showConfidence = true,
  useRandomBoxes = true,  // Use random boxes by default
  debugMode = false
}) => {
  // Store the current detection data
  const [detections, setDetections] = useState<Detection[]>([]);
  const [lastFrameId, setLastFrameId] = useState<number>(-1);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [imageSize] = useState({ width: 0, height: 0 });

  // Track if the stream actually loaded successfully
  const [streamLoaded, setStreamLoaded] = useState(false);

  // Custom handlers for stream status
  const handleStreamSuccess = useCallback(() => {
    setStreamLoaded(true);
    console.log('Stream loaded successfully in StreamViewerWithDetections');
    if (onSuccess) onSuccess();
  }, [onSuccess]);
  
  const handleStreamError = useCallback(() => {
    setStreamLoaded(false);
    console.log('Stream failed to load in StreamViewerWithDetections');
    // Clear detections if the stream failed unless we're in debug mode
    if (!debugMode && !useRandomBoxes) {
      setDetections([]);
    }
    if (onError) onError();
  }, [onError, debugMode, useRandomBoxes]);
  
  // Update container dimensions when mounted or resized
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      if (containerRef.current) {
        setContainerDimensions({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight
        });
      }
    };
    
    // Initial update
    updateDimensions();
    
    // Set up resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, []);
  
  // Update container dimensions when the container resizes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      if (containerRef.current) {
        setContainerDimensions({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight
        });
      }
    };
    
    // Initial update
    updateDimensions();
    
    // Set up resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, []);
  
  useEffect(() => {
    if (!debugMode || streamUrl) return;
    
    // Create random detections for testing when no stream is available
    const testInterval = setInterval(() => {
      const numDetections = Math.floor(Math.random() * 5) + 1;
      const types = ['person', 'vehicle', 'animal', 'suspicious', 'unattended_object'];
      const colors = ['red', 'blue', 'green', 'orange', 'purple'];
      
      const newDetections: Detection[] = [];
      
      for (let i = 0; i < numDetections; i++) {
        const typeIndex = Math.floor(Math.random() * types.length);
        const x1 = Math.random() * 0.7 + 0.1;
        const y1 = Math.random() * 0.7 + 0.1;
        const width = Math.random() * 0.2 + 0.1;
        const height = Math.random() * 0.2 + 0.1;
        
        newDetections.push({
          id: `test_${Date.now()}_${i}`,
          type: types[typeIndex],
          color: colors[typeIndex],
          confidence: Math.random() * 0.3 + 0.7,
          bbox: [x1, y1, x1 + width, y1 + height]
        });
      }
      
      setDetections(newDetections);
      setLastFrameId(prev => prev + 1);
    }, 2000);
    
    return () => clearInterval(testInterval);
  }, [debugMode, streamUrl]);
  
  // Convert normalized coordinates to pixel coordinates
  const calculatePixelCoordinates = (bbox: number[]) => {
    if (!containerRef.current || containerDimensions.width === 0) {
      return { x1: 0, y1: 0, x2: 0, y2: 0 };
    }
    
    // Get dimensions from container
    const { width, height } = containerDimensions;
    
    // Calculate SVG coordinates
    const [x1Norm, y1Norm, x2Norm, y2Norm] = bbox;
    
    return {
      x1: Math.round(x1Norm * width),
      y1: Math.round(y1Norm * height),
      x2: Math.round(x2Norm * width),
      y2: Math.round(y2Norm * height)
    };
  };
  
  return (
    <div ref={containerRef} className="relative w-full h-full">
      {/* Use the original StreamViewer for the base functionality */}
      <StreamViewer 
        streamUrl={streamUrl}
        className={className}
        onError={handleStreamError}
        onSuccess={handleStreamSuccess}
        debugMode={debugMode}
      />
      
      {/* SVG overlay for detection bounding boxes - only show when stream loaded or in debug mode */}
      {showDetections && detections.length > 0 && (streamLoaded || debugMode || useRandomBoxes) && (
        <div className="absolute inset-0 pointer-events-none">
          <svg width="100%" height="100%" className="absolute inset-0">
            {detections.map((detection) => {
              // Get detection coordinates
              let { x1, y1, x2, y2 } = calculatePixelCoordinates(detection.bbox);
              
              // Ensure x1 is always less than x2, and y1 is always less than y2
              // This prevents negative width/height values which are invalid in SVG
              if (x1 > x2) [x1, x2] = [x2, x1];
              if (y1 > y2) [y1, y2] = [y2, y1];
              
              const boxWidth = Math.max(1, x2 - x1); // Ensure minimum size of 1px
              const boxHeight = Math.max(1, y2 - y1);
              
              return (
                <g key={detection.id}>
                  {/* Bounding box */}
                  <rect
                    x={x1}
                    y={y1}
                    width={boxWidth}
                    height={boxHeight}
                    stroke={detection.color || 'red'}
                    strokeWidth="2"
                    fill="none"
                  />
                  
                  {/* Label background */}
                  {showLabels && (
                    <rect
                      x={x1}
                      y={y1 - 20}
                      width={detection.type.length * 8 + (showConfidence ? 50 : 10)}
                      height="20"
                      fill={detection.color || 'red'}
                      fillOpacity="0.7"
                    />
                  )}
                  
                  {/* Label text */}
                  {showLabels && (
                    <text
                      x={x1 + 5}
                      y={y1 - 5}
                      fill="white"
                      fontSize="12"
                      fontWeight="bold"
                    >
                      {detection.type}
                      {showConfidence && ` (${Math.round(detection.confidence * 100)}%)`}
                    </text>
                  )}
                </g>
              );
            })}
          </svg>
          
          {/* Debug info */}
          {debugMode && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white p-2 rounded text-xs max-w-xs">
              <p>Detections: {detections.length}</p>
              <p>Frame: {lastFrameId}</p>
              <p>Container: {containerDimensions.width}x{containerDimensions.height}</p>
              <p>Image: {imageSize.width}x{imageSize.height}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StreamViewerWithDetections;
