"use client";

import React, { useState, useCallback, useEffect } from "react";
import OverlayStreamViewer from "@/components/cameras/overlays/OverlayStreamViewer";
import { useCamera } from "@/services/cameras";

interface LiveCameraProps {
  cameraId: string;
  cameraName: string;
  stream_url?: string;
}

/**
 * Simplified LiveCameraTab component that just focuses on showing the camera stream
 * with detection overlays, without any status checking or complex logic.
 */
const LiveCameraTab: React.FC<LiveCameraProps> = ({
  cameraId,
  cameraName,
  stream_url,
}) => {
  const [refreshKey, setRefreshKey] = useState<number>(0);
  const [shouldRenderStream, setShouldRenderStream] = useState<boolean>(true);
  
  // Fetch camera data with SWR to get the latest status
  const { camera, mutate } = useCamera(cameraId);
  
  // Construct the stream URL if not provided
  const effectiveStreamUrl = stream_url || `/api/cameras/${cameraId}/stream/`;
  
  // Debug logging to check what the API is returning
  useEffect(() => {
    if (camera && camera.status == "online") {
      console.log('Camera data from API:', camera);
      console.log('Camera status:', camera.status);
      setShouldRenderStream(true);
    } else {
      setShouldRenderStream(false);
    }
  }, [camera]);
  
  // Force refresh the camera data every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('Refreshing camera data...');
      mutate();
    }, 10000);
    
    return () => clearInterval(interval);
  }, [mutate]);

  // These handlers no longer update local state but are still needed for the StreamViewer component
  const handleStreamSuccess = useCallback(() => {
    // We no longer set a local stream status - the backend is the source of truth
    console.log('Stream loaded successfully');
  }, []);

  const handleStreamError = useCallback(() => {
    // We no longer set a local stream status - the backend is the source of truth
    console.log('Stream load failed');
  }, []);

  // Handle refresh button click
  const handleRefresh = useCallback(() => {
    console.log('Manually refreshing camera data and stream...');
    setRefreshKey((prev: number) => prev + 1);
    mutate(); // Force refresh the camera data from the API
  }, [mutate]);

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
      <div className="border-b border-gray-200 px-4 pb-4 pt-2 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {cameraName} - Live Feed
            </h2>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Camera ID: <span className="font-mono">{cameraId}</span>
            </div>
          </div>
          <div>
            <button 
              className="rounded bg-blue-500 px-3 py-1.5 text-sm text-white transition hover:bg-blue-600"
              onClick={handleRefresh}
            >
              Refresh Stream
            </button>
          </div>
        </div>
      </div>
      
      <div className="relative bg-black w-full h-0 pb-[56.25%] overflow-hidden"> {/* 16:9 aspect ratio */}
        <div className="absolute inset-0">
          {/* Only render the stream if shouldRenderStream is true */}
          {/* {shouldRenderStream && effectiveStreamUrl && ( */}
            <OverlayStreamViewer 
              cameraId={cameraId}
              streamUrl={effectiveStreamUrl}
              onError={handleStreamError}
              onSuccess={handleStreamSuccess}
              key={`stream-${refreshKey}`}
              debugMode={false}
          />
          {/* )} */}

          {/* For now show camera stream regardless of camera status because stream has backup feed to display */}
          {shouldRenderStream && !effectiveStreamUrl && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70">
              <div className="text-center text-gray-400">
                <svg
                  className="mx-auto h-16 w-16"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                <p className="mt-2">Stream not available</p>
                <button
                  className="mt-3 rounded bg-blue-500 px-4 py-2 text-white transition hover:bg-blue-600"
                  onClick={handleRefresh}
                >
                  Retry
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LiveCameraTab;
