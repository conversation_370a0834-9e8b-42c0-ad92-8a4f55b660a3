"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import StreamViewer from './StreamViewer';
import { RegionOfInterest } from '@/types/region';
import { ArrowUturnLeftIcon, TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Coordinate } from '@/types/region';

interface DrawingOverlayWithStreamProps {
  cameraId: string;
  onComplete: (coordinates: Coordinate[]) => void;
  onPointsUpdate?: (coordinates: Coordinate[]) => void;
  initialColor?: string;
  regionType?: 'region' | 'line';
  selectedRegions?: RegionOfInterest[];
  onCancel?: () => void;
  initialPoints?: Coordinate[];
}

const DrawingOverlayWithStream: React.FC<DrawingOverlayWithStreamProps> = ({
  cameraId,
  onPointsUpdate,
  initialColor = '#264b96',
  regionType = 'region',
  selectedRegions = [],
  initialPoints = []
}) => {
  const [coordinates, setCoordinates] = useState<Coordinate[]>(initialPoints);
  const [isDrawing] = useState(true);
  const [isActivelyDrawing, setIsActivelyDrawing] = useState(false);
  const [isStreamLoading, setIsStreamLoading] = useState(true);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [currentMousePos, setCurrentMousePos] = useState<Coordinate | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<SVGSVGElement>(null);

  // Handle stream success
  const handleStreamSuccess = useCallback((event?: React.SyntheticEvent<HTMLImageElement>) => {
    // Short delay to ensure the stream is fully rendered
    setTimeout(() => {
      setIsStreamLoading(false);
      
      // If we have the event, get the actual video dimensions
      if (event?.currentTarget) {
        const img = event.currentTarget;
        const dimensions = {
          width: img.naturalWidth || img.clientWidth,
          height: img.naturalHeight || img.clientHeight
        };
        setContainerDimensions(dimensions);
      }
    }, 300);
  }, []);

  // Handle stream error
  const handleStreamError = useCallback(() => {
    setIsStreamLoading(false);
    console.error('Stream failed to load for drawing overlay');
  }, []);

  // Update container dimensions when the component mounts or the window resizes
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerDimensions({ width: clientWidth, height: clientHeight });
      }
    };

    // Initial update
    updateDimensions();

    // Add resize listener
    window.addEventListener('resize', updateDimensions);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Handle click on the overlay to add a point
  const handleOverlayClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!isDrawing || !overlayRef.current) return;
    
    // For trip wire (line), limit to exactly 2 points
    if (regionType === 'line' && coordinates.length >= 2) {
      toast('Trip wire can only have 2 points. Click Complete to save.', {
        icon: '⚠️',
        duration: 3000
      });
      return;
    }
    
    // Get SVG element dimensions
    const svgRect = overlayRef.current.getBoundingClientRect();
    const containerWidth = svgRect.width;
    const containerHeight = svgRect.height;
    
    // Calculate raw pixel coordinates relative to the SVG
    const x = e.clientX - svgRect.left;
    const y = e.clientY - svgRect.top;
    
    // Calculate normalized coordinates (0-1 scale)
    // These are the values we'll send to the backend
    const normalizedX = containerWidth > 0 ? x / containerWidth : 0;
    const normalizedY = containerHeight > 0 ? y / containerHeight : 0;
    
    // Add the point with both raw and normalized coordinates
    const updatedPoints = [...coordinates, { x, y, normalizedX, normalizedY }];
    setCoordinates(updatedPoints);
    setIsActivelyDrawing(true);
    
    // Call the onPointsUpdate callback to update the parent component in real-time
    if (onPointsUpdate) {
      onPointsUpdate(updatedPoints);
    }
    
    // For trip wire, automatically complete when 2 points are added
    if (regionType === 'line' && coordinates.length === 1) {
      // We've just added the second point (index 0 + new point)
      toast.success('Trip wire complete with 2 points');
    }
  };

  // Handle mouse movement
  const handleMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!isDrawing || !overlayRef.current) return;
    
    const svgRect = overlayRef.current.getBoundingClientRect();
    const containerWidth = svgRect.width;
    const containerHeight = svgRect.height;
    
    // Calculate raw pixel coordinates relative to the SVG
    const x = e.clientX - svgRect.left;
    const y = e.clientY - svgRect.top;
    
    // Calculate normalized coordinates (0-1 scale)
    const normalizedX = containerWidth > 0 ? x / containerWidth : 0;
    const normalizedY = containerHeight > 0 ? y / containerHeight : 0;
    
    // Don't show preview line for trip wire once we have 2 points
    if (regionType === 'line' && coordinates.length >= 2) {
      setCurrentMousePos(null);
      return;
    }
    
    // Only update current mouse position if we're actively drawing
    if (isActivelyDrawing || coordinates.length === 0) {
      setCurrentMousePos({ 
        x, 
        y,
        normalizedX,
        normalizedY
      });
    }
  };

  // Undo the last point
  const handleUndoPoint = () => {
    if (coordinates.length > 0) {
      const newCoordinates = [...coordinates];
      newCoordinates.pop();
      setCoordinates(newCoordinates);
      
      // Update the parent component with the new points array
      if (onPointsUpdate) {
        onPointsUpdate(newCoordinates);
      }
      
      toast('Last point removed');
    }
  };

  // Reset points
  const handleReset = () => {
    setCoordinates([]);
    
    // Update the parent component with an empty points array
    if (onPointsUpdate) {
      onPointsUpdate([]);
    }
    
    toast('Canvas cleared');
  };

  // Generate path data for the SVG
  const getPathData = () => {
    if (coordinates.length === 0) return '';
    
    let pathData = `M ${coordinates[0].x} ${coordinates[0].y}`;
    
    for (let i = 1; i < coordinates.length; i++) {
      pathData += ` L ${coordinates[i].x} ${coordinates[i].y}`;
    }
    
    // Connect to the current mouse position only if we're actively drawing
    // and have fewer than 3 points or it's a line type
    if (currentMousePos && isActivelyDrawing) {
      pathData += ` L ${currentMousePos.x} ${currentMousePos.y}`;
    }
    
    // Close the path for regions (only if we're not actively drawing or have enough points)
    if (regionType === 'region' && coordinates.length > 2 && (!isActivelyDrawing || coordinates.length >= 3)) {
      pathData += ' Z';
    }
    
    return pathData;
  };

  return (
    <div className="w-full flex flex-col" ref={containerRef}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-md font-medium">
          Draw on Live Feed
        </h3>
        <div className="flex space-x-1">
          <button
            type="button"
            onClick={handleUndoPoint}
            disabled={coordinates.length === 0}
            className="inline-flex items-center px-2 py-1 text-xs rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo Last Point"
          >
            <ArrowUturnLeftIcon className="h-4 w-4 mr-1" />
            Undo
          </button>
          <button
            type="button"
            onClick={handleReset}
            disabled={coordinates.length === 0}
            className="inline-flex items-center px-2 py-1 text-xs rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Reset All Points"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Reset
          </button>
        </div>
      </div>
      
      <div className="relative border border-gray-300 rounded-md overflow-hidden" style={{ minHeight: '300px' }}>
        {/* Loading indicator - always show until stream is loaded */}
        {isStreamLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-20">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-3 text-sm font-medium text-gray-700">Loading camera feed...</p>
            </div>
          </div>
        )}
        
        {/* The actual camera stream */}
        <div className="relative z-0">
          <StreamViewer 
            streamUrl={`/api/cameras/${cameraId}/stream/`}
            className="w-full h-auto"
            onSuccess={handleStreamSuccess}
            onError={handleStreamError}
          />
        </div>
        
        {/* SVG overlay for drawing */}
        <svg
          ref={overlayRef}
          className={`absolute top-0 left-0 w-full h-full z-10 ${isActivelyDrawing ? 'cursor-crosshair' : 'cursor-default'}`}
          width={containerDimensions.width}
          height={containerDimensions.height}
          onClick={handleOverlayClick}
          onMouseMove={handleMouseMove}
          onMouseLeave={() => isActivelyDrawing && setCurrentMousePos(null)}
          onMouseEnter={(e: React.MouseEvent<SVGSVGElement>) => {
            if (isActivelyDrawing && overlayRef.current) {
              const svgRect = overlayRef.current.getBoundingClientRect();
              const x = e.clientX - svgRect.left;
              const y = e.clientY - svgRect.top;
              const normalizedX = svgRect.width > 0 ? x / svgRect.width : 0;
              const normalizedY = svgRect.height > 0 ? y / svgRect.height : 0;
              setCurrentMousePos({ x, y, normalizedX, normalizedY });
            }
          }}
        >
          {/* Draw the selected regions from the multiselect */}
          {selectedRegions.map((region, regionIndex) => {
            // For trip wires (line)
            if (region.roi_type === 'line') {
              // We need at least 2 points for a line
              if (region.coordinates.length < 2) return null;
              
              // Create line path
              return (
                <g key={`region-${region.id || regionIndex}`}>
                  <line
                    x1={region.coordinates[0].x}
                    y1={region.coordinates[0].y}
                    x2={region.coordinates[1].x}
                    y2={region.coordinates[1].y}
                    stroke={initialColor || '#FF5733'} // Use the layer color passed via props
                    strokeWidth="3"
                    strokeDasharray="5,5"
                  />
                  {/* Add dots at endpoints */}
                  <circle cx={region.coordinates[0].x} cy={region.coordinates[0].y} r="4" fill={initialColor || '#FF5733'} />
                  <circle cx={region.coordinates[1].x} cy={region.coordinates[1].y} r="4" fill={initialColor || '#FF5733'} />
                  {/* Add a small label */}
                  <text
                    x={((region.coordinates[0].x + region.coordinates[1].x) / 2) + 5}
                    y={((region.coordinates[0].y + region.coordinates[1].y) / 2) - 5}
                    fill="white"
                    stroke="black"
                    strokeWidth="0.5"
                    fontSize="10px"
                  >
                    {region.name || `Trip Wire ${regionIndex + 1}`}
                  </text>
                </g>
              );
            }
            
            // For polygons (regions)
            if (region.roi_type === 'region') {
              // We need at least 3 points for a region
              if (region.coordinates.length < 3) return null;
              
              // Convert normalized coordinates to pixels if needed
              const convertedPoints = region.coordinates.map(point => {
                // Check if we have x,y or normalized coordinates
                if (typeof point.x === 'number' && typeof point.y === 'number') {
                  return point;
                } else {
                  // Fall back to raw coordinates
                  return point;
                }
              });
              
              // Create polygon path
              const pathData = convertedPoints.map((point, idx) => 
                `${idx === 0 ? 'M' : 'L'}${point.x},${point.y}`
              ).join(' ') + ' Z';
              
              return (
                <g key={`region-${region.id || regionIndex}`}>
                  <path
                    d={pathData}
                    fill={`${initialColor || '#3B82F6'}40`} // Semi-transparent fill
                    stroke={initialColor || '#3B82F6'}
                    strokeWidth="2"
                  />
                  {/* Add a small label */}
                  <text
                    x={region.coordinates[0].x + 5}
                    y={region.coordinates[0].y - 5}
                    fill="white"
                    stroke="black"
                    strokeWidth="0.5"
                    fontSize="10px"
                  >
                    {region.name || `Region ${regionIndex + 1}`}
                  </text>
                </g>
              );
            }
            
            return null;
          })}
          
          {/* Draw the shape path for the current drawing */}
          <path
            d={getPathData()}
            fill={regionType === 'region' ? `${initialColor}40` : 'none'}
            stroke={initialColor}
            strokeWidth="2"
          />
          
          {/* Draw points */}
          {coordinates.map((coordinate, index) => (
            <circle
              key={index}
              cx={coordinate.x}
              cy={coordinate.y}
              r="5"
              fill={index === 0 ? '#4ADE80' : initialColor}
              stroke="#FFFFFF"
              strokeWidth="1"
            />
          ))}
          
          {/* Draw point coordinates */}
          {coordinates.map((coordinate, index) => (
            <text
              key={`text-${index}`}
              x={coordinate.x + 10}
              y={coordinate.y - 10}
              fill="white"
              stroke="black"
              strokeWidth="0.5"
              fontSize="12px"
              fontFamily="Arial"
            >
              {coordinate.normalizedX !== undefined && coordinate.normalizedY !== undefined ? 
                `(${(coordinate.normalizedX * 100).toFixed(1)}%, ${(coordinate.normalizedY * 100).toFixed(1)}%)` : 
                `(${Math.round(coordinate.x)}, ${Math.round(coordinate.y)})`
              }
            </text>
          ))}
          
          {/* Draw current mouse position indicator only when actively drawing and not exceeding point limits */}
          {currentMousePos && isDrawing && isActivelyDrawing && !(regionType === 'line' && coordinates.length >= 2) && (
            <circle
              cx={currentMousePos.x}
              cy={currentMousePos.y}
              r="5"
              fill="#FBBF24"
              stroke="#FFFFFF"
              strokeWidth="1"
            />
          )}
        </svg>
      </div>
      
      {/* Instructions and coordinates display */}
      <div className="mt-3">
        <div className="text-xs text-gray-500">
          <span className="font-medium">Points:</span> {coordinates.length}{' '}
          <span className="text-xs">(min 2 for line, min 3 for region)</span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Once you have added your points, click Create {regionType === 'line' ? 'Trip Wire' : 'Area'} to create the {regionType === 'line' ? 'trip wire' : 'area'} to be added for this layer.
        </div>
      </div>
    </div>
  );
};

export default DrawingOverlayWithStream;
