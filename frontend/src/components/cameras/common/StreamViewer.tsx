"use client";

import React, { useEffect, useRef, useState } from "react";

interface StreamViewerProps {
  streamUrl: string;
  className?: string;
  onError?: () => void;
  onSuccess?: () => void;
  debugMode?: boolean; // Optional debug mode flag
  isPlayback?: boolean; // Flag to indicate if this is a playback stream
  playbackTime?: Date; // The timestamp for playback (when in playback mode)
}

/**
 * A minimal MJPEG stream viewer component
 * Displays a camera stream from the backend with minimal overhead
 */
const StreamViewer: React.FC<StreamViewerProps> = ({
  streamUrl,
  className = "w-full h-full object-contain",
  onError,
  onSuccess,
  debugMode = false,
  isPlayback = false,
  playbackTime,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [fullUrl, setFullUrl] = useState("");
  const imgRef = useRef<HTMLImageElement>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Set up the URL when streamUrl changes
  useEffect(() => {
    // Don't attempt to load if no stream URL is provided
    if (!streamUrl) {
      if (debugMode) console.log("No stream URL provided");
      setIsError(true);
      return;
    }

    // Reset states when URL changes
    setIsLoading(true);
    setIsError(false);

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    // Construct the full URL with backend domain if needed
    let url = streamUrl;
    if (url.startsWith("/")) {
      // Get backend URL from environment or use default fallback
      let backendUrl = process.env.NEXT_PUBLIC_API_DOMAIN;
      if (!backendUrl) {
        console.error("NEXT_PUBLIC_API_DOMAIN is not defined");
        setIsError(true);
        return;
      }

      // Extract base URL and port
      const urlParts = backendUrl.split(":");
      if (urlParts.length > 2) {
        const basePort = parseInt(urlParts[2]);
        backendUrl = `${urlParts[0]}:${urlParts[1]}:${basePort}`;
      }

      url = `${backendUrl}${url}`;
      if (debugMode) console.log("Full stream URL:", url);
    }

    // Handle URL construction based on stream type (regular vs. playback)
    let urlWithCache: string;

    if (isPlayback) {
      // For playback streams, ensure we have the correct start_time parameter
      // This is typically already in the streamUrl, but we ensure it's formatted correctly
      if (debugMode) console.log("Preparing playback stream URL", url);

      // Add cache buster to prevent browser caching
      const cacheBuster = `cacheBuster=${Date.now()}`;
      const separator = url.includes("?") ? "&" : "?";
      urlWithCache = `${url}${separator}${cacheBuster}`;

      // Log additional context in debug mode
      if (debugMode && playbackTime) {
        console.log("Playback time:", playbackTime.toISOString());
      }
    } else {
      // Regular stream - just add cache buster
      const cacheBuster = `cacheBuster=${Date.now()}`;
      const separator = url.includes("?") ? "&" : "?";
      urlWithCache = `${url}${separator}${cacheBuster}`;
    }

    // Update the URL
    setFullUrl(urlWithCache);

    if (debugMode) console.log(`Stream URL prepared: ${urlWithCache}`);
  }, [streamUrl, debugMode]);

  // Cleanup function to stop the stream when component unmounts
  useEffect(() => {
    // Store ref value to avoid the lint warning about changing ref in cleanup
    const imgElement = imgRef.current;

    return () => {
      // Clear any pending retry timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      // Clear the image source to stop the stream using the stored ref
      if (imgElement) {
        imgElement.src = "";
      }
      // Reset states
      setIsLoading(true);
      setIsError(false);
      setFullUrl("");
      if (debugMode) console.log("StreamViewer cleanup completed");
    };
  }, [debugMode]);

  // Handle successful image load
  const handleImageLoaded = () => {
    setIsLoading(false);
    setIsError(false);
    if (onSuccess) onSuccess();
    if (debugMode) console.log("Stream loaded successfully");
  };

  // Handle image loading error with retry logic
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    setIsError(true);
    setIsLoading(false);
    if (debugMode)
      console.error(
        "Stream failed to load. URL attempted:",
        e.currentTarget.src
      );

    // Implement retry logic with exponential backoff
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    retryTimeoutRef.current = setTimeout(() => {
      if (debugMode) console.log("Retrying stream connection...");
      refreshStream();
    }, 2000); // Retry after 2 seconds

    if (onError) onError();
  };

  // Simple way to try refreshing the stream
  const refreshStream = () => {
    setIsLoading(true);
    setIsError(false);

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    // Update the URL with a new cache buster
    const cacheBuster = `cacheBuster=${Date.now()}`;
    const baseUrl = fullUrl.split("cacheBuster=")[0];
    const separator = baseUrl.includes("?") ? "&" : "?";
    setFullUrl(`${baseUrl}${separator}${cacheBuster}`);
    if (debugMode) console.log("Refreshing stream with new URL");
  };

  return (
    <div className="relative h-full w-full">
      {/* 
        Stream image with improved attributes to reduce tearing
        Note: Using standard <img> tag instead of Next.js Image component for direct stream compatibility.
        This is intentional as Next's Image component is not ideal for dynamic MJPEG streams.
      */}
      <img
        ref={imgRef}
        className={className}
        src={fullUrl || ''}
        alt="Camera Stream"
        onLoad={handleImageLoaded}
        onError={handleImageError}
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        style={{
          display: isLoading ? "none" : "block",
          objectFit: "contain",
          aspectRatio: "16/9", // This will maintain a 16:9 aspect ratio
          imageRendering: "auto", // Let browser handle image rendering
        }}
        // Additional attributes to improve MJPEG handling
        decoding="async"
        loading="eager"
      />

      {/* Loading indicator */}
      {isLoading && !isError && (
        <div className="bg-opacity-30 absolute inset-0 flex items-center justify-center bg-black">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
        </div>
      )}

      {/* Error message */}
      {isError && (
        <div className="bg-opacity-60 absolute inset-0 flex flex-col items-center justify-center bg-black p-4 text-white">
          <div className="mb-4 text-center">
            <svg
              className="mx-auto h-12 w-12 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="mt-2 text-xl font-medium">
              {isPlayback ? "Playback Unavailable" : "Stream Unavailable"}
            </h3>
            <p className="mt-1 text-sm text-gray-400">
              {isPlayback
                ? "Unable to connect to camera playback. The requested time might be unavailable."
                : "Unable to connect to camera stream"}
            </p>
            {debugMode && (
              <p className="mt-2 text-xs text-gray-500">
                URL: {fullUrl}
                {isPlayback && playbackTime && (
                  <>
                    <br />
                    Time: {playbackTime.toISOString()}
                  </>
                )}
              </p>
            )}
          </div>
          <button
            onClick={refreshStream}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
};

export default StreamViewer;
