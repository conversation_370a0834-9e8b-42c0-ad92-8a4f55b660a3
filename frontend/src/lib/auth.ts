import { cache } from "react";
import { cookies } from "next/headers";

import { CustomResponse } from "@/types/response";
import { User } from "@/types/user";
import api from "@/services/api";

export const validateRequest: () => Promise<
  | { session: true; user: User; is_approved: boolean }
  | { session: false; user: null }
> = cache(async () => {
  const cookieStore = await cookies();
  const authCookie = cookieStore.get(process.env.SESSION_COOKIE_NAME!);

  try {
    const response = await api.get("/user/user-session/v1/get_user_info/", {
      headers: {
        ...(authCookie
          ? { Cookie: `${authCookie.name}=${authCookie.value}` }
          : {}),
      },
    });

    const { data: user } = response.data as CustomResponse<User>;

    return {
      session: true,
      user,
      is_approved: user.is_approved,
    };
  } catch {
    return {
      session: false,
      user: null,
    };
  }
});
