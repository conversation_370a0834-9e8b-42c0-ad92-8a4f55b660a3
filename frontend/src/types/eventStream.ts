import { CameraEvent } from '@/types/event';

// Basic types for SSE connection
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected';

// Simplified stream event types
export interface StreamEvent {
  type: 'new_event' | 'connected' | 'error' | 'heartbeat' | 'event_acknowledged';
  data?: Record<string, unknown>;
  error?: string;
  timestamp?: number;
}

// Simplified callback types
export type EventCallback = (event: CameraEvent) => void;
export type StatusCallback = (status: ConnectionStatus) => void;
export type ErrorCallback = (error: string) => void;

// EventStream service interface
export interface EventStreamService {
  connect: () => void;
  disconnect: () => void;
  onStatusChange: (callback: StatusCallback) => void;
  onNewEvent: (callback: EventCallback) => void;
  onError: (callback: ErrorCallback) => void;
}
