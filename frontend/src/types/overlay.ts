// Permanent Camera Overlay type definitions

export type OverlayCoordinates = {
    // Array of points defining the overlay shape
    absolute: [number, number][]; // Absolute coordinates (pixel position)
    normalized: [number, number][]; // Normalized coordinates (relative position between 0-1)
  };
  
  export type PermanentOverlay = {
    id: string;
    name: string;
    description?: string;
    camera: string;  // Camera ID
    camera_name?: string;  // Camera name (included in API response)
    coordinates: OverlayCoordinates;
    text?: string; // Text to display in the overlay (if applicable)
    color: string; // Color of the overlay (hex code or color name)
    created_at: string;
    updated_at: string;
  };
  
  // Props shared between overlay components
  export interface OverlayComponentProps {
    overlayId: string;
    overlay?: PermanentOverlay | null;
  }
  