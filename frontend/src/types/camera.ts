// Camera status enum
export type CameraStatus = "online" | "offline" | "error";

export type Camera = {
  id: string;
  name: string;
  location?: string;
  status?: CameraStatus;
  description?: string;
  rtsp_url?: string;
  stream_url?: string;
  stream_id?: string;
  thumbnail?: string;
  created_at: string;
  updated_at: string;
  last_status_check?: string;
  stream_fps?: number;
  encoding_format?: string;
  enabled: boolean;
  dynamic_frame_rate?: boolean;
};

// Props shared between components
export interface CameraComponentProps {
  cameraId: string;
  camera?: Camera | null;
}

// Analytics data interface
export interface AnalyticsData {
  totalEvents: number;
  lastEvent: string;
  eventsToday: number;
  eventsThisWeek: number;
  byType: {
    [key: string]: number;
  };
  heatmapData: number[][];
}
