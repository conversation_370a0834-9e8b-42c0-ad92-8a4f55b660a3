export interface Coordinate {
  x: number;
  y: number;
  normalizedX: number;
  normalizedY: number;
}

// Available keypoints for pose detection
export type KeypointType = 
  | 'Nose'
  | 'Left Eye'
  | 'Right Eye'
  | 'Left Ear'
  | 'Right Ear'
  | 'Left Shoulder'
  | 'Right Shoulder'
  | 'Left Elbow'
  | 'Right Elbow'
  | 'Left Wrist'
  | 'Right Wrist'
  | 'Left Hip'
  | 'Right Hip'
  | 'Left Knee'
  | 'Right Knee'
  | 'Left Ankle'
  | 'Right Ankle';

// Requirements for triggering alerts
export type RequirementType = 
  | 'above the line' 
  | 'below the line' 
  | 'inside the region' 
  | 'outside the region';

// Alert categories
export type AlertCategoryType = 'Warning' | 'Critical';

export interface RegionOfInterest {
  id?: string;
  name: string;
  description?: string;
  coordinates: Array<Coordinate>;
  roi_type: 'line' | 'region';
  is_active?: boolean;
  alerts_category?: AlertCategoryType;
  pose_keypoints?: KeypointType[];
  requirement?: RequirementType;
  criteria?: {
    pose_keypoints?: KeypointType[];
    requirements?: RequirementType;
  };
  created_at?: string;
  updated_at?: string;
}

export interface RegionRequest {
  name: string;
  description?: string;
  coordinates: Array<Coordinate>;
  roi_type: 'line' | 'region';
  is_active?: boolean;
  alerts_category?: AlertCategoryType;
  pose_keypoints?: KeypointType[];
  requirement?: RequirementType;
}
