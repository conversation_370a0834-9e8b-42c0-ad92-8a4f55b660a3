export interface Frame {
    id: string;
    timestamp: string;
    image_url: string;
    thumbnail_url?: string;
    frame_bytes?: string; // Base64 encoded image data
    width?: number;       // Original image width
    height?: number;      // Original image height
    format?: string;      // Image format (e.g., 'jpeg', 'png')
    camera_id?: string;   // Reference to the source camera
    event_id?: string;    // Reference to the associated event
    metadata?: {          // Optional metadata about the frame
      fps?: number;       // Frames per second of source video
      frame_number?: number; // Position in video sequence
      exposure?: number;  // Camera exposure settings
      created_at?: string; // When the frame was created/processed
    };
  }