import { BoundingBox } from './event'

export type NotificationType = "info" | "success" | "warning" | "error";

export interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  timestamp: Date;
  read: boolean;
  camera?: string;
  eventId?: string;
  boundingBoxes?: BoundingBox[];
  confidence?: number;
  eventType?: string;
  severity?: string;
  is_reviewed?: boolean;
}
