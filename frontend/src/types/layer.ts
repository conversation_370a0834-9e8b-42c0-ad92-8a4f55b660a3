import { RegionOfInterest } from './region';

// Define numeric layer types to match backend model
export type LayerType = 1 | 2 | 3;

// Map of layer type to human-readable names
export const LAYER_TYPE_NAMES: Record<LayerType, string> = {
  1: 'Layer Type 1',
  2: 'Layer Type 2',
  3: 'Layer Type 3'
};

// Define a type for layer option values
type LayerOptionValue = string | number | boolean | null | string[] | { [key: string]: LayerOptionValue };

/**
 * Response type for the layers API
 */
export interface LayersApiResponse {
  configurations: CameraLayerConfiguration[];
  base_layers: Layer[];
}

// Define a dependency request with conditions
export interface DependencyRequest {
  dependency_id: string;
  conditions: Record<string, unknown>;
}

export interface CameraLayerConfigurationRequest {
  name: string;
  camera_id: string;
  layer_id: string;
  enabled?: boolean;
  color?: string;
  region_ids: string[]; // Updated to match what the backend actually expects
  dependencies: DependencyRequest[];
  configuration: Record<string, LayerOptionValue>;
}

export interface CameraLayerConfiguration {
  id: string;
  name: string;
  camera: { 
    id: string; 
    name: string,
    location: string, 
  } | string;
  layer?: Layer | null;
  enabled: boolean;
  color: string;
  is_default: boolean;
  regions: RegionOfInterest[];
  dependencies: CameraLayerConfigurationDependency[];
  configuration: Record<string, LayerOptionValue>;
  created_at: string;
  updated_at: string;
}

export interface CameraLayerConfigurationDependency {
  id: string;
  current_layer?: string | CameraLayerConfiguration;
  dependency_layer: string | CameraLayerConfiguration;
  conditions: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Layer {
  id: string;
  name: string;
  layer_type: LayerType;
  function_name?: string;
  cv_model?: { id: string; model_type: string };
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

// Computer Vision Model type
export interface ComputerVisionModel {
  id: string;
  model_type: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// Available function type
export interface AvailableFunction {
  name: string;
  description?: string;
  layer_type: LayerType[];
}
