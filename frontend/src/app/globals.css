@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--foreground);
  --color-popover: var(--card);
  --color-popover-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--muted);
  --color-accent-foreground: var(--foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--border);
  --color-ring: var(--primary);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--card);
  --color-sidebar-foreground: var(--foreground);
  --color-sidebar-primary: var(--primary);
  --color-sidebar-primary-foreground: var(--primary-foreground);
  --color-sidebar-accent: var(--muted);
  --color-sidebar-accent-foreground: var(--foreground);
  --color-sidebar-border: var(--border);
  --color-sidebar-ring: var(--primary);
}

:root {
  --radius: 0.5rem;
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --primary: oklch(0.596 0.145 163.225);
  --primary-foreground: oklch(0.969 0.015 12.422);
  --secondary: oklch(0.74 0.127468 207.9215);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --primary: oklch(0.596 0.145 163.225);
  --primary-foreground: oklch(0.969 0.015 12.422);
  --secondary: oklch(0.74 0.127468 207.9215);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    font-family: Arial, Helvetica, sans-serif;
    @apply bg-background text-foreground;
  }

  html {
    @apply scroll-smooth;
  }

  ::selection {
    @apply bg-primary text-primary-foreground;
  }

  ::-webkit-scrollbar {
    @apply size-3;
  }

  ::-webkit-scrollbar-track {
    @apply border-border bg-foreground/5 border border-solid;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-foreground/20 hover:bg-foreground/30 active:bg-foreground/40 rounded-lg border-[3px] border-solid border-transparent bg-clip-content transition;
  }

  ::-webkit-scrollbar-corner {
    @apply bg-foreground/5;
  }

  :-webkit-autofill,
  :-webkit-autofill:hover,
  :-webkit-autofill:focus,
  :-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: inherit;
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px transparent;
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}
