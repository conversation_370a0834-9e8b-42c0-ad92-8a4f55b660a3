"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft, ShieldAlert } from "lucide-react";

export default function NotApprovedPage() {
  const router = useRouter();

  const handleGoBack = () => {
    router.push("/");
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-900">
      <div className="w-full max-w-md rounded-lg bg-white p-8 text-center shadow-lg dark:bg-gray-800">
        <div className="mb-6 flex justify-center">
          <ShieldAlert className="h-16 w-16 text-red-500" />
        </div>
        <h1 className="mb-2 text-2xl font-bold dark:text-white">
          Account Not Approved
        </h1>
        <p className="mb-6 text-gray-600 dark:text-gray-300">
          Your account is pending administrator approval.
        </p>
        <div className="mb-8">
          <p className="mb-4 text-gray-700 dark:text-gray-200">
            Your Google login was successful, but you need administrator
            approval before you can access SquirrelSentry.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your system administrator to get your account
            approved.
          </p>
        </div>
        <button
          onClick={handleGoBack}
          className="mx-auto flex items-center justify-center gap-2 rounded-md border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-100 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700"
        >
          <ArrowLeft className="h-4 w-4" />
          Go Back
        </button>
      </div>
    </div>
  );
}
