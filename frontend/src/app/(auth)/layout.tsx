import React from "react";
import { redirect } from "next/navigation";
import { Bar<PERSON>hart3, <PERSON>, Zap } from "lucide-react";

import { validateRequest } from "@/lib/auth";

const Layout = async ({ children }: { children: React.ReactNode }) => {
  const { session } = await validateRequest();

  return session ? (
    redirect("/")
  ) : (
    <main className="grid min-h-svh flex-1 lg:grid-cols-2">
      <div className="from-primary via-primary to-primary/80 text-primary-foreground relative z-0 hidden flex-col items-center justify-center gap-8 overflow-hidden bg-gradient-to-br p-8 text-center lg:flex">
        <div className="relative z-10 flex flex-1 flex-col items-center justify-center gap-8">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">iSecurity</h1>
            <p className="text-primary-foreground/90 max-w-md text-lg leading-relaxed">
              SMRT Advanced Video Analytics
            </p>
          </div>

          <div className="grid grid-cols-3 gap-6">
            <div className="flex flex-col items-center gap-2">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/10 backdrop-blur-sm">
                <Eye className="size-6" />
              </div>
              <span className="text-sm font-medium">Smart Vision</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/10 backdrop-blur-sm">
                <BarChart3 className="size-6" />
              </div>
              <span className="text-sm font-medium">Analytics</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/10 backdrop-blur-sm">
                <Zap className="size-6" />
              </div>
              <span className="text-sm font-medium">Real-time</span>
            </div>
          </div>
        </div>

        <div className="relative z-10 space-y-1.5">
          <p className="text-primary-foreground/70 text-sm font-bold">
            Version 1.0.2
          </p>

          <p className="text-primary-foreground/70 text-sm">
            © {new Date().getFullYear()} SquirrelSentry - All rights reserved.
          </p>
        </div>

        <div className="pointer-events-none absolute inset-0 isolate -z-10 bg-[url('/images/pattern.svg')] opacity-10" />
      </div>

      <div className="flex flex-col gap-4 p-6">
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-sm">{children}</div>
        </div>
      </div>
    </main>
  );
};

export default Layout;
