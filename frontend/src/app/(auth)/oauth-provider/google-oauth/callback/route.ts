import { NextRequest, NextResponse } from "next/server";

import api from "@/services/api";

export const GET = async (req: NextRequest) => {
  const host = req.headers.get("x-forwarded-host") || req.headers.get("host");
  const protocol = req.headers.get("x-forwarded-proto") || "http";
  const domain = `${protocol}://${host}`;

  const { searchParams } = new URL(req.url);
  const queryParams = searchParams.toString();

  try {
    const apiResponse = await api.get(
      `/oauth-provider/google-oauth/v1/oauth2callback?${queryParams}`
    );

    const setCookieHeader = apiResponse.headers["set-cookie"];
    const response = NextResponse.redirect(
      new URL("/dashboard/cameras", domain)
    );

    if (setCookieHeader) {
      const cookies = Array.isArray(setCookieHeader)
        ? setCookieHeader.join(", ")
        : setCookieHeader;
      response.headers.set("Set-Cookie", cookies);
    }

    return response;
  } catch (error) {
    console.log(error);
    return NextResponse.redirect(new URL("/login", domain));
  }
};
