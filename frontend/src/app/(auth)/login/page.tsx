import React from "react";
import { Metadata } from "next";

import LoginForm from "@/components/auth/LoginForm";

export const metadata: Metadata = {
  title: "Login",
};

const Login = () => (
  <div className="flex flex-col gap-6 w-full max-w-sm">
    <div className="flex flex-col items-center gap-1 text-center">
      <h1 className="text-2xl font-bold">Welcome Back</h1>
      <p className="text-muted-foreground text-sm">
        Login to SquirrelSentry to access your account
      </p>
    </div>

    {/* Username/password login form */}
    <LoginForm />
    
    {/* <div className="relative">
      <div className="absolute inset-0 flex items-center">
        <Separator className="w-full" />
      </div>
      <div className="relative flex justify-center text-xs uppercase">
        <span className="bg-background px-2 text-muted-foreground">
          Or continue with
        </span>
      </div>
    </div>

    {/* <GoogleOAuthButton /> */}
  </div>
);

export default Login;
