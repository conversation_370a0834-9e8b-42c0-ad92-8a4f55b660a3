"use client";

import React from "react";

import LogoutButton from "@/components/auth/LogoutButton";
import { useAuth } from "@/contexts/auth";

const Home = () => {
  const { user } = useAuth();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-4">
      <div className="text-2xl font-bold">Home</div>
      {user && (
        <p className="text-center">
          Logged in as:
          <br />
          {user.first_name} {user.last_name} ({user.email})
        </p>
      )}
      <LogoutButton />
    </div>
  );
};

export default Home;
