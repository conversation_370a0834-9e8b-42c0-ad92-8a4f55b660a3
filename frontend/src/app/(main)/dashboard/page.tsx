"use client";

import "./dashboard.css";

import { useState } from "react";
import Image from "next/image";

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data for the dashboard
  const analyticsData = {
    totalVisitors: 12458,
    todayVisitors: 342,
    conversionRate: "3.8%",
    avgTimeSpent: "4m 23s",
    hotspots: [
      { id: 1, name: "Entrance Display", activity: "High", conversion: "5.2%" },
      { id: 2, name: "Central Aisle", activity: "Medium", conversion: "3.1%" },
      {
        id: 3,
        name: "Checkout Area",
        activity: "Very High",
        conversion: "7.8%",
      },
      {
        id: 4,
        name: "Electronics Section",
        activity: "High",
        conversion: "4.5%",
      },
    ],
    recentAlerts: [
      {
        id: 1,
        type: "Security",
        message: "Unusual activity detected in Zone B",
        time: "2 mins ago",
        severity: "high",
      },
      {
        id: 2,
        type: "Analytics",
        message: "Traffic spike at north entrance",
        time: "15 mins ago",
        severity: "medium",
      },
      {
        id: 3,
        type: "System",
        message: "Camera 3 connection restored",
        time: "1 hour ago",
        severity: "low",
      },
    ],
    performanceMetrics: [
      { metric: "Dwell Time", value: "3m 45s", change: "+12%", trend: "up" },
      { metric: "Engagement Rate", value: "42%", change: "+5%", trend: "up" },
      { metric: "Conversion", value: "3.8%", change: "-2%", trend: "down" },
      { metric: "Return Visitors", value: "28%", change: "+8%", trend: "up" },
    ],
  };

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      {/* Dashboard Header */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Retail Analytics Command Center
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Advanced video analytics and behavioral insights for optimized
            retail performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700">
              <span>Time Period: Last 7 Days</span>
              <svg
                className="-mr-0.5 ml-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
          <div className="relative">
            <button className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700">
              <span>Location: All Stores</span>
              <svg
                className="-mr-0.5 ml-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
          <button className="rounded-md p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Active Filters */}
      <div className="mb-6 flex flex-wrap items-center gap-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Active Filters:
        </span>
        <span className="inline-flex items-center rounded-full bg-emerald-100 px-3 py-0.5 text-sm font-medium text-emerald-800 dark:bg-emerald-800 dark:text-emerald-100">
          High Traffic Areas
          <button
            type="button"
            className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 rounded-full text-emerald-600 hover:text-emerald-800 focus:outline-none dark:text-emerald-300 dark:hover:text-emerald-100"
          >
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </span>
        <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-0.5 text-sm font-medium text-blue-800 dark:bg-blue-800 dark:text-blue-100">
          Conversion {">"}3%
          <button
            type="button"
            className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 rounded-full text-blue-600 hover:text-blue-800 focus:outline-none dark:text-blue-300 dark:hover:text-blue-100"
          >
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </span>
        <span className="inline-flex items-center rounded-full bg-purple-100 px-3 py-0.5 text-sm font-medium text-purple-800 dark:bg-purple-800 dark:text-purple-100">
          Weekend Data
          <button
            type="button"
            className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 rounded-full text-purple-600 hover:text-purple-800 focus:outline-none dark:text-purple-300 dark:hover:text-purple-100"
          >
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </span>
      </div>

      {/* Dashboard Tabs */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("overview")}
            className={`border-b-2 px-1 pb-4 text-sm font-medium ${
              activeTab === "overview"
                ? "border-emerald-500 text-emerald-600 dark:border-emerald-400 dark:text-emerald-300"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab("analytics")}
            className={`border-b-2 px-1 pb-4 text-sm font-medium ${
              activeTab === "analytics"
                ? "border-emerald-500 text-emerald-600 dark:border-emerald-400 dark:text-emerald-300"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            Analytics
          </button>
          <button
            onClick={() => setActiveTab("hotspots")}
            className={`border-b-2 px-1 pb-4 text-sm font-medium ${
              activeTab === "hotspots"
                ? "border-emerald-500 text-emerald-600 dark:border-emerald-400 dark:text-emerald-300"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            Hotspots
          </button>
          <button
            onClick={() => setActiveTab("alerts")}
            className={`border-b-2 px-1 pb-4 text-sm font-medium ${
              activeTab === "alerts"
                ? "border-emerald-500 text-emerald-600 dark:border-emerald-400 dark:text-emerald-300"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            Alerts
          </button>
        </nav>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total Visitors */}
        <div className="dashboard-card overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 rounded-md bg-emerald-100 p-3 dark:bg-emerald-800">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-emerald-600 dark:text-emerald-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Customer Footfall
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900 dark:text-white">
                      {analyticsData.totalVisitors.toLocaleString()}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6 dark:bg-gray-700">
            <div className="text-sm">
              <span className="font-medium text-emerald-600 dark:text-emerald-300">
                +{analyticsData.todayVisitors} today (12.4% ↑)
              </span>
            </div>
          </div>
        </div>

        {/* Conversion Rate */}
        <div className="dashboard-card overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 rounded-md bg-blue-100 p-3 dark:bg-blue-800">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-600 dark:text-blue-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Conversion Rate
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900 dark:text-white">
                      {analyticsData.conversionRate}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6 dark:bg-gray-700">
            <div className="text-sm">
              <span className="font-medium text-red-600 dark:text-red-400">
                -2% from last week
              </span>
            </div>
          </div>
        </div>

        {/* Avg Time Spent */}
        <div className="dashboard-card overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 rounded-md bg-purple-100 p-3 dark:bg-purple-800">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-purple-600 dark:text-purple-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Avg Dwell Duration
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900 dark:text-white">
                      {analyticsData.avgTimeSpent}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6 dark:bg-gray-700">
            <div className="text-sm">
              <span className="font-medium text-emerald-600 dark:text-emerald-300">
                +12% from last week
              </span>
            </div>
          </div>
        </div>

        {/* Security Score */}
        <div className="dashboard-card overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 rounded-md bg-amber-100 p-3 dark:bg-amber-800">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-amber-600 dark:text-amber-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Security Score
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900 dark:text-white">
                      92/100
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6 dark:bg-gray-700">
            <div className="text-sm">
              <span className="font-medium text-emerald-600 dark:text-emerald-300">
                +3 points from last week
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Sections */}
      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Store Layout & Hotspots */}
        <div className="dashboard-card rounded-lg bg-white shadow lg:col-span-2 dark:bg-gray-800">
          <div className="border-b border-gray-200 px-4 py-5 sm:px-6 dark:border-gray-700">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Spatial Analytics & Engagement Zones
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Real-time behavioral mapping with AI-powered insights
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <div className="relative h-80 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
              {/* Mall floor plan with heatmap */}
              <div className="absolute inset-0 bg-white dark:bg-gray-800">
                <div className="relative h-full w-full">
                  {/* Floor plan background image */}
                  <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                    style={{
                      backgroundImage: `url('/images/office.jpg')`,
                      opacity: 0.7,
                    }}
                  ></div>

                  {/* Heatmap overlay */}
                  <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat mix-blend-multiply"
                    style={{
                      backgroundImage: `url('/images/office.jpg')`,
                      opacity: 0.8,
                    }}
                  ></div>

                  {/* Store labels */}
                  <div className="absolute top-[15%] left-[20%] rounded bg-white px-2 py-1 text-xs font-medium shadow-sm dark:bg-gray-800">
                    Main Entrance
                  </div>
                  <div className="absolute top-[40%] left-[50%] rounded bg-white px-2 py-1 text-xs font-medium shadow-sm dark:bg-gray-800">
                    Central Atrium
                  </div>
                  <div className="absolute top-[70%] left-[75%] rounded bg-white px-2 py-1 text-xs font-medium shadow-sm dark:bg-gray-800">
                    Electronics
                  </div>
                  <div className="absolute top-[25%] left-[80%] rounded bg-white px-2 py-1 text-xs font-medium shadow-sm dark:bg-gray-800">
                    Fashion
                  </div>
                  <div className="absolute top-[80%] left-[30%] rounded bg-white px-2 py-1 text-xs font-medium shadow-sm dark:bg-gray-800">
                    Food Court
                  </div>

                  {/* Legend */}
                  <div className="absolute right-2 bottom-2 rounded-md bg-white p-2 text-xs shadow-md dark:bg-gray-800">
                    <div className="mb-1 font-medium">Traffic Intensity</div>
                    <div className="mb-1 flex items-center">
                      <div className="mr-1 h-3 w-3 bg-blue-500"></div>
                      <span>Low</span>
                    </div>
                    <div className="mb-1 flex items-center">
                      <div className="mr-1 h-3 w-3 bg-green-500"></div>
                      <span>Medium</span>
                    </div>
                    <div className="mb-1 flex items-center">
                      <div className="mr-1 h-3 w-3 bg-yellow-500"></div>
                      <span>High</span>
                    </div>
                    <div className="flex items-center">
                      <div className="mr-1 h-3 w-3 bg-red-500"></div>
                      <span>Very High</span>
                    </div>
                  </div>

                  {/* Interactive hotspots */}
                  <div className="bg-opacity-50 hotspot-pulse absolute top-[15%] left-[20%] h-8 w-8 rounded-full bg-red-500"></div>
                  <div className="bg-opacity-50 hotspot-pulse absolute top-[40%] left-[50%] h-10 w-10 rounded-full bg-yellow-500"></div>
                  <div className="bg-opacity-50 hotspot-pulse absolute top-[70%] left-[75%] h-8 w-8 rounded-full bg-green-500"></div>
                  <div className="bg-opacity-50 hotspot-pulse absolute top-[25%] left-[80%] h-6 w-6 rounded-full bg-blue-500"></div>
                  <div className="bg-opacity-50 hotspot-pulse absolute top-[80%] left-[30%] h-12 w-12 rounded-full bg-red-500"></div>
                </div>
              </div>

              {/* Controls overlay */}
              <div className="absolute top-2 left-2 flex space-x-1">
                <button className="rounded bg-white p-1 shadow-sm dark:bg-gray-800">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-600 dark:text-gray-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </button>
                <button className="rounded bg-white p-1 shadow-sm dark:bg-gray-800">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-600 dark:text-gray-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                </button>
                <button className="rounded bg-white p-1 shadow-sm dark:bg-gray-800">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-600 dark:text-gray-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20 12H4"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div className="mt-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Engagement Zones Analysis
                </h4>
                <div className="flex space-x-2">
                  <select className="rounded border-gray-300 bg-white text-xs text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300">
                    <option>Today</option>
                    <option>This Week</option>
                    <option>This Month</option>
                    <option>Custom Range</option>
                  </select>
                  <button className="rounded bg-emerald-50 px-2 py-1 text-xs text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="mr-1 inline h-3 w-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    Export
                  </button>
                </div>
              </div>
              <div className="mt-2 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-900">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                      >
                        Zone
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                      >
                        Traffic
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                      >
                        Dwell Time
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                      >
                        Conversion
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        Main Entrance
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-800 dark:text-red-100">
                          Very High
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        0:45
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        5.2%
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        Central Atrium
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                          High
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        3:12
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        7.8%
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        Electronics
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-800 dark:text-green-100">
                          Medium
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        5:45
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        4.5%
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        Fashion
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                          Low
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        8:20
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        9.1%
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        Food Court
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-800 dark:text-red-100">
                          Very High
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        12:35
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        3.2%
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Incident Management */}
        <div className="dashboard-card rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="border-b border-gray-200 px-4 py-5 sm:px-6 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                  Incident Management
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  AI-powered security and behavioral anomaly detection
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <select className="rounded border-gray-300 bg-white py-1 text-xs text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300">
                  <option>All Incidents</option>
                  <option>Security</option>
                  <option>Traffic</option>
                  <option>Behavior</option>
                </select>
                <button className="rounded-md bg-emerald-50 p-1 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div className="px-4 py-3 sm:p-0">
            <div className="flow-root">
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {/* Incident 1 */}
                <li className="dark:hover:bg-gray-750 px-4 py-3 transition duration-150 ease-in-out hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-shrink-0">
                      <div className="h-16 w-28 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
                        <Image
                          src="/images/office.jpg"
                          alt="Security footage"
                          className="h-full w-full object-cover"
                          width={112}
                          height={64}
                        />
                      </div>
                      <div className="absolute right-1 bottom-1 rounded bg-red-600 px-1 text-xs text-white">
                        LIVE
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-gray-900 dark:text-white">
                          <span className="mr-2 inline-flex items-center rounded bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
                            High Priority
                          </span>
                          Unauthorized Access Detected
                        </p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            2 mins ago
                          </p>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1.5 h-4 w-4 flex-shrink-0 text-gray-400 dark:text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Electronics Section - Camera #4
                        </div>
                      </div>
                      <div className="mt-1 flex space-x-2">
                        <button className="inline-flex items-center rounded border border-transparent bg-emerald-600 px-2 py-1 text-xs font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          View
                        </button>
                        <button className="inline-flex items-center rounded border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                            />
                          </svg>
                          Assign
                        </button>
                        <button className="inline-flex items-center rounded border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          Resolve
                        </button>
                      </div>
                    </div>
                  </div>
                </li>

                {/* Incident 2 */}
                <li className="dark:hover:bg-gray-750 px-4 py-3 transition duration-150 ease-in-out hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-shrink-0">
                      <div className="h-16 w-28 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
                        <Image
                          src="/images/office.jpg"
                          alt="Security footage"
                          className="h-full w-full object-cover"
                          width={112}
                          height={64}
                        />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-gray-900 dark:text-white">
                          <span className="mr-2 inline-flex items-center rounded bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            Medium Priority
                          </span>
                          Unusual Traffic Pattern
                        </p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            15 mins ago
                          </p>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1.5 h-4 w-4 flex-shrink-0 text-gray-400 dark:text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Main Entrance - Camera #1
                        </div>
                      </div>
                      <div className="mt-1 flex space-x-2">
                        <button className="inline-flex items-center rounded border border-transparent bg-emerald-600 px-2 py-1 text-xs font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          View
                        </button>
                        <button className="inline-flex items-center rounded border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                            />
                          </svg>
                          Assign
                        </button>
                        <button className="inline-flex items-center rounded border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          Resolve
                        </button>
                      </div>
                    </div>
                  </div>
                </li>

                {/* Incident 3 */}
                <li className="dark:hover:bg-gray-750 px-4 py-3 transition duration-150 ease-in-out hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-shrink-0">
                      <div className="h-16 w-28 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
                        <Image
                          src="/images/office.jpg"
                          alt="Security footage"
                          className="h-full w-full object-cover"
                          width={112}
                          height={64}
                        />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-gray-900 dark:text-white">
                          <span className="mr-2 inline-flex items-center rounded bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Low Priority
                          </span>
                          Camera Connection Restored
                        </p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            1 hour ago
                          </p>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1.5 h-4 w-4 flex-shrink-0 text-gray-400 dark:text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Food Court - Camera #7
                        </div>
                      </div>
                      <div className="mt-1 flex space-x-2">
                        <button className="inline-flex items-center rounded border border-transparent bg-emerald-600 px-2 py-1 text-xs font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          View
                        </button>
                        <button className="inline-flex items-center rounded border border-gray-300 bg-white px-2 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="mr-1 h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div className="border-t border-gray-200 px-4 py-4 sm:px-6 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex flex-1 justify-between">
                  <a
                    href="#"
                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    Previous
                  </a>
                  <a
                    href="#"
                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                  >
                    Next
                  </a>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-end">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Showing <span className="font-medium">1</span> to{" "}
                      <span className="font-medium">3</span> of{" "}
                      <span className="font-medium">12</span> incidents
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="dashboard-card mt-8 rounded-lg bg-white shadow dark:bg-gray-800">
        <div className="border-b border-gray-200 px-4 py-5 sm:px-6 dark:border-gray-700">
          <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
            Retail Performance Analytics
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Key performance indicators with predictive trend analysis
          </p>
        </div>
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {analyticsData.performanceMetrics.map((metric, index) => (
              <div
                key={index}
                className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700"
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {metric.metric}
                  </h4>
                  <div
                    className={`flex items-center text-xs font-medium ${
                      metric.trend === "up"
                        ? "text-emerald-600 dark:text-emerald-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {metric.change}
                    {metric.trend === "up" ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ml-1 h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 15l7-7 7 7"
                        />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ml-1 h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    )}
                  </div>
                </div>
                <p className="mt-1 text-2xl font-semibold text-gray-900 dark:text-white">
                  {metric.value}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
