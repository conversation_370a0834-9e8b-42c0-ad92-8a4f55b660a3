"use client";

import { useMemo, useState } from "react";

// import Link from "next/link";

import AddCamera from "@/components/cameras/AddCamera";
import OverlayStreamViewer from "@/components/cameras/overlays/OverlayStreamViewer";
import { useCamerasWithFilter } from "@/services/cameras";

export default function NonVACamerasPage() {
  const { data: rawData, isLoading, error } = useCamerasWithFilter("non-va");
  const [thumbnailSize, setThumbnailSize] = useState("large"); // small, medium, large
  const [displayCount, setDisplayCount] = useState(12);

  const data = useMemo(() => {
    if (!rawData) return [];

    return [...rawData]
      .map((camera) => {
        // Determine if camera is for visual analytics based on description
        // Alternatively use camera.custom_properties?.required_analytics if available
        const isVA = camera.description?.includes("VA Camera") || false;

        // Use the actual camera name from the backend fixtures
        return {
          ...camera,
          isVA,
          displayName: camera.name || `Camera ${camera.id}`,
        };
      });
  }, [rawData]);

  // Function to get grid column class based on thumbnail size
  const getGridClass = () => {
    switch (thumbnailSize) {
      case "small":
        return "grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6";
      case "large":
        return "grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
      case "medium":
      default:
        return "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4";
    }
  };

  // Handle display count change
  const handleDisplayCountChange = (count: number) => {
    setDisplayCount(count);
  };

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      {/* Page Header */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Non-VA Camera Management
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Monitor all non-visual analytics security cameras and manage their
            settings
          </p>
        </div>
        <AddCamera />
      </div>

      {/* Controls */}
      <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search */}
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg
                className="h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full rounded-md border border-gray-300 bg-white py-2 pr-3 pl-10 leading-5 text-gray-900 placeholder-gray-500 focus:border-emerald-500 focus:ring-emerald-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400"
              placeholder="Search cameras..."
            />
          </div>

          {/* Filter */}
          <div className="relative">
            <select className="block w-full rounded-md border border-gray-300 bg-white py-2 pr-10 pl-3 text-base text-gray-900 focus:border-emerald-500 focus:ring-emerald-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100">
              <option>All Locations</option>
              <option>North Wing</option>
              <option>South Wing</option>
              <option>East Wing</option>
              <option>West Wing</option>
              <option>Central</option>
              <option>Exterior</option>
              <option>Basement</option>
            </select>
          </div>

          {/* Status Filter */}
          <div className="relative">
            <select className="block w-full rounded-md border border-gray-300 bg-white py-2 pr-10 pl-3 text-base text-gray-900 focus:border-emerald-500 focus:ring-emerald-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100">
              <option>All Status</option>
              <option>Online</option>
              <option>Offline</option>
            </select>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Display Count */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Show:
            </span>
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => handleDisplayCountChange(12)}
                className={`rounded-l-md px-3 py-1 text-sm font-medium ${
                  displayCount === 12
                    ? "bg-emerald-600 text-white"
                    : "border border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                12
              </button>
              <button
                onClick={() => handleDisplayCountChange(16)}
                className={`px-3 py-1 text-sm font-medium ${
                  displayCount === 16
                    ? "bg-emerald-600 text-white"
                    : "border-t border-b border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                16
              </button>
              <button
                onClick={() => handleDisplayCountChange(20)}
                className={`rounded-r-md px-3 py-1 text-sm font-medium ${
                  displayCount === 20
                    ? "bg-emerald-600 text-white"
                    : "border border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                All
              </button>
            </div>
          </div>

          {/* Thumbnail Size */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Size:
            </span>
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => setThumbnailSize("small")}
                className={`rounded-l-md px-3 py-1 text-sm font-medium ${
                  thumbnailSize === "small"
                    ? "bg-emerald-600 text-white"
                    : "border border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                S
              </button>
              <button
                onClick={() => setThumbnailSize("medium")}
                className={`px-3 py-1 text-sm font-medium ${
                  thumbnailSize === "medium"
                    ? "bg-emerald-600 text-white"
                    : "border-t border-b border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                M
              </button>
              <button
                onClick={() => setThumbnailSize("large")}
                className={`rounded-r-md px-3 py-1 text-sm font-medium ${
                  thumbnailSize === "large"
                    ? "bg-emerald-600 text-white"
                    : "border border-gray-300 bg-white text-gray-700 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                }`}
              >
                L
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Camera Grid */}
      {/* Loading and Error States */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="h-12 w-12 animate-spin rounded-full border-t-2 border-b-2 border-emerald-500"></div>
        </div>
      )}

      {error && (
        <div
          className="mb-6 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
          role="alert"
        >
          <p className="font-bold">Error loading cameras</p>
          <p>{error}</p>
        </div>
      )}

      {/* Camera Grid */}
      {!isLoading && (
        <div className={`grid ${getGridClass()} gap-6`}>
          {data.slice(0, displayCount).map((camera) => (
            <a
              key={camera.id}
              href={`/dashboard/cameras/${camera.id}`}
              className="block w-full"
            >
              <div className="overflow-hidden rounded-lg shadow">
                {/* Single card with just the stream and overlay name */}
                <div className="relative cursor-pointer">
                  <div
                    className={`relative ${camera.isVA ? "bg-blue-50 dark:bg-blue-900/30" : "bg-gray-100 dark:bg-gray-700"} ${
                      thumbnailSize === "small"
                        ? "h-40"
                        : thumbnailSize === "medium"
                          ? "h-56"
                          : "h-72"
                    } flex w-full items-center justify-center overflow-hidden`}
                  >
                    {/* Stream wrapper */}
                    <div className="absolute inset-0 flex h-full w-full items-center justify-center bg-black">
                      <OverlayStreamViewer
                        streamUrl={camera.stream_url || ""}
                        cameraId={camera.id}
                        className="h-full w-full object-contain"
                        debugMode={false}
                      />

                      {/* Loading UI */}
                      {isLoading && (
                        <div className="bg-opacity-50 absolute inset-0 flex items-center justify-center bg-black">
                          <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-emerald-500"></div>
                        </div>
                      )}

                      {/* Error state */}
                      {error && (
                        <div className="bg-opacity-50 absolute inset-0 flex flex-col items-center justify-center bg-black p-4 text-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-8 w-8 text-red-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <p className="mt-2 text-sm text-white">
                            Failed to load stream
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Minimalist camera name overlay */}
                    <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 to-transparent px-2 py-1.5">
                      <div className="flex items-center justify-between">
                        <h3
                          className="truncate text-sm font-medium text-white"
                          title={camera.description || camera.name}
                        >
                          {camera.displayName}
                          {camera.location && (
                            <span className="text-gray-300 ml-1">
                              • {camera.location}
                            </span>
                          )}
                          {camera.isVA && (
                            <span className="ml-1 rounded bg-blue-500 px-1 py-0.5 text-xs text-white">
                              VA
                            </span>
                          )}
                        </h3>
                        {/* {camera.status === "offline" && (
                          <span className="text-xs font-medium text-red-400">Offline</span>
                        )} */}
                      </div>
                    </div>

                    {/* Play button */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity hover:opacity-100">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        className="rounded-full bg-black/50 p-3 text-white transition-opacity hover:bg-black/75"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          ))}
        </div>
      )}

      {/* Pagination */}
      {displayCount < data.length && (
        <div className="mt-8 flex justify-center">
          <button
            onClick={() => setDisplayCount(data.length)}
            className="inline-flex items-center rounded-md border border-transparent bg-emerald-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none"
          >
            Load All Cameras
          </button>
        </div>
      )}
    </main>
  );
}
