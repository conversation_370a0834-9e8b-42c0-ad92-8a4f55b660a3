"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { Calendar, ChevronDown, ChevronUp, Search } from "lucide-react";

// Import the API types
import {
  CameraEvent,
  CameraEventListResponse,
  EventFilterParams,
  EventType,
} from "@/types/event";
import { useEvents } from "@/services/events";

export default function EventsPage() {
  const router = useRouter();

  // Define UI event type that extends CameraEventListResponse with UI-specific properties
  type UIEvent = CameraEventListResponse & {
    thumbnail?: string;
    details?: string;
    processTimestamps?: {
      frameReceived: string;
      framePreprocessed: string;
      detection: string;
      alertSent: string;
      soccConfirmation: string | null;
      tsoAcknowledge: string | null;
      tsoResponse: string | null;
    };
  };

  // State for events and filters
  const [events, setEvents] = useState<UIEvent[]>([]);
  // Scores for True/False Positive display
  const scores = {
    truePositive: {
      count: 2772,
      total: 3000,
      percentage: 92.4,
    },
    falsePositive: {
      count: 228,
      total: 3000,
      percentage: 7.6,
    },
  };
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearchDebouncing, setIsSearchDebouncing] = useState(false);
  const [sortBy, setSortBy] =
    useState<keyof CameraEventListResponse>("timestamp");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [typeFilter, setTypeFilter] = useState<EventType | "all">("all");
  const [dateRange, setDateRange] = useState<{
    start: string | null;
    end: string | null;
  }>({
    start: null,
    end: null,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Debounce search term with 2-second delay to avoid excessive API calls
  // The search will only be sent to the backend after the user stops typing for 2 seconds
  useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearchDebouncing(true);
    }

    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setIsSearchDebouncing(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedSearchTerm]);

  // Build filter parameters for the API call - use useMemo to prevent recreation on every render
  const filterParams = useMemo(() => {
    const params: EventFilterParams = {};
    if (typeFilter !== "all") {
      params.event_type = typeFilter as EventType;
    }
    if (dateRange.start) {
      params.start_date = dateRange.start;
    }
    if (dateRange.end) {
      params.end_date = dateRange.end;
    }
    if (debouncedSearchTerm.trim()) {
      params.search = debouncedSearchTerm.trim();
    }
    params.page = currentPage;
    params.page_size = pageSize;
    return params;
  }, [
    typeFilter,
    dateRange.start,
    dateRange.end,
    debouncedSearchTerm,
    currentPage,
    pageSize,
  ]);

  // Use our API service to fetch events
  const {
    events: apiEvents,
    isLoading,
    totalEvents: apiTotalEvents,
  } = useEvents(filterParams);

  // Helper function to get a human-readable description for event types
  const getEventTypeDescription = (eventType: EventType): string => {
    switch (eventType) {
      case "CR1 - Suspicious Person":
        return "Suspicious Person";
      case "CR2 - Weapon":
        return "Offensive Weapon";
      case "CR3 - Oversized object detected":
        return "Person Carrying Oversize Object";
      case "CR4 - Unattended object detected":
        return "Unattended Object Detection";
      case "CR6 - The person is the scaling gantry":
        return "Scaling Gantry";
      case "custom":
        return "Custom Detection Rule";
      default:
        return eventType;
    }
  };

  // Convert API events to UI events - use useMemo to prevent recreation on every render
  const eventUIData = useMemo(() => {
    if (!apiEvents || apiEvents.length === 0) {
      return [];
    }

    return apiEvents.map((event) => {
      // Get thumbnail from API or use fallback
      let thumbnailSrc = event.thumbnail
        ? `data:image/jpeg;base64,${event.thumbnail}`
        : `/thumbnails/${Math.floor(Math.random() * 8) + 1}.jpg`;

      // Ensure we have all required properties for CameraEvent type
      const uiEvent: UIEvent = {
        id: event.id,
        camera_layer_config: event.camera_layer_config,
        event_type: event.event_type,
        timestamp: event.timestamp,
        confidence: event.confidence,
        is_reviewed: event.is_reviewed,
        is_suspicious: event.is_suspicious,
        camera_name: event.camera_name || "Unknown Camera",
        camera_location: event.camera_location || "Unknown Location",
        event_severity: event.event_severity,
        bounding_boxes: event.bounding_boxes,
        thumbnail: thumbnailSrc,
        details: getEventTypeDescription(event.event_type),
        camera_id: event.camera_id,
        frame_timestamp: event.frame_timestamp,
        preprocessing_timestamp: event.preprocessing_timestamp,
        detection_timestamp: event.detection_timestamp,
        alert_received_timestamp: event.alert_received_timestamp,
        socc_acknowledged_timestamp: event.socc_acknowledged_timestamp,
        socc_notification_to_tso_timestamp:
          event.socc_notification_to_tso_timestamp,
        tso_acknowledged_timestamp: event.tso_acknowledged_timestamp,
      };

      return uiEvent;
    });
  }, [apiEvents]);

  // Update the events state when API data changes
  useEffect(() => {
    setEvents(eventUIData);
    setLoading(isLoading);
  }, [eventUIData, isLoading]);

  // Handle sort change
  const handleSort = (column: keyof CameraEventListResponse) => {
    if (column === sortBy) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("desc");
    }
  };

  // Handle type filter change
  const handleTypeFilterChange = (type: EventType | "all") => {
    setTypeFilter(type);

    // Update API filter params
    if (type !== "all") {
      filterParams.event_type = type;
    } else {
      delete filterParams.event_type;
    }
  };

  // Handle date range filter change
  const handleDateRangeChange = (start: string | null, end: string | null) => {
    // Update the date range state which will trigger a refresh of filter params
    setDateRange({ start, end });

    // Update API filter params
    if (start) {
      filterParams.start_date = start;
    } else {
      delete filterParams.start_date;
    }

    if (end) {
      filterParams.end_date = end;
    } else {
      delete filterParams.end_date;
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [typeFilter, dateRange.start, dateRange.end, debouncedSearchTerm]);

  // Helper function to calculate pagination info
  const getPaginationInfo = () => {
    const total = apiTotalEvents || events.length;
    const totalPages = Math.ceil(total / pageSize);
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, total);

    return {
      total,
      totalPages,
      startItem,
      endItem,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    };
  };

  // Format date for display
  const formatDate = (dateInput: string | Date) => {
    const date = new Date(dateInput);
    try {
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "short",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      }).format(date);
    } catch (e) {
      console.error("Error formatting date:", e);
      return "Invalid Date";
    }
  };

  // Format timestamp as HH:MM:SS.mmm
  const formatTimestamp = (timestamp: string | null): string => {
    if (!timestamp) return "-";
    try {
      const date = new Date(timestamp);
      // Format as HH:MM:SS.mmm
      return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date.getSeconds().toString().padStart(2, "0")}.${date.getMilliseconds().toString().padStart(3, "0")}`;
    } catch (e) {
      console.error("Error formatting timestamp:", e);
      return "-";
    }
  };

  // Calculate time difference between timestamps
  const calculateTimeDiff = (
    current: string | null,
    previous: string | null
  ): string => {
    if (!current || !previous) return "";

    try {
      const currentTime = new Date(current).getTime();
      const previousTime = new Date(previous).getTime();
      const diffMs = currentTime - previousTime;
      const isNegative = diffMs < 0;
      const absDiff = Math.abs(diffMs);

      // Format the difference as HH:MM:SS.mmm
      const hours = Math.floor(absDiff / 3600000);
      const minutes = Math.floor((absDiff % 3600000) / 60000);
      const seconds = Math.floor((absDiff % 60000) / 1000);
      const milliseconds = absDiff % 1000;

      const sign = isNegative ? "-" : "+";
      return `${sign}${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(3, "0")}`;
    } catch (e) {
      console.error("Error calculating time difference:", e);
      return "";
    }
  };

  // Get color class for delay based on time difference
  const getDelayColorClass = (
    current: string | null,
    previous: string | null
  ): string => {
    if (!current || !previous) return "text-gray-500";

    try {
      const currentTime = new Date(current).getTime();
      const previousTime = new Date(previous).getTime();
      const diffMs = currentTime - previousTime;

      if (diffMs < 500) return "text-green-600 dark:text-green-400"; // Fast: under 500ms
      if (diffMs < 2000) return "text-yellow-600 dark:text-yellow-400"; // Moderate: under 2s
      return "text-red-600 dark:text-red-400"; // Slow: over 2s
    } catch (e) {
      console.error("Error getting delay color class:", e);
      return "text-gray-500";
    }
  };

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      {/* Page Header */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Event History
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Monitor and review all security events across the system
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search */}
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Search className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search events..."
              className={`block w-full rounded-md border py-2 pr-3 pl-10 leading-5 text-gray-900 placeholder-gray-500 focus:outline-none sm:text-sm dark:text-gray-100 dark:placeholder-gray-400 ${
                isSearchDebouncing
                  ? "border-yellow-500 bg-white focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-gray-800"
                  : "border-gray-300 bg-white focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-800"
              }`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {isSearchDebouncing && (
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-yellow-500 border-t-transparent"></div>
              </div>
            )}
            {isSearchDebouncing && (
              <div className="absolute -bottom-5 left-0 text-xs text-yellow-600 dark:text-yellow-400">
                Searching in 1s...
              </div>
            )}
          </div>

          {/* Type Filter Dropdown */}
          <div className="relative">
            <select
              className="block w-full appearance-none rounded-md border border-gray-300 bg-white py-2 pr-8 pl-3 leading-5 text-gray-900 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
              value={typeFilter}
              onChange={(e) =>
                handleTypeFilterChange(e.target.value as EventType | "all")
              }
            >
              <option value="all">All Event Types</option>
              <option value="suspicious_person">Suspicious Person</option>
              <option value="offensive_weapon">Offensive Weapon</option>
              <option value="oversized_object">Oversize Object</option>
              <option value="unattended_object">Unattended Object</option>
              <option value="scaling_gantry">Scaling Gantry</option>
              {/* <option value="custom">Custom Detection Rule</option> */}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-400">
              <ChevronDown className="h-4 w-4" />
            </div>
          </div>

          {/* Date Range */}
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="date"
              placeholder="Start Date"
              className="block w-full rounded-md border border-gray-300 bg-white py-2 pr-3 pl-10 leading-5 text-gray-900 placeholder-gray-500 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400"
              value={dateRange.start || ""}
              onChange={(e) =>
                handleDateRangeChange(e.target.value, dateRange.end)
              }
            />
          </div>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="date"
              placeholder="End Date"
              className="block w-full rounded-md border border-gray-300 bg-white py-2 pr-3 pl-10 leading-5 text-gray-900 placeholder-gray-500 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400"
              value={dateRange.end || ""}
              onChange={(e) =>
                handleDateRangeChange(dateRange.start, e.target.value)
              }
            />
          </div>

          {/* Page Size Selector */}
          <div className="relative">
            <select
              className="block w-full appearance-none rounded-md border border-gray-300 bg-white py-2 pr-8 pl-3 leading-5 text-gray-900 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-400">
              <ChevronDown className="h-4 w-4" />
            </div>
          </div>

          {/* Score Statistics Box */}
          {/* <div className="flex items-center h-10 rounded-md border border-gray-300 overflow-hidden dark:border-gray-600">
            <div className="h-full bg-gray-100 px-3 py-1.5 flex items-center border-r border-gray-300 dark:bg-gray-700 dark:border-gray-600">
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Accuracy</span>
            </div>
            <div className="flex h-full">
              <div className="flex items-center px-3 gap-1.5 border-r border-gray-300 dark:border-gray-600">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">TP:</span>
                <span className="text-xs font-medium text-green-600 dark:text-green-500">{scores.truePositive.count}</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">/ {scores.truePositive.total}</span>
                <span className="text-xs font-medium text-green-600 dark:text-green-500">({scores.truePositive.percentage}%)</span>
              </div>
              <div className="flex items-center px-3 gap-1.5">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">FP:</span>
                <span className="text-xs font-medium text-red-600 dark:text-red-500">{scores.falsePositive.count}</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">/ {scores.falsePositive.total}</span>
                <span className="text-xs font-medium text-red-600 dark:text-red-500">({scores.falsePositive.percentage}%)</span>
              </div>
            </div>
          </div> */}
        </div>
      </div>

      {/* Events Table */}
      <div className="mt-4 overflow-hidden rounded-lg border border-gray-200 bg-white shadow dark:border-gray-700 dark:bg-gray-800">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900 dark:border-white"></div>
            <span className="ml-2">Loading events...</span>
          </div>
        ) : events.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="mb-2 text-6xl">🔍</div>
            <h3 className="mb-1 text-lg font-semibold">No events found</h3>
            <p className="text-sm text-gray-500">
              Try adjusting your search or filter criteria
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full whitespace-nowrap">
              <thead>
                <tr className="border-b bg-gray-50 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:border-gray-700 dark:bg-gray-800/50 dark:text-gray-400">
                  <th className="sticky left-0 w-28 bg-gray-50 px-6 py-3 dark:bg-gray-800">
                    <div className="flex items-center">Details</div>
                  </th>
                  <th
                    className="cursor-pointer px-6 py-3"
                    onClick={() => handleSort("camera_name")}
                  >
                    <div className="flex items-center">
                      Camera
                      {sortBy === "camera_name" && (
                        <span className="ml-1">
                          {sortDirection === "asc" ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </div>
                  </th>
                  {/* <th
                    className="cursor-pointer px-6 py-3"
                    onClick={() => handleSort("confidence")}
                  >
                    <div className="flex items-center">
                      Confidence
                      {sortBy === "confidence" && (
                        <span className="ml-1">
                          {sortDirection === "asc" ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </div>
                  </th> */}
                  <th
                    className="cursor-pointer px-6 py-3"
                    onClick={() => handleSort("event_severity")}
                  >
                    <div className="flex items-center">
                      Severity
                      {sortBy === "event_severity" && (
                        <span className="ml-1">
                          {sortDirection === "asc" ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </span>
                      )}
                    </div>
                  </th>
                  {/* Process Timestamp Headers */}
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">
                        Frame Received
                      </span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">
                        Frame Preprocessed
                      </span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">Detection</span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">Alert Sent</span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">
                        SOCC Confirmation
                      </span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">
                        TSO Acknowledge
                      </span>
                    </div>
                  </th>
                  <th className="min-w-[180px] bg-gray-100 px-6 py-3 dark:bg-gray-800">
                    <div className="flex flex-col">
                      <span className="text-xs font-medium">TSO Response</span>
                    </div>
                  </th>
                  <th className="sticky right-0 bg-gray-50 px-6 py-3 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {events.map((event, index) => (
                  <tr
                    key={index}
                    className="group cursor-pointer bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                    onClick={() => router.push(`/dashboard/events/${event.id}`)}
                  >
                    <td className="sticky left-0 bg-white px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex items-start space-x-3">
                        <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded-md bg-gray-100">
                          <img
                            src={event.thumbnail}
                            alt="Event thumbnail"
                            onError={(e) => {
                              e.currentTarget.src = `/thumbnail-placeholder.jpg`;
                            }}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {event.details}
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatDate(event.timestamp)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-xs font-medium break-words text-gray-900 dark:text-white">
                          {event.camera_name || "Unknown Camera"}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {event.camera_location || "Unknown Location"}
                        </div>
                      </div>
                    </td>
                    {/* <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-medium ${
                          event.confidence >= 0.85
                            ? "text-green-600 dark:text-green-500"
                            : event.confidence >= 0.6
                              ? "text-yellow-600 dark:text-yellow-500"
                              : "text-red-600 dark:text-red-500"
                        }`}
                      >
                        {Math.round(event.confidence * 100)}%
                      </span>
                    </td> */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-medium ${
                          event.event_severity === "Critical"
                            ? "text-red-600 dark:text-red-500"
                            : "text-yellow-600 dark:text-yellow-500"
                        }`}
                      >
                        {event.event_severity}
                      </span>
                    </td>

                    {/* Process Timestamps Cells */}
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(event.frame_timestamp || null)}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(
                            event.preprocessing_timestamp || null
                          )}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.preprocessing_timestamp || null, event.frame_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.preprocessing_timestamp || null,
                            event.frame_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(event.detection_timestamp || null)}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.detection_timestamp || null, event.preprocessing_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.detection_timestamp || null,
                            event.preprocessing_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(
                            event.alert_received_timestamp || null
                          )}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.alert_received_timestamp || null, event.detection_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.alert_received_timestamp || null,
                            event.detection_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(
                            event.socc_acknowledged_timestamp || null
                          )}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.socc_acknowledged_timestamp || null, event.alert_received_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.socc_acknowledged_timestamp || null,
                            event.alert_received_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(
                            event.socc_notification_to_tso_timestamp || null
                          )}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.socc_notification_to_tso_timestamp || null, event.socc_acknowledged_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.socc_notification_to_tso_timestamp || null,
                            event.socc_acknowledged_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="bg-gray-50 px-6 py-4 group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatTimestamp(
                            event.tso_acknowledged_timestamp || null
                          )}
                        </span>
                        <span
                          className={`text-xs ${getDelayColorClass(event.tso_acknowledged_timestamp || null, event.socc_notification_to_tso_timestamp || null)}`}
                        >
                          {calculateTimeDiff(
                            event.tso_acknowledged_timestamp || null,
                            event.socc_notification_to_tso_timestamp || null
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="sticky right-0 bg-gray-50 px-6 py-4 text-right text-sm font-medium whitespace-nowrap group-hover:bg-gray-50 dark:bg-gray-800 dark:group-hover:bg-gray-700">
                      <button
                        onClick={() =>
                          (window.location.href = `/dashboard/events/${event.id}`)
                        }
                        className="mr-3 text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        <div className="flex items-center justify-between border-t border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-800/50">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {(() => {
              const paginationInfo = getPaginationInfo();
              return (
                <>
                  Showing{" "}
                  <span className="font-medium">
                    {paginationInfo.startItem}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium">{paginationInfo.endItem}</span>{" "}
                  of <span className="font-medium">{paginationInfo.total}</span>{" "}
                  events
                </>
              );
            })()}
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              disabled={!getPaginationInfo().hasPrevPage}
              onClick={() => handlePageChange(currentPage - 1)}
            >
              Previous
            </button>

            {/* Page Numbers */}
            {(() => {
              const paginationInfo = getPaginationInfo();
              const maxVisiblePages = 5;
              const pages = [];

              if (paginationInfo.totalPages <= maxVisiblePages) {
                // Show all pages if total is small
                for (let i = 1; i <= paginationInfo.totalPages; i++) {
                  pages.push(i);
                }
              } else {
                // Show pages around current page
                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  paginationInfo.totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                for (let i = startPage; i <= endPage; i++) {
                  pages.push(i);
                }
              }

              return pages.map((page) => (
                <button
                  key={page}
                  className={`inline-flex items-center justify-center rounded-md border px-3 py-1.5 text-sm font-medium shadow-sm disabled:opacity-50 ${
                    page === currentPage
                      ? "border-transparent bg-indigo-600 text-white"
                      : "border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              ));
            })()}

            <button
              className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              disabled={!getPaginationInfo().hasNextPage}
              onClick={() => handlePageChange(currentPage + 1)}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
