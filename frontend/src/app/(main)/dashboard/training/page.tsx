"use client";

import React, { useState } from "react";
import Image from "next/image";

export default function TrainingPage() {
  // Mock training data
  const [trainingData] = useState([
    {
      id: 1,
      timestamp: "2025-04-25T08:30:45",
      confidence: 87,
      camera: "Front Yard",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 2,
      timestamp: "2025-04-25T09:15:22",
      confidence: 92,
      camera: "Backyard",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 3,
      timestamp: "2025-04-25T10:42:18",
      confidence: 79,
      camera: "Side Gate",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 4,
      timestamp: "2025-04-25T11:30:04",
      confidence: 88,
      camera: "Front Yard",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 5,
      timestamp: "2025-04-25T12:21:33",
      confidence: 95,
      camera: "Driveway",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 6,
      timestamp: "2025-04-25T13:05:27",
      confidence: 84,
      camera: "Backyard",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 7,
      timestamp: "2025-04-25T14:18:41",
      confidence: 91,
      camera: "Front Yard",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 8,
      timestamp: "2025-04-24T15:52:09",
      confidence: 86,
      camera: "Side Gate",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
    {
      id: 9,
      timestamp: "2025-04-24T16:40:57",
      confidence: 93,
      camera: "Driveway",
      thumbnail: "/images/office.jpg",
      reviewed: false,
    },
  ]);

  // Training state
  const [selectedTrainingItem, setSelectedTrainingItem] = useState<
    number | null
  >(1);
  const [trainingStats] = useState({
    totalReviewed: 147,
    queueRemaining: 23,
    markedSuspicious: 12, // percentage
    modelImprovement: 8.3, // percentage
  });

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          Model Training
        </h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Help improve detection accuracy by reviewing and classifying person
          detections
        </p>
      </div>

      {/* Training Content */}
      <div className="space-y-4">
        {/* Training Header with Statistics */}
        <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
          <h3 className="mb-2 text-lg font-medium text-gray-700 dark:text-gray-300">
            Training Dashboard
          </h3>
          <p className="mb-4 text-sm text-gray-500 dark:text-gray-400">
            Review detected people and classify them to improve detection
            accuracy. Your input helps our system learn!
          </p>

          {/* Training Statistics */}
          <div className="mb-4 grid grid-cols-4 gap-4">
            <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Queue Remaining
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {trainingStats.queueRemaining}
              </div>
            </div>

            <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                You&apos;ve Reviewed
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {trainingStats.totalReviewed}
              </div>
            </div>

            <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Marked Suspicious
              </div>
              <div className="mt-1 text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                {trainingStats.markedSuspicious}%
              </div>
            </div>

            <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Model Improvement
              </div>
              <div className="mt-1 text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                +{trainingStats.modelImprovement}%
              </div>
            </div>
          </div>
        </div>

        {/* Main Training Interface */}
        <div className="grid grid-cols-3 gap-4">
          {/* Queue Section - Left Column */}
          <div className="col-span-1 max-h-[600px] overflow-y-auto rounded-lg bg-white p-4 shadow dark:bg-gray-800">
            <h4 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
              Pending Review
            </h4>

            <div className="space-y-3">
              {/* Queue Items */}
              {trainingData.map((item) => (
                <div
                  key={item.id}
                  className={`flex cursor-pointer items-center rounded-lg p-2 ${item.id === selectedTrainingItem ? "border border-emerald-200 bg-emerald-50 dark:border-emerald-800 dark:bg-emerald-900" : "hover:bg-gray-50 dark:hover:bg-gray-700"}`}
                  onClick={() => setSelectedTrainingItem(item.id)}
                >
                  <div className="mr-3 h-16 w-16 flex-shrink-0 overflow-hidden rounded-md">
                    <Image
                      src={item.thumbnail}
                      alt="Person detection"
                      className="h-full w-full object-cover"
                      width={64}
                      height={64}
                    />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      Person #{item.id}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(item.timestamp).toLocaleString("en-US", {
                        month: "short",
                        day: "numeric",
                        hour: "numeric",
                        minute: "numeric",
                        hour12: true,
                      })}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {item.camera}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Review Area - Right Columns */}
          <div className="col-span-2 space-y-4">
            {/* Video Preview */}
            <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
              <h4 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                Review Detection
              </h4>

              {selectedTrainingItem !== null && (
                <>
                  <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-900">
                    <Image
                      src={
                        trainingData.find(
                          (item) => item.id === selectedTrainingItem
                        )?.thumbnail || "/images/placeholder.jpg"
                      }
                      className="h-full w-full object-contain"
                      alt="Person detection"
                      width={640}
                      height={360}
                    />

                    {/* Person highlight overlay */}
                    <div className="pointer-events-none absolute top-[30%] left-[40%] h-[40%] w-[20%] rounded-md border-2 border-red-500"></div>
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        Person detected at{" "}
                        {new Date(
                          trainingData.find(
                            (item) => item.id === selectedTrainingItem
                          )?.timestamp || ""
                        ).toLocaleTimeString("en-US", {
                          hour: "numeric",
                          minute: "numeric",
                          hour12: true,
                        })}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Initial confidence:{" "}
                        {
                          trainingData.find(
                            (item) => item.id === selectedTrainingItem
                          )?.confidence
                        }
                        %
                      </p>
                    </div>

                    <div className="flex space-x-2">
                      <button className="rounded-md bg-gray-100 px-3 py-2 text-sm text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mr-1 inline h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 13l-3 3m0 0l-3-3m3 3V8m0 13a9 9 0 110-18 9 9 0 010 18z"
                          />
                        </svg>
                        Rewind
                      </button>

                      <button className="rounded-md bg-gray-100 px-3 py-2 text-sm text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mr-1 inline h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>
                        Zoom
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Classification Area */}
            <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
              <h4 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                Classify Person
              </h4>

              <div className="mb-4">
                <label className="mb-1 block text-sm text-gray-700 dark:text-gray-300">
                  Add Tags (Optional)
                </label>
                <div className="flex flex-wrap gap-2">
                  <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    Delivery Person
                    <button className="ml-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-200">
                      ×
                    </button>
                  </span>

                  <input
                    type="text"
                    placeholder="Add tag..."
                    className="rounded-full border border-gray-300 bg-white px-2 py-1 text-xs focus:ring-1 focus:ring-emerald-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="mb-1 block text-sm text-gray-700 dark:text-gray-300">
                  Notes (Optional)
                </label>
                <textarea
                  className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="Add any additional context about this person..."
                  rows={2}
                ></textarea>
              </div>

              <div className="flex items-center justify-between">
                <button className="rounded-md bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                  Skip for Now
                </button>

                <div className="flex space-x-3">
                  <button className="rounded-md bg-green-100 px-4 py-2 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="mr-1 inline h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Not Suspicious
                  </button>

                  <button className="rounded-md bg-red-100 px-4 py-2 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-200 dark:hover:bg-red-800">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="mr-1 inline h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                    Mark Suspicious
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
