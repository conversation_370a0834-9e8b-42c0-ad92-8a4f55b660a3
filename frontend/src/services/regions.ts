import useS<PERSON> from "swr";
import api from "./api";
import { createFetcher } from "./fetcher";
import { RegionOfInterest, RegionRequest } from "@/types/region";

/**
 * Get all regions (global, not camera-specific)
 */
export const useAllRegions = () => {
  const url = `api/cameras/regions/`;
  const { data, error, mutate } = useSWR<RegionOfInterest[]>(
    url, 
    createFetcher,
    { revalidateOnFocus: false }
  );
  
  return {
    regions: data || [],
    isLoading: !error && !data,
    isError: error,
    mutate,
  };
};


/**
 * Create a new region
 */
export const addRegion = async (region: RegionRequest) => {
  try {
    // Use the new endpoint for creating regions
    const response = await api.post(
      `api/cameras/regions/`,
      region
    );
    return response.data as RegionOfInterest;
  } catch (error) {
    console.error('Error creating region:', error);
    throw error;
  }
};

/**
 * Update an existing region
 */
export const updateRegion = async (regionId: string, region: Partial<RegionRequest>) => {
  try {
    // Use the new endpoint for updating regions
    const response = await api.put(
      `api/cameras/regions/${regionId}/`,
      region
    );
    return response.data as RegionOfInterest;
  } catch (error) {
    console.error('Error updating region:', error);
    throw error;
  }
};

/**
 * Delete a region
 */
export const deleteRegion = async (regionId: string) => {
  try {
    // Use the new endpoint for deleting regions
    await api.delete(`api/cameras/regions/${regionId}/`);
  } catch (error) {
    console.error('Error deleting region:', error);
    throw error;
  }
};


/**
 * Get a single region by ID (global, not camera-specific)
 */
export const useRegion = (regionId: string) => {
  const url = regionId ? `api/cameras/regions/${regionId}/` : null;
  const { data, error, mutate } = useSWR<RegionOfInterest>(
    url, 
    createFetcher,
    { revalidateOnFocus: false }
  );
  
  return {
    region: data,
    isLoading: !error && !data && !!url,
    isError: error,
    mutate,
  };
};

/**
 * Get all regions for a specific camera
 */
export const useRegionsByCamera = (cameraId: string) => {
  const url = cameraId ? `api/cameras/regions/camera/${cameraId}/` : null;
  const { data, error, mutate } = useSWR<RegionOfInterest[]>(
    url, 
    createFetcher,
    { revalidateOnFocus: false }
  );
  
  return {
    regions: data || [],
    isLoading: !error && !data && !!url,
    isError: error,
    mutate,
  };
};

