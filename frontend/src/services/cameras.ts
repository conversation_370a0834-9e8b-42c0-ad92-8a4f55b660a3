import useS<PERSON> from 'swr';
import api from './api';
import { Camera } from '@/types/camera';
import { ApiResponse } from '@/types/response';
import { createFetcher } from './fetcher';

// Ensure URL has no leading slash to avoid double slashes
const BASE_URL = '/api/cameras/';

// Fetch single camera by ID
export function useCamera(cameraId: string) {
  const url = cameraId ? `${BASE_URL}${cameraId}/` : null;
  const { data, error, mutate } = useSWR<Camera>(url, fetcher);

  return {
    camera: data,
    isLoading: !error && !data && !!url,
    isError: error,
    mutate
  };
}

// Fetch all cameras (legacy version using SWR directly)
export const useAllCameras = () => {
  const url = BASE_URL;
  const snapshot = useSWR<Camera[]>(url, createFetcher, {});
  return { ...snapshot, data: snapshot.data ?? [] };
};

// Fetch cameras with view filter (VA or non-VA)
export const useCamerasWithFilter = (viewFilter?: 'va' | 'non-va') => {
  const url = viewFilter ? `${BASE_URL}?view_filter=${viewFilter}` : BASE_URL;
  const snapshot = useSWR<Camera[]>(url, createFetcher, {});
  return { ...snapshot, data: snapshot.data ?? [] };
};

// Fetch all cameras (paginated version)
export function useCameras() {
  const { data, error, mutate } = useSWR<ApiResponse<Camera[]>>(BASE_URL, fetcher);

  return {
    cameras: data?.results || [],
    isLoading: !error && !data,
    isError: error,
    total: data?.count || 0,
    mutate
  };
}

// Update camera
export async function updateCamera(cameraId: string, updateData: Partial<Camera>) {
  try {
    const response = await api.patch(`${BASE_URL}${cameraId}/`, updateData);
    return response.data;
  } catch (error: unknown) {
    console.error('Error updating camera:', error);
    throw error;
  }
}

// Create camera
export async function createCamera(cameraData: Partial<Camera>) {
  try {
    const response = await api.post(BASE_URL, cameraData);
    return response.data;
  } catch (error: unknown) {
    console.error('Error creating camera:', error);
    throw error;
  }
}

// Add camera (legacy alias for createCamera)
export const addCamera = createCamera;

// Delete camera
export async function deleteCamera(cameraId: string) {
  try {
    const response = await api.delete(`${BASE_URL}${cameraId}/`);
    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting camera:', error);
    throw error;
  }
}

// Helper function for SWR fetcher
async function fetcher(url: string) {
  try {
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${url}:`, error);
    throw error;
  }
}
