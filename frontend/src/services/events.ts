import useS<PERSON> from "swr";
import useSWRImmutable from "swr/immutable";

import {
  CameraEvent,
  CameraEventDetailResponse,
  CameraEventListResponse,
  EventFilterParams,
} from "@/types/event";

import api from "./api";

// Ensure URL has no leading slash to avoid double slashes
const BASE_URL = "/api/cameras/events/";

// Interface for paginated response
interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    count: number;
    total_pages: number;
    current_page: number;
    page_size: number;
    has_next: boolean;
    has_previous: boolean;
    next_page: number | null;
    previous_page: number | null;
  };
}

/**
 * Hook to fetch a single event by ID
 */
export function useEvent(eventId: string) {
  const url = eventId ? `${BASE_URL}${eventId}/` : null;
  const { data, error, mutate } = useSWRImmutable<CameraEventDetailResponse>(
    url,
    fetcher
  );

  return {
    event: data,
    isLoading: !error && !data && !!url,
    isError: error,
    mutate,
  };
}

/**
 * Hook to fetch all events with optional filtering and pagination
 */
export function useEvents(filters?: EventFilterParams) {
  // Build query string for filters
  let url = BASE_URL;
  if (filters) {
    const params = new URLSearchParams();
    if (filters.camera) params.append("camera", filters.camera);
    if (filters.event_type) params.append("event_type", filters.event_type);
    if (filters.is_reviewed !== undefined)
      params.append("is_reviewed", String(filters.is_reviewed));
    if (filters.is_suspicious !== undefined)
      params.append("is_suspicious", String(filters.is_suspicious));
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);
    if (filters.search) params.append("search", filters.search);
    if (filters.page) params.append("page", String(filters.page));
    if (filters.page_size)
      params.append("page_size", String(filters.page_size));

    const queryString = params.toString();
    if (queryString) url += `?${queryString}`;
  }

  const { data, error, mutate } = useSWR<
    PaginatedResponse<CameraEventListResponse>
  >(url, fetcher);

  return {
    events: data?.results || [],
    pagination: data?.pagination || null,
    totalEvents: data?.pagination?.count || 0,
    isLoading: !error && !data,
    isError: error,
    mutate,
  };
}

/**
 * Create a new event
 */
export async function createEvent(eventData: Partial<CameraEvent>) {
  try {
    const response = await api.post(BASE_URL, eventData);
    return response.data;
  } catch (error: unknown) {
    console.error("Error creating event:", error);
    throw error;
  }
}

/**
 * Update an existing event
 */
export async function updateEvent(
  eventId: string,
  updateData: Partial<CameraEvent>
) {
  try {
    const response = await api.patch(`${BASE_URL}${eventId}/`, updateData);
    return response.data;
  } catch (error: unknown) {
    console.error("Error updating event:", error);
    throw error;
  }
}

/**
 * Delete an event
 */
export async function deleteEvent(eventId: string) {
  try {
    const response = await api.delete(`${BASE_URL}${eventId}/`);
    return response.data;
  } catch (error: unknown) {
    console.error("Error deleting event:", error);
    throw error;
  }
}

/**
 * Mark an event as legitimate (not suspicious)
 */
export async function markEventLegitimate(eventId: string) {
  try {
    // Using the detail endpoint with action query parameter and empty body
    // The action parameter will handle setting the appropriate fields on the backend
    const response = await api.patch(
      `${BASE_URL}${eventId}/?action=mark_legitimate`,
      {}
    );
    return response.data;
  } catch (error: unknown) {
    console.error("Error marking event as legitimate:", error);
    throw error;
  }
}

/**
 * Mark an event as suspicious
 */
export async function markEventSuspicious(eventId: string) {
  try {
    // Using the detail endpoint with action query parameter and empty body
    // The action parameter will handle setting the appropriate fields on the backend
    const response = await api.patch(
      `${BASE_URL}${eventId}/?action=mark_suspicious`,
      {}
    );
    return response.data;
  } catch (error: unknown) {
    console.error("Error marking event as suspicious:", error);
    throw error;
  }
}

/**
 * Helper function for SWR fetcher
 */
async function fetcher(url: string) {
  try {
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${url}:`, error);
    throw error;
  }
}
