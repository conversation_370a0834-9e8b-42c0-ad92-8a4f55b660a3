import useS<PERSON> from "swr";
import api from "./api";
import { createFetcher } from "./fetcher";
import { 
  CameraLayerConfiguration,
  CameraLayerConfigurationRequest,
  LayersApiResponse
} from "@/types/layer";

/**
 * Get all camera layer configurations for a camera
 * Also returns all available base layer types
 */
export const useLayers = (cameraId: string) => {
  const url = cameraId ? `api/cameras/${cameraId}/layers/` : null;
  const { data, error, mutate } = useSWR<LayersApiResponse>(
    url, 
    createFetcher,
    { revalidateOnFocus: false }
  );

  return {
    layers: data?.configurations || [],
    baseLayers: data?.base_layers || [],
    isLoading: !error && !data && !!url,
    isError: error,
    mutate,
  };
};

/**
 * Get a single camera layer configuration by ID
 */
export const useLayer = (cameraId: string, layerId: string) => {
  const url = layerId ? `api/cameras/${cameraId}/layers/${layerId}/` : null;
  const { data, error, mutate } = useSWR<CameraLayerConfiguration>(
    url, 
    createFetcher,
    { revalidateOnFocus: false }
  );
  
  return {
    layer: data || null,
    isLoading: !error && !data && !!url,
    isError: error,
    mutate,
  };
};

/**
 * Add a new camera layer configuration
 */
export const addLayer = async (cameraId: string, layerConfig: CameraLayerConfigurationRequest): Promise<CameraLayerConfiguration> => {
  if (!cameraId) {
    throw new Error("Camera ID is required");
  }
  
  const url = `api/cameras/${cameraId}/layers/`;
  
  try {
    const response = await api.post<CameraLayerConfiguration>(url, layerConfig);
    return response.data;
  } catch (error: unknown) {
    console.error("Error adding layer configuration:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(typeof error === 'string' ? error : 'Unknown error adding layer configuration');
  }
};

/**
 * Update an existing camera layer configuration
 */
export const updateLayer = async (
  cameraId: string, 
  layerId: string, 
  layerConfig: Partial<CameraLayerConfigurationRequest>
): Promise<CameraLayerConfiguration> => {
  if (!cameraId || !layerId) {
    throw new Error("Camera ID and Layer ID are required");
  }
  
  const url = `api/cameras/${cameraId}/layers/${layerId}/`;
  
  try {
    const response = await api.put<CameraLayerConfiguration>(url, layerConfig);
    return response.data;
  } catch (error: unknown) {
    console.error("Error updating layer configuration:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(typeof error === 'string' ? error : 'Unknown error updating layer configuration');
  }
};

/**
 * Delete a camera layer configuration
 */
export const deleteLayer = async (cameraId: string, layerId: string): Promise<boolean> => {
  if (!cameraId || !layerId) {
    throw new Error("Camera ID and Layer ID are required");
  }
  
  const url = `api/cameras/${cameraId}/layers/${layerId}/`;
  
  try {
    await api.delete(url);
    return true;
  } catch (error: unknown) {
    console.error("Error deleting layer configuration:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(typeof error === 'string' ? error : 'Unknown error deleting layer configuration');
  }
};