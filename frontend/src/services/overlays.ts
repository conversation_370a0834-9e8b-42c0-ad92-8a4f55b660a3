import useSWR from 'swr';
import api from './api';
import { PermanentOverlay } from '@/types/overlay';
import { createFetcher } from './fetcher';

// Ensure URL has no leading slash to avoid double slashes
const BASE_URL = '/api/cameras/overlays/';

// Fetch single overlay by ID
export function useOverlay(overlayId: string) {
  const url = overlayId ? `${BASE_URL}${overlayId}/` : null;
  const { data, error, mutate } = useSWR<PermanentOverlay>(url, fetcher);

  return {
    overlay: data,
    isLoading: !error && !data && !!url,
    isError: error,
    mutate
  };
}

// Fetch all overlays
export const useAllOverlays = () => {
  const url = BASE_URL;
  const snapshot = useSWR<PermanentOverlay[]>(url, createFetcher);
  return { ...snapshot, data: snapshot.data ?? [] };
};

// Fetch overlays for a specific camera
export function useCameraOverlays(cameraId: string) {
  const url = cameraId ? `${BASE_URL}camera/${cameraId}/` : null;
  const { data, error, mutate } = useSWR<PermanentOverlay[]>(url, fetcher);

  return {
    overlays: data || [],
    isLoading: !error && !data && !!url,
    isError: error,
    mutate
  };
}

// Create overlay
export async function createOverlay(overlayData: Partial<PermanentOverlay>) {
  try {
    const response = await api.post(BASE_URL, overlayData);
    return response.data;
  } catch (error: unknown) {
    console.error('Error creating overlay:', error);
    throw error;
  }
}

// Update overlay
export async function updateOverlay(overlayId: string, updateData: Partial<PermanentOverlay>) {
  try {
    const response = await api.patch(`${BASE_URL}${overlayId}/`, updateData);
    return response.data;
  } catch (error: unknown) {
    console.error('Error updating overlay:', error);
    throw error;
  }
}

// Delete overlay
export async function deleteOverlay(overlayId: string) {
  try {
    const response = await api.delete(`${BASE_URL}${overlayId}/`);
    return response.data;
  } catch (error: unknown) {
    console.error('Error deleting overlay:', error);
    throw error;
  }
}

// Helper function for SWR fetcher
async function fetcher(url: string) {
  try {
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${url}:`, error);
    throw error;
  }
}
