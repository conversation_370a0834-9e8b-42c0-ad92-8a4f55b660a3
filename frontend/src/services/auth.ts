import useSWRImmutable from "swr/immutable";

import { CustomResponse } from "@/types/response";
import { User } from "@/types/user";
import { createFetcher } from "@/services/fetcher";

export const useCurrentUser = () => {
  const url = "/user/public/v1/check-auth";

  const snapshot = useSWRImmutable<CustomResponse<User>>(url, createFetcher, {
    shouldRetryOnError: false,
    revalidateOnMount: true,
  });

  return {
    ...snapshot,
    data: snapshot.data?.data,
  };
};
