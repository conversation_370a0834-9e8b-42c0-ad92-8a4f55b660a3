"use client";

import React, { createContext, ReactNode, useContext } from "react";

import { User } from "@/types/user";

interface AuthType {
  user: User;
}

const Auth = createContext<AuthType | undefined>(undefined);

interface AuthProviderProps {
  user: User;
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({
  user,
  children,
}) => (
  <Auth.Provider
    value={{
      user,
    }}
  >
    {children}
  </Auth.Provider>
);

export const useAuth = (): AuthType => {
  const context = useContext(Auth);

  if (!context) {
    throw new Error("useAuth must be used within a AuthProvider");
  }

  return context;
};
