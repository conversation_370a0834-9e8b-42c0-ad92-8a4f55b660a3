services:
  postgres:
    image: postgres:15
    expose:
      - "5432" # Map external 5433 to internal 5432 for external access
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U squirrelsentry_user -d squirrelsentry_db"]
      interval: 2s
      timeout: 2s
      retries: 3
    
  redis:
    image: squirrelsentry-redis:latest
    container_name: squirrelsentry-redis
    ports:
      - "6379:6379"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "no" ]

  rstp:
    image: squirrelsentry-rstp:latest
    container_name: squirrelsentry-rstp
    restart: unless-stopped
    ports:
      - "8554:8554"
    networks:
      sentry-network:
        ipv4_address: **********

  analytics_camera_one:
    image: squirrelsentry-camera-one-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_one.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
    depends_on:
      - postgres
      - backend
      - rstp
      - model_services
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  model_services:
    image: squirrelsentry-model_services:latest
    container_name: squirrelsentry-model_services
    volumes:
      - ./keys:/keys
    ports:
      - "4010:4010"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    # env_file:
    #   - ./analytics/model_services/.env

  backend:
    image: squirrelsentry-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    working_dir: /usr/src/backend
    expose:
      - "4000"
    command: >
      sh -c "sh ./start_uvicorn_async.sh"
    healthcheck:
      test: ["CMD-SHELL", "curl -f $BACKEND_DOMAIN:4000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./keys:/usr/src/backend/keys
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      - rstp

  nginx:
    image: squirrelsentry-nginx:latest
    container_name: squirrelsentry-nginx
    ports:
      - "4000:4000"
      - "4001:4001"
      - "4002:4002"
      - "4003:4003"
    networks:
      sentry-network:
        ipv4_address: **********
    depends_on:
      - backend

  frontend:
    image: squirrelsentry-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
      # This is required to send the environment variables to the build stage for client side code to access.
      args:
        NEXT_PUBLIC_API_DOMAIN: ${NEXT_PUBLIC_API_DOMAIN}
    working_dir: /app
    ports:
      - "3000:3000"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    env_file:
      - .env
    command: >
      sh -c "npm run start -- -p 3000"

volumes:
  postgres_data: # Persist PostgreSQL data

networks:
  sentry-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16