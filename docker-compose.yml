services:
  postgres:
    image: postgres:15
    expose:
      - "5432" # Map external 5433 to internal 5432 for external access
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U squirrelsentry_user -d squirrelsentry_db"]
      interval: 2s
      timeout: 2s
      retries: 3
    
  redis:
    build:
      context: ./redis
      dockerfile: Dockerfile
    ports:
      - "6379:6379"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: always
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "no" ]

  # rtsp:
  #   build:
  #     context: ./rtsp-server
  #     dockerfile: Dockerfile
  #   container_name: rtsp
  #   restart: always
  #   ports:
  #     - "8554:8554"
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********

  analytics_camera_one:
    image: squirrelsentry-camera-one-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_one.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services
      - redis
    tty: true
    stdin_open: true
    restart: always

  # analytics_camera_two:
  #   image: squirrelsentry-camera-two-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_two.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: always

  model_services:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
    ports:
      - "4010:4010"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: always
    # env_file:
    #   - ./analytics/model_services/.env
    
  batch_processor:
    image: squirrelsentry-batch-processor
    build:
      context: .
      dockerfile: ./analytics/batch_processor/Dockerfile
    environment:
      - BATCH_SIZE=20
      - SLEEP_INTERVAL=1
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: always

  backend:
    image: squirrelsentry-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    working_dir: /usr/src/backend
    expose:
      - "4000"
    command: >
      sh -c "sh ./start_uvicorn_async.sh"
    healthcheck:
      test: ["CMD-SHELL", "curl -f $BACKEND_DOMAIN:4000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./keys:/usr/src/backend/keys
      - ./output_logs_backend:/usr/src/backend/output_logs
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      # - rtsp

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
      - "4001:4001"
      - "4002:4002"
      - "4003:4003"
    networks:
      sentry-network:
        ipv4_address: **********
    depends_on:
      - backend

  frontend:
    image: squirrelsentry-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
      # This is required to send the environment variables to the build stage for client side code to access.
      args:
        NEXT_PUBLIC_API_DOMAIN: ${NEXT_PUBLIC_API_DOMAIN}
    working_dir: /app
    ports:
      - "3000:3000"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: always
    env_file:
      - .env
    command: >
      sh -c "npm run start -- -p 3000"

volumes:
  postgres_data: # Persist PostgreSQL data

networks:
  sentry-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16