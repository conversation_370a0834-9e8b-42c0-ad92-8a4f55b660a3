events {
    worker_connections 1024;
}

# Stream configuration for PostgreSQL
stream {
    upstream postgres_backend {
        server postgres:5432;
    }

    server {
        listen 5432;
        proxy_pass postgres_backend;
    }
}

http {
    upstream backend {
        server backend:4000;
        keepalive 32;
    }

    # upstream model_services {
    #     server model_services_one:4020;
    #     # server model_services_two:4021;
    #     # server model_services_three:4022;
    #     # server model_services_four:4023;
    #     # server model_services_five:4024;
    #     # server model_services_six:4025;
    # }

    # Configuration for port 4000
    server {
        listen 4000 http2;
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Websocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }

    # Configuration for port 4001
    server {
        listen 4001;
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Websocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }

    # Configuration for port 4002
    server {
        listen 4002;
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Websocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }

    # Configuration for port 4003
    server {
        listen 4003;
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Websocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
    }

    # # Model services load balancer configuration
    # server {
    #     listen 4010;
        
    #     location /process_frame {
    #         proxy_pass http://model_services;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Increase buffer size for large frames
    #         proxy_buffer_size 128k;
    #         proxy_buffers 4 256k;
    #         proxy_busy_buffers_size 256k;
            
    #         # Increase max body size for image uploads
    #         client_max_body_size 10M;
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }

    #     location / {
    #         proxy_pass http://model_services;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # # Individual port configurations for model services
    # server {
    #     listen 4020;
    #     location / {
    #         proxy_pass http://model_services_one:4020;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # server {
    #     listen 4021;
    #     location / {
    #         proxy_pass http://model_services_two:4021;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # server {
    #     listen 4022;
    #     location / {
    #         proxy_pass http://model_services_three:4022;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # server {
    #     listen 4023;
    #     location / {
    #         proxy_pass http://model_services_four:4023;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # server {
    #     listen 4024;
    #     location / {
    #         proxy_pass http://model_services_five:4024;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }

    # server {
    #     listen 4025;
    #     location / {
    #         proxy_pass http://model_services_six:4025;
    #         proxy_http_version 1.1;
    #         proxy_set_header Connection "";
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
            
    #         # Websocket support
    #         proxy_set_header Upgrade $http_upgrade;
    #         proxy_set_header Connection "upgrade";
            
    #         # Timeouts
    #         proxy_connect_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_read_timeout 300s;
    #     }
    # }
} 